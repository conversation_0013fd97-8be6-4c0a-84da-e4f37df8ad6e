/*! DataTables Bootstrap 3 integration
 * ©2011-2015 SpryMedia Ltd - datatables.net/license
 */
!function(t){var a,i;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(e){return t(e,window,document)}):"object"==typeof exports?(a=require("jquery"),i=function(e,n){n.fn.dataTable||require("datatables.net")(e,n)},"undefined"==typeof window?module.exports=function(e,n){return e=e||window,n=n||a(e),i(e,n),t(n,0,e.document)}:(i(window,a),module.exports=t(a,window,window.document))):t(jQuery,window,document)}(function(r,e,n){"use strict";var t=r.fn.dataTable;return r.extend(!0,t.defaults,{renderer:"semanticUI"}),r.extend(!0,t.ext.classes,{container:"dt-container dt-semanticUI ui stackable grid",search:{input:"dt-search ui input"},processing:{container:"dt-processing ui segment"},table:"dataTable table unstackable"}),t.ext.renderer.pagingButton.semanticUI=function(e,n,t,a,i){var d=["dt-paging-button","item"],a=(a&&d.push("active"),i&&d.push("disabled"),r("<li>").addClass(d.join(" ")));return{display:a,clicker:r("<"+(i?"div":"a")+">",{href:i?null:"#",class:"page-link"}).html(t).appendTo(a)}},t.ext.renderer.pagingContainer.semanticUI=function(e,n){return r("<div/>").addClass("ui unstackable pagination menu").append(n)},r(n).on("init.dt",function(e,n){"dt"===e.namespace&&(e=new r.fn.dataTable.Api(n),r.fn.dropdown&&r("div.dt-length select",e.table().container()).dropdown(),r("div.dt-search.ui.input",e.table().container()).removeClass("input").addClass("form"),r("div.dt-search input",e.table().container()).wrap('<span class="ui input" />'))}),t.ext.renderer.layout.semanticUI=function(e,n,t){var a=r("<div/>",{class:(t.full,"row")}).appendTo(n);r.each(t,function(e,n){var t="";"start"===e?t+="left floated eight wide column":"end"===e?t+="right floated right aligned eight wide column":"full"===e&&(t+="center aligned sixteen wide column"),r("<div/>",{id:n.id||null,class:t+" "+(n.className||"")}).append(n.contents).appendTo(a)})},t});