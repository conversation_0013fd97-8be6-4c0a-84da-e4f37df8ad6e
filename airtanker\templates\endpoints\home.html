{% extends "base.html" %}

{% block styles %}
  <link
    rel="stylesheet"
    href="{{ url_for('static', filename='css/extra.css') }}"
  />
{% endblock %}

{% block scripts %}
  <script
    src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.4/Chart.min.js"
  ></script>
  <script>
    document.addEventListener("DOMContentLoaded", () => {
      fetch("/api/approvals/timesheets")
        .then(res => res.json())
        .then(data => {
          const labels = data.map(d => d.WeekEnding);

          const metrics = [
            {
              key: "Approval",
              id: "approvals",
              percentLabel: "Approvals Percentage",
              countLabel: "Total Approvals",
              totalKey: "TotalCount",
              countKey: "ApprovedCount"
            },
            {
              key: "Bill",
              id: "bills",
              percentLabel: "Bills Percentage Exported",
              countLabel: "Bills Exported",
              totalKey: "BillTotalCount",
              countKey: "BillCount"
            },
            {
              key: "Invoice",
              id: "invoices",
              percentLabel: "Invoices Percentage Exported",
              countLabel: "Invoices Exported",
              totalKey: "InvoiceTotalCount",
              countKey: "InvoiceCount"
            },
            {
              key: "Timesheet",
              id: "timesheets",
              percentLabel: "Timesheets Percentage Exported",
              countLabel: "Timesheets Exported",
              totalKey: "TimesheetTotalCount",
              countKey: "TimesheetCount"
            }
          ];

          function createChart(ctx, label, data, remaining, isPercent) {
            return new Chart(ctx, {
              type: "horizontalBar",
              data: {
                labels,
                datasets: [
                  {
                    label,
                    backgroundColor: "rgba(220,157,151,1)",
                    borderColor: "rgb(220,157,151)",
                    data,
                    hoverBackgroundColor: "rgba(47,128,237,0.8)",
                    stack: "Stack 0"
                  },
                  {
                    label: "Remaining",
                    backgroundColor: "rgba(200,200,200,0.5)",
                    borderColor: "rgba(200,200,200,1)",
                    data: remaining,
                    hoverBackgroundColor: "rgba(180,180,180,0.8)",
                    stack: "Stack 0"
                  }
                ]
              },
              options: {
                title: { display: true, text: label },
                tooltips: {
                  callbacks: {
                    label: (tip, { datasets }) =>
                      `${datasets[tip.datasetIndex].label}: ` +
                      `${tip.xLabel}${isPercent ? "%" : ""}`
                  }
                },
                legend: { display: false },
                scales: {
                  xAxes: [
                    {
                      ticks: {
                        beginAtZero: true,
                        max: isPercent ? 100 : undefined,
                        callback: v => `${v}${isPercent ? "%" : ""}`
                      },
                      scaleLabel: {
                        display: true,
                        labelString: isPercent ? "Percentage" : "Totals"
                      }
                    }
                  ],
                  yAxes: [
                    {
                      scaleLabel: {
                        display: true,
                        labelString: "Week Ending"
                      }
                    }
                  ]
                },
                responsive: true,
                maintainAspectRatio: false
              }
            });
          }

          metrics.forEach(
            ({ id, percentLabel, countLabel, key, totalKey, countKey }) => {
              const pct = data.map(d => d[key + "Percentage"]);
              const cnt = data.map(d => d[countKey]);
              const tot = data.map(d => d[totalKey]);
              const rem = tot.map((t, i) => t - cnt[i]);

              createChart(
                document
                  .getElementById(`${id}_percentage_chart`)
                  .getContext("2d"),
                percentLabel,
                pct,
                [],
                true
              );
              createChart(
                document
                  .getElementById(`${id}_count_chart`)
                  .getContext("2d"),
                countLabel,
                cnt,
                rem,
                false
              );
            }
          );
        })
        .catch(e => console.error("Fetch error:", e));
    });
  </script>
{% endblock %}

{% block content %}
  {# small macro to emit two canvases side by side #}
  {% macro chart_row(id) %}
    <div class="col chart-container">
      <div class="card p-2">
        <canvas id="{{ id }}_percentage_chart"></canvas>
      </div>
    </div>
    <div class="col chart-container">
      <div class="card p-2">
        <canvas id="{{ id }}_count_chart"></canvas>
      </div>
    </div>
  {% endmacro %}

  <div class="container splash mt-4">
    <div class="row text-center py-4">
      <div class="d-flex flex-column justify-content-start start-screen">
        <h1>Welcome to AirTanker, {{ username }}!</h1>
        <div>
          <a class="btn btn-light" href="/import_work_orders">
            Start Timesheet Wizard
          </a>
          {% if session.get("member_of") == "finances" %}
            <a
              class="btn btn-light"
              href="/import_work_orders_expenses"
            >
              Start Expense Wizard
            </a>
          {% endif %}
        </div>
      </div>
      <div class="row d-flex align-self-end gx-2">
        {{ chart_row("approvals") }}
      </div>
    </div>

    {% if session.get("member_of") == "finances" %}
      {% for id in ["bills", "invoices", "timesheets"] %}
        <div class="row mt-4">
          {{ chart_row(id) }}
        </div>
      {% endfor %}
    {% endif %}
  </div>
{% endblock %}
