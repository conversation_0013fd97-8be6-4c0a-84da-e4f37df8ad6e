#container,
#buttonGroup {
    max-width: 1200px;
    min-width: 320px;
    margin: 1em auto;
}

.hidden {
    display: none;
}

.main-container button {
    font-size: 12px;
    border-radius: 2px;
    border: 0;
    background-color: #ddd;
    padding: 13px 18px;
}

.main-container button[disabled] {
    color: silver;
}

.button-row button {
    display: inline-block;
    margin: 0;
}

.overlay {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0 0 0 / 30%);
    transition: opacity 500ms;
    z-index: 1;
}

.popup {
    margin: 70px auto;
    padding: 20px;
    background: #fff;
    border-radius: 5px;
    width: 300px;
    position: relative;
}

.popup input,
.popup select {
    width: 100%;
    margin: 5px 0 15px;
}

.popup button {
    float: right;
    margin-left: 0.2em;
}

.popup .clear {
    height: 50px;
}

.popup input[type="text"],
.popup select {
    height: 2em;
    font-size: 16px;
}
