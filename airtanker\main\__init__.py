import os
from flask import Flask
from flask_bootstrap import Bootstra<PERSON>
#from flask_wtf.csrf import CSRFProtect
from flask_session import Session
import datetime
import redis
from modules.csrf import csrf
from dotenv import load_dotenv
import subprocess
import platform


# Load environment variables from .env file if present
load_dotenv()

# Determine if the server is in production
is_production = os.getenv('PRODUCTION', 'True').lower() == 'true' 

bootstrap = Bootstrap()
session = Session()

project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Since the 'templates' directory is at the project root, we set it directly
template_folder_path = os.path.join(project_root, 'templates')
base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))

# Construct paths to the output and staging directories
output_dir = os.path.join(base_dir, 'output')
staging_dir = os.path.join(output_dir, 'staging')

# Ensure the output and staging directories exist
directories_created = False
for directory in [output_dir, staging_dir]:
    if not os.path.exists(directory):
        os.makedirs(directory)
        directories_created = True

# Check if the OS is Linux
if directories_created and platform.system() == 'Linux':
# Change ownership and permissions
   for directory in [output_dir, staging_dir]:
       # Change permissions to 777
       subprocess.run(['sudo', 'chmod', '-R', '777', directory], check=True)        

if platform.system() == 'Linux':
    # Linux dictates production environment, in this case. Target the redis instance by the docker container name.
    redis_client = redis.Redis(host='redis', port=6379, db=0)
else:
    # Windows dictates debug environment, in this case. Target the redis instance by localhost and ensure the redis container is running.
    redis_client = redis.Redis(host='127.0.0.1', port=6379, db=0)

airtanker_app = Flask(__name__,
                      template_folder=template_folder_path,
                      root_path=project_root
                      )

# Base configuration
config = {
    'SECRET_KEY': os.urandom(24),
    'SESSION_TYPE': 'filesystem',
    'SESSION_FILE_DIR': os.path.join(base_dir, 'flask_session'),
    'MAX_CONTENT_LENGTH': 1 * 1024 * 1024 * 1024,  # 1 Gigabyte
    'SESSION_PERMANENT': True,
    'PERMANENT_SESSION_LIFETIME': datetime.timedelta(minutes=300),
    'SESSION_REFRESH_EACH_REQUEST': True,
    'WTF_CSRF_ENABLED': False,
    'WTF_CSRF_TIME_LIMIT': None,
    'WTF_CSRF_FIELD_NAME': 'csrf_token'
}

# Add secure cookie settings if in production
if is_production:
    config.update({
        'SESSION_COOKIE_SAMESITE': 'None',
        'SESSION_COOKIE_SECURE': True
    })

# Apply configuration to the Flask app
airtanker_app.config.update(config)

# Add PRODUCTION setting to config for template access
airtanker_app.config['PRODUCTION'] = is_production

## IF Maintenance is enabled
airtanker_app.config['MAINTENANCE_MODE'] = False # Toggle this when done with debugging
airtanker_app.config['ALLOWED_IPS'] = ['************']

bootstrap.init_app(airtanker_app)
# csrf.init_app(airtanker_app)
session.init_app(airtanker_app)

 # Configure session cookies
#csrf = CSRFProtect(airtanker_app)
