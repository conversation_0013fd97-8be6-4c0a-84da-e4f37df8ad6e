import xmlrpc.client
from dotenv import load_dotenv
from nameparser import <PERSON><PERSON><PERSON>
from services.DatabaseService import DatabaseService
import os
import ast
from datetime import datetime, timedelta
import xmlrpc.client
from main import airtanker_app
from datetime import date

class OdooService:
    def __init__(self, password) -> None:
        # Settings for connection
        load_dotenv()
        self.url = os.getenv('ODOO_URL')
        self.db = os.getenv('ODOO_DB')

        self.uid = None
        self.password = password
    

    ###### --- Logins --- ######
    def login(self, username):
        '''Attempts to login user to the database.
        Returns the user's UserID'''

        # Authenticate against Odoo instance
        common = xmlrpc.client.ServerProxy('{}/xmlrpc/2/common'.format(self.url))
        version = common.version()

        ### TODO REMOVE THE "self." BELOW WHEN READY 
        self.uid = common.authenticate(self.db, username, self.password, {})
        airtanker_app.logger.info(f"User attempting to login with Odoo: {username}")

        return self.uid


    ###### --- Work Orders --- ######
    def import_work_orders_to_db(self, selectedDate):
        # Initialize database connection.
        database_service = DatabaseService()
        if not database_service.connect():
            raise Exception("Connection Unsuccessful")
        
        cursor = database_service.connection.cursor()
        work_orders = self.get_work_orders(selectedDate)

        # Initialize error tracking.
        error_log = {
            'skipped_records': [],
            'processing_errors': [],
            'db_errors': []
        }

        # Data collections for each table.
        site_sheets_data = []
        employees_data    = []
        projects_data     = []
        customers_data    = []
        work_orders_data  = []
        python_work_order_ids = []

        # --------------------------------------------
        # First pass: Collect all data from work_orders
        # --------------------------------------------
        for index, work_order in enumerate(work_orders):
            try:
                # Process Employee data.
                employee_field = os.getenv("EMP_INFO_FIELD")
                if employee_field not in work_order or not work_order[employee_field]:
                    wo_id = work_order.get(os.getenv("WO_ID_FIELD"), "Unknown ID")
                    error_log['skipped_records'].append(wo_id)
                    continue

                employeeID = work_order[employee_field][0]
                name = HumanName(work_order[employee_field][1].strip())

                # Process SiteSheets.
                site_sheet_id = [None, None]
                if 'x_studio_site_sheet' in work_order:
                    site_sheet_id = work_order["x_studio_site_sheet"]
                    if not isinstance(site_sheet_id, list) or isinstance(site_sheet_id, bool):
                        site_sheet_id = [None, None]
                    else:
                        site_sheets_data.append((site_sheet_id[0], site_sheet_id[1]))

                # Process Employees.
                employee_type = "Internal" if work_order.get('x_studio_employee_type') == "employee" else None
                employees_data.append((
                    employeeID,
                    name.first,
                    name.middle,
                    name.last,
                    name.full_name,
                    employee_type
                ))

                # Process Projects.
                project_field = os.getenv("PROJECT_INFO_FIELD")
                projectID   = work_order[project_field][0]
                projectName = work_order[project_field][1].strip()
                projects_data.append((projectID, projectName))

                # Process Customers.
                customer_field = os.getenv("CUSTOMER_INFO_FIELD")
                customerID   = work_order[customer_field][0]
                customerName = work_order[customer_field][1].strip()
                customers_data.append((customerID, customerName))

                # Process Work Orders.
                wo_id      = work_order[os.getenv("WO_ID_FIELD")]
                wo_number  = work_order[os.getenv("WO_NUMBER_FIELD")].strip()
                start_date = work_order[os.getenv("START_DATE_FIELD")]
                anticipated_end_date = work_order[os.getenv("END_DATE_FIELD")] or None
                status = compare_dates(
                    anticipated_end_date=anticipated_end_date,
                    selectedDate=selectedDate
                )
                stage_id = work_order['stage_id'][0]
                work_orders_data.append((
                    wo_id, wo_number, status, employeeID, customerID,
                    projectID, start_date, anticipated_end_date, site_sheet_id[0], stage_id
                ))
                python_work_order_ids.append(wo_id)

            except Exception as e:
                error_log['processing_errors'].append(
                    f"#{index} Work order id-{work_order.get(os.getenv('WO_ID_FIELD'), 'Unknown')}: {str(e)}"
                )
                continue

        try:
            # --------------------------------------------
            # Process SiteSheets Block
            # --------------------------------------------
            if site_sheets_data:
                try:
                    cursor.execute("SET IDENTITY_INSERT SiteSheets ON")
                    cursor.executemany("""
                        MERGE INTO [dbo].[SiteSheets] AS target
                        USING (VALUES (?, ?)) AS source (SiteSheetID, SiteSheetName)
                        ON target.SiteSheetID = source.SiteSheetID
                        WHEN MATCHED AND target.SiteSheetName != source.SiteSheetName THEN
                            UPDATE SET SiteSheetName = source.SiteSheetName
                        WHEN NOT MATCHED THEN
                            INSERT (SiteSheetID, SiteSheetName)
                            VALUES (source.SiteSheetID, source.SiteSheetName);
                    """, site_sheets_data)
                    cursor.execute("SET IDENTITY_INSERT SiteSheets OFF")
                    database_service.connection.commit()
                    
                except Exception as e:
                    error_log['db_errors'].append(f"SiteSheets block failed: {str(e)}")
                    raise
                
            # --------------------------------------------
            # Process Employees Block
            # --------------------------------------------
            if employees_data:
                try:
                    cursor.execute("SET IDENTITY_INSERT Employees ON")
                    cursor.executemany("""
                        MERGE INTO [dbo].[Employees] AS target
                        USING (VALUES (?, ?, ?, ?, ?, ?)) AS source
                        (EmployeeID, FirstName, MiddleName, LastName, FullName, Type)
                        ON target.EmployeeID = source.EmployeeID
                        WHEN MATCHED AND (
                            ISNULL(target.FirstName,'') != ISNULL(source.FirstName,'') OR
                            ISNULL(target.MiddleName,'') != ISNULL(source.MiddleName,'') OR
                            ISNULL(target.LastName,'') != ISNULL(source.LastName,'') OR
                            ISNULL(target.FullName,'') != ISNULL(source.FullName,'') OR
                            ISNULL(target.Type,'') != ISNULL(source.Type,'')
                        ) THEN
                            UPDATE SET 
                                FirstName = source.FirstName,
                                MiddleName = source.MiddleName,
                                LastName = source.LastName,
                                FullName = source.FullName,
                                Type = source.Type
                        WHEN NOT MATCHED THEN
                            INSERT (EmployeeID, FirstName, MiddleName, LastName, FullName, Type)
                            VALUES (source.EmployeeID, source.FirstName, source.MiddleName, 
                                    source.LastName, source.FullName, source.Type);
                    """, employees_data)
                    cursor.execute("SET IDENTITY_INSERT Employees OFF")
                    database_service.connection.commit()

                except Exception as e:
                    error_log['db_errors'].append(f"Employees block failed: {str(e)}")
                    raise

            # --------------------------------------------
            # Process Projects Block
            # --------------------------------------------
            if projects_data:
                try:
                    # Deduplicate projects
                    unique_projects = {}
                    for pid, pname in projects_data:
                        unique_projects[pid] = pname
                    deduped_projects = list(unique_projects.items())

                    cursor.execute("SET IDENTITY_INSERT Projects ON")
                    cursor.executemany("""
                        MERGE INTO [dbo].[Projects] AS target
                        USING (VALUES (?, ?)) AS source (ProjectID, ProjectNumber)
                        ON target.ProjectID = source.ProjectID
                        WHEN MATCHED AND target.ProjectNumber != source.ProjectNumber THEN
                            UPDATE SET ProjectNumber = source.ProjectNumber
                        WHEN NOT MATCHED THEN
                            INSERT (ProjectID, ProjectNumber)
                            VALUES (source.ProjectID, source.ProjectNumber);
                    """, deduped_projects)
                    cursor.execute("SET IDENTITY_INSERT Projects OFF")
                    database_service.connection.commit()

                except Exception as e:
                    error_log['db_errors'].append(f"Projects block failed: {str(e)}")
                    raise

            # --------------------------------------------
            # Process Customers Block
            # --------------------------------------------
            if customers_data:
                try:
                    cursor.execute("SET IDENTITY_INSERT Customers ON")
                    cursor.executemany("""
                        MERGE INTO [dbo].[Customers] AS target
                        USING (VALUES (?, ?)) AS source (CustomerID, CustomerName)
                        ON target.CustomerID = source.CustomerID
                        WHEN MATCHED AND target.CustomerName != source.CustomerName THEN
                            UPDATE SET CustomerName = source.CustomerName
                        WHEN NOT MATCHED THEN
                            INSERT (CustomerID, CustomerName)
                            VALUES (source.CustomerID, source.CustomerName);
                    """, customers_data)
                    cursor.execute("SET IDENTITY_INSERT Customers OFF")
                    database_service.connection.commit()

                except Exception as e:
                    error_log['db_errors'].append(f"Customers block failed: {str(e)}")
                    raise

            # --------------------------------------------
            # Process WorkOrders Block
            # --------------------------------------------
            if work_orders_data:
                try:
                    cursor.execute("SET IDENTITY_INSERT WorkOrders ON")
                    cursor.executemany("""
                        MERGE INTO [dbo].[WorkOrders] AS target
                        USING (VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)) AS source 
                        (WorkOrderID, WorkOrderNumber, StatusID, EmployeeID, CustomerID, 
                            ProjectID, StartDate, AnticipatedEndDate, SiteSheetID, StageID)
                        ON target.WorkOrderID = source.WorkOrderID
                        WHEN MATCHED THEN
                            UPDATE SET 
                                WorkOrderNumber = source.WorkOrderNumber,
                                StatusID = source.StatusID,
                                EmployeeID = source.EmployeeID,
                                CustomerID = source.CustomerID,
                                ProjectID = source.ProjectID,
                                StartDate = source.StartDate,
                                AnticipatedEndDate = source.AnticipatedEndDate,
                                SiteSheetID = source.SiteSheetID,
                                StageID = source.StageID,
                                ImportDate = GETDATE()
                        WHEN NOT MATCHED THEN
                            INSERT (WorkOrderID, WorkOrderNumber, StatusID, EmployeeID,
                                    CustomerID, ProjectID, StartDate, AnticipatedEndDate, 
                                    SiteSheetID, StageID, ImportDate)
                            VALUES (source.WorkOrderID, source.WorkOrderNumber, source.StatusID,
                                    source.EmployeeID, source.CustomerID, source.ProjectID,
                                    source.StartDate, source.AnticipatedEndDate, source.SiteSheetID,
                                    source.StageID, GETDATE());
                    """, work_orders_data)
                    cursor.execute("SET IDENTITY_INSERT WorkOrders OFF")
                    database_service.connection.commit()

                except Exception as e:
                    error_log['db_errors'].append(f"WorkOrders block failed: {str(e)}")
                    raise

            # --------------------------------------------
            # Post-commit WorkOrder Status Update
            # --------------------------------------------
            try:
                # Get all currently active work orders from DB
                cursor.execute("SELECT WorkOrderID, WorkOrderNumber FROM WorkOrders WHERE StatusID = 1")
                db_work_orders = {row[0]: row[1] for row in cursor.fetchall()}  # Store both ID and Number
                
                # Find work orders that need to be deactivated
                ids_to_update = set(db_work_orders.keys()) - set(python_work_order_ids)
                
                if ids_to_update:
                    # Update the status
                    placeholders = ', '.join('?' for _ in ids_to_update)
                    update_query = f"""
                        UPDATE WorkOrders 
                        SET 
                            StatusID = 2,
                            ImportDate = GETDATE()
                        WHERE WorkOrderID IN ({placeholders})
                    """
                    cursor.execute(update_query, tuple(ids_to_update))
                    database_service.connection.commit()
                    
            except Exception as e:
                error_msg = f"Failed to update work order statuses: {str(e)}"
                error_log['db_errors'].append(error_msg)
                airtanker_app.logger.error(error_msg)

        except Exception as e:
            error_log['db_errors'].append(f"General processing error: {str(e)}")
            raise

        finally:
            # --------------------------------------------
            # Final Logging and Cleanup
            # --------------------------------------------
            if any(error_log.values()):
                error_summary = self.get_error_summary(error_log, work_orders_data)
                airtanker_app.logger.info(error_summary)
            else:
                airtanker_app.logger.info(
                    f"Successfully processed all {len(work_orders_data)} work orders."
                )

            cursor.close()
            database_service.disconnect()

    def get_error_summary(self, error_log, work_orders_data):
        """
        Generate error summary with grouped errors
        """
        # Group processing errors by error message and count occurrences
        error_groups = {}
        for error in error_log['processing_errors']:
            # Extract error message from the full error string
            error_msg = error.split(': ', 2)[-1]
            error_groups[error_msg] = error_groups.get(error_msg, 0) + 1

        # Simple format with error type counts
        summary = [
            f"Work Order Import completed with the following issues:",
            f"      Skipped records: {len(error_log['skipped_records'])}, due to missing employee_id",
            "       Processing errors by type:" if error_groups else None,
            *[f"  - ({count} records): {error}" for error, count in error_groups.items()],
            "       Database errors:" if error_log['db_errors'] else None,
            *[f"  - {err}" for err in error_log['db_errors']],
            f"      Successfully processed: {len(work_orders_data)}"
        ]
        
        return '\n'.join([line for line in summary if line is not None])

    def get_work_orders(self, selectedDate_dt):
        '''Gets the work orders from Odoo to
        store into the database.'''
        # Convert the selectedDate string to a datetime object for manipulation
        #selectedDate_dt = datetime.strptime(selectedDate, '%Y-%m-%d')
        # Calculate the date for "selectedDate - 7 days"
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))

        date_minus_7_days_dt = selectedDate_dt - timedelta(days=7)
        # Convert dates back to string format expected by Odoo
        selectedDate_str = selectedDate_dt.strftime('%Y-%m-%d')
        date_minus_7_days_str = date_minus_7_days_dt.strftime('%Y-%m-%d')

       # Establish Odoo object repository        

        # Search filtered active (In Progress) tasks
        stage_id_field = os.getenv("STAGE_ID_FIELD")
        start_date_field = os.getenv("START_DATE_FIELD")
        end_date_field = os.getenv("END_DATE_FIELD")


        # TODO comment this out when we figure out end date situation, do the same in excel service.
        taskIDs = models.execute_kw(self.db, self.uid, self.password, 'project.task', 'search', [
            [
                # '|',  # OR
                # '&',  # AND
                # (stage_id_field, '=', 187), 
                # (start_date_field, '<=', selectedDate_str),  
                # '|',  # OR
                # '&',  # AND
                # (stage_id_field, '!=', 187),  
                # (end_date_field, '>=', date_minus_7_days_str),  
                # (end_date_field, '=', False)  # This is now part of the OR group correctly
            ]
        ])
        
        fields_str = os.getenv("ODOO_FIELDS")
        fields = ast.literal_eval(fields_str)

        tasks = models.execute_kw(self.db, self.uid, self.password, 'project.task', 'read', [taskIDs], 
                         {'fields': fields })
        for task in tasks:
            if "KUKA-096" in task['name']:
                pass

        return tasks

    def get_work_orders_for_talent_desk_by_ids(self, work_order_ids):
        fields_str = os.getenv("ODOO_FIELDS")
        fields = ast.literal_eval(fields_str)
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        tasks = models.execute_kw(self.db, self.uid, self.password, 'project.task', 'read', [work_order_ids], 
                         {'fields': fields })
        return tasks

    ###### --- Expenses --- ######
    def get_expense_details_by_id(self, expense_ids):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        model_name = 'x_approved_expenses'  # Replace with the actual model name for expenses
        # Get all fields of the expense model
        expense_fields = ['id', 'display_name', 'x_lodging', 'x_perdiem',
                          'x_misc_description', 'x_mileage', 'x_mileage_rate',
                          'x_travel_time', 'x_misc', 'x_lodging_clarification',
                          'x_vehicle_clarification', 'x_travel_time_clarification',
                          'x_airfare_clarification', 'x_name', 'x_airfare_rule',
                          'x_vehicle_rule', 'x_lodging_rule', 'x_travel_time_rule',
                          'x_airfare', 'x_vehicle', 'x_site_sheet_id', 'x_currency_id']
        
        # Read the expense record by ID
        expenses = models.execute_kw(self.db, self.uid, self.password, model_name, 'read', [expense_ids, expense_fields])
        
        if not expenses:
            return None
        
        return expenses

    def get_expenses_for_site_sheet(self, site_sheet_id):
        site_sheet = self.get_site_sheet_by_id(site_sheet_id)
        if not site_sheet:
            return
        
        expense_fields = ['x_arrival_expense', 'x_working_expense', 'x_departure_expense']

        expense_data = {}
    
        for field in expense_fields:
            if field in site_sheet[0] and site_sheet[0][field]:
                expense_id = site_sheet[0][field][0]
                expense_details = self.get_expense_details_by_id([expense_id])
                if expense_details:
                    expense_data[field] = expense_details
        return expense_data

    def get_site_sheet_fields(self):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        model_name = 'x_site_sheet'
        fields = models.execute_kw(self.db, self.uid, self.password, model_name, 'fields_get', [], {'attributes': ['string', 'help', 'type']})
                      
        return fields


    def get_site_sheet_by_id(self, record_id):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        model_name = 'x_site_sheet'
        # First, get all fields
        field_list = ['id', 'display_name', 'x_name', 'x_project_id', 'x_arrival_expense', 'x_working_expense', 'x_departure_expense']
        
        # Now, read the specific record by ID using all fields
        record = models.execute_kw(self.db, self.uid, self.password, model_name, 'read', [[record_id], field_list])
        
        # Print the detailed record information
        if record:
            return record
        else:
            return None
    


    ###### --- Invoices -- ######
    def create_to_ATS_invoice_id(self, week_ending, customerPO, customerID=1323):
        week_ending_subtract = 6
        week_starting = week_ending - timedelta(days=week_ending_subtract)
        week_ending_str = week_ending.strftime('%Y-%m-%d')
        # Define some static lookup IDs
        companyID = 3 # Services
        invoice_payment_term_id = 4 # Net 30

        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        # Create the Invoice. I believe these are the only required fields so far.
        wInvoiceID = models.execute_kw(self.db, self.uid, self.password, 'account.move', 'create', [
            {'x_studio_service_date': week_ending_str,
            'move_type':'out_invoice',
            'company_id': companyID,
            'partner_id': customerID,
            'invoice_payment_term_id': invoice_payment_term_id,
            'x_studio_message_on_invoice': customerPO + '\nStart Date:' + week_starting.strftime("%m/%d/%Y") + '\nEnd Date:' + week_ending.strftime("%m/%d/%Y")}])
        
        return wInvoiceID
        
    
    def add_line_item_to_invoice(self, line_item_name, hours, rate, wInvoiceID, accountID):
        currencyID = 2 # USD
        productID = 35 # Engineering Services
        uomID = 6 # Hours        
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        models.execute_kw(self.db, self.uid, self.password, 'account.move.line', 'create', [
                {'name': line_item_name,
                'move_id':wInvoiceID,
                'analytic_distribution': {str(accountID[0]): 100.0}, # 100% distribution into the account ID defined earlier
                'quantity': float(hours), # Calculated Approved Hours
                'product_id': productID,
                'product_uom_id': uomID,
                'price_unit': rate}])
        

    def get_sale_order_by_id(self, saleOrderID):   
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))     
        saleOrders = models.execute_kw(self.db, self.uid, self.password, 'sale.order', 'read', [saleOrderID], [])
        return saleOrders
    

    def get_customer_by_id(self, customer_id):
        # Define the model name and the domain to filter customers
        model_name = 'res.partner'
        domain = [('customer_rank', '>', 0)]  # Filtering for customers
        fields = [
            'id', 'name', 'email', 'phone', 'street', 'street2', 'city', 'state_id', 'zip', 'country_id'
        ]  # Fields to fetch    
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        
        # Read customer details using the fetched IDs
        customers = models.execute_kw(self.db, self.uid, self.password, model_name, 'read', [[customer_id], fields])
        
        return customers

    def get_invoices_by_week(self, week_ending):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))

        invoice_filter = [[('x_studio_service_date', '=', week_ending), ('move_type', '=', 'out_invoice'), ('state', '=', 'posted')]]
        invoice_fields = ['id', 'name', 'state', 'amount_total', 'line_ids', 'partner_id']
        invoices = models.execute_kw(self.db, self.uid, self.password, 'account.move', 'search_read', invoice_filter, {'fields': invoice_fields})

        invoice_line_ids = []
        for invoice in invoices:
            invoice_line_ids.extend(invoice['line_ids'])
        invoice_lines = models.execute_kw(self.db, self.uid, self.password, 'account.move.line', 'read', [invoice_line_ids], 
                                          {'fields': ['id', 'name', 'move_id', 'analytic_distribution', 'quantity', 'price_unit', 'price_total']})
        
        for invoice in invoices:
            lines = [line for line in invoice_lines if line['move_id'][0] == invoice['id']]
            invoice['lines'] = lines

        return invoices
        

    def get_related_invoices(self, project_id, week_ending):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        found_invoices = []
        week_ending_filter = []
        week_ending_filter.append(('x_studio_service_date', '=', week_ending))
        week_ending_filter.append(('move_type', '=', 'out_invoice'))
        invoice_ids = models.execute_kw(self.db, self.uid, self.password, 'account.move', 'search', [week_ending_filter])
        for invoice_id in invoice_ids:
            # invoice = 

            invoice_line_filter = []
            invoice_line_filter.append(('move_id', '=', invoice_id))
            # invoice_line_filter.append(('analytic_distribution', 'like', str(project_id[0])))

            analytic_line_ids = models.execute_kw(self.db, self.uid, self.password, 'account.move.line', 'search', [invoice_line_filter])
            analytic_lines = models.execute_kw(self.db, self.uid, self.password, 'account.move.line', 'read', [analytic_line_ids], {'fields': ['id', 'name', 'analytic_distribution', 'quantity', 'price_unit', 'price_total']})
            for line in analytic_lines:
                if line['analytic_distribution'] and str(project_id[0]) in line['analytic_distribution']:
                    invoices = models.execute_kw(self.db, self.uid, self.password, 'account.move', 'read', [[invoice_id]],
                                                {'fields': ['id', 'name', 'state', 'amount_total']})
                    if invoices:
                        invoices[0]['lines'] = analytic_lines
                        found_invoices.append(invoices[0])
                    else:
                        airtanker_app.logger.error(f'Invoice {invoice_id} cannot be read from Odoo!')
                    break
            pass
        return found_invoices

    def get_account_moves(self, move_ids):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        move_ids_list = list(move_ids) 
        return models.execute_kw(self.db, self.uid, self.password, 'account.move', 'read', [move_ids_list])

    def create_invoice_bill(self, week_ending_str, customerID, customerPO):
        # Convert the string to a datetime object
        week_ending = datetime.strptime(week_ending_str, '%Y%m%d')
        week_ending_subtract = 6
        week_starting = week_ending - timedelta(days=week_ending_subtract)
        
        # Format the dates
        week_ending_formatted = week_ending.strftime('%Y-%m-%d')
        week_starting_formatted = week_starting.strftime("%m/%d/%Y")
        week_ending_formatted_display = week_ending.strftime("%m/%d/%Y")

        # Define some static lookup IDs
        companyID = 3 # Services
        invoice_payment_term_id = 4 # Net 30

        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        # Create the Invoice. I believe these are the only required fields so far.
        wInvoiceID = models.execute_kw(self.db, self.uid, self.password, 'account.move', 'create', [
            {'x_studio_service_date': week_ending_formatted,
            'move_type': 'out_invoice',
            'company_id': companyID,
            'partner_id': customerID,
            'invoice_payment_term_id': invoice_payment_term_id,
            'x_studio_message_on_invoice': f"{customerPO}\nStart Date: {week_starting_formatted}\nEnd Date: {week_ending_formatted_display}"}])
        
        return wInvoiceID

    

    ###### --- Other Fields --- ######
    def get_parent_code_field(self, parent_id):        
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        domain = [['id', '=', parent_id]]
        fields = ['id', 'x_studio_code']
        parents = models.execute_kw(self.db, self.uid, self.password,
                                    'res.partner', 'search_read',
                                    [domain], {'fields': fields})
        return parents[0]['x_studio_code'] if parents else None


    def get_initials_from_contact_id(self, contact_id):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))

        # Fetch the employee's name based on the contact ID
        name = models.execute_kw(self.db, self.uid, self.password,
                                    'res.partner', 'read', [contact_id], {'fields': ['name']})

        if not name:
            return None

        full_name = name[0].get('name')

        if not full_name:
            return None

        # Check if the full name looks like an email address
        if '@' in full_name:
            # Split the email address to get the initials
            local_part = full_name.split('@')[0]
            email_parts = local_part.split('.')
            initials = ''.join([part[0].upper() for part in email_parts if part])
        else:
            # Split the full name into parts
            name_parts = full_name.split()
            # Extract initials from the name parts
            initials = ''.join([part[0].upper() for part in name_parts if part])

        return initials
    

    def get_all_subcontractor_contacts(self):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        # Define the search domain to find contacts whose parent has the "Subcontractor" tag
        #domain = [['parent_id.category_id.name', '=', 'Subcontractor']]
        domain = []
        # Include 'parent_id' in the fields list to fetch the parent ID
        fields = ['id', 'display_name', 'parent_id', 'parent_name','company_name','company_id','x_studio_customer_id']

        contacts = models.execute_kw(self.db, self.uid, self.password, 
                                    'res.partner', 'search_read', 
                                    [domain], {'fields': fields})

        return contacts


    def create_timesheet(self, description, date, hours, project_id, employee_id, work_order_id):        

        date_datetime = datetime.strptime(date, '%m/%d/%Y')
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        timesheet_data = {
            'name': description,  # Name of timesheet its: WO_Name - Contractor Name ("Double Time", "Overtime", "")
            'date': date_datetime.strftime('%Y-%m-%d'),  # Ensure the format is correct
            'unit_amount': hours,
            'user_id': False,
            'employee_id': employee_id,  # TODO Make Actual EmployeeID
            'project_id': project_id,
        }

        if work_order_id is not None:
            timesheet_data['task_id'] = work_order_id  # TODO Make WorkOrderID
        
        timesheet_id = models.execute_kw(self.db, self.uid, self.password, 'account.analytic.line', 'create', [timesheet_data])
        
        return timesheet_id

    def create_timesheets_batch(self, entries):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        batch_data = []
        
        for entry in entries:
            date_datetime = datetime.strptime(entry['Date'], '%m/%d/%Y')
            timesheet_data = {
                'name': f"AirTanker {entry['Description']}",
                'date': date_datetime.strftime('%Y-%m-%d'),
                'unit_amount': entry['Hours'],
                'employee_id': entry['ContractorID'],
                'project_id': entry['ProjectID'],
                'task_id': entry.get('TaskID', False)
            }
            batch_data.append(timesheet_data)
        
        try:
            return models.execute_kw(
                self.db, self.uid, self.password,
                'account.analytic.line', 'create',
                [batch_data]
            )
        except xmlrpc.client.Fault as e:
            error_msg = f"OdooService error in create_timesheets_batch: faultCode: {str(e.faultCode)}, faultString: {str(e.faultString)}" # Cannot use e, because of the < and > in the string.
            # airtanker_app.logger.error(error_msg) # Better practice to log in the calling function.
            raise Exception(error_msg) from e # Preserve original traceback

    
    def get_all_tasks(self, project=None, task_id=None, stages=None):
        """
            Why bring all the data?
            Lets filter by just the stages needed:
                210 => Wo - Dashboard
                187 => WO - In Progress
                 => WO - Completed
        """
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        filter = []

        if stages:
            filter.append(('stage_id', 'in', stages))

        if task_id:
            filter.append(('id', '=', int(task_id)))
            
        else:
            if isinstance(project, int):
                filter.append(('project_id', '=', project))
            elif project:
                filter.append(('project_id', '=', project['id']))               

        tasks = models.execute_kw(self.db, self.uid, self.password, 'project.task', 'search_read', [filter], {'fields': ['id', 'name', 'project_id']})

        return tasks
    


    ###### --- Employees --- ######
    def get_all_employees(self):        
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        employee_ids = models.execute_kw(self.db, self.uid, self.password, 'hr.employee', 'search', [[]])
        employees = models.execute_kw(self.db, self.uid, self.password, 'hr.employee', 'read', [employee_ids, ['id', 'name', 'barcode']])

        return employees
    

    def get_employee(self, employee_id):
        employee = None
        try:
            models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
            employee = models.execute_kw(self.db, self.uid, self.password, 'hr.employee', 'read', [employee_id, ['id', 'name', 'x_studio_customer_id']])
        except:
            print('Error getting employee')
        return employee
    

    def update_work_order_sow_partner(self, employee_id, selected_contact):        
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        # Assuming selected_contact is a dictionary like {'id': 123, 'name': 'Subcontractor A'}
        # Extract the ID of the selected contact
        # Prepare the update values
        # Assuming x_studio_sow_partner expects just the ID of the contact
        update_values = {'display_name': selected_contact['name'],
                         os.getenv("SOW_CONTACT"): selected_contact['id']}
        
        success = models.execute_kw(self.db, self.uid, self.password, 
                                    'hr.employee', 'write', 
                                    [[employee_id], update_values])

        return success
    


    def get_all_projects(self, id=None):        
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        # Step 1: Get all projects
        projects = None
        if id:
            projects = models.execute_kw(self.db, self.uid, self.password, 'project.project', 'read', [[int(id)], ['id', 'name']])
        else:
            project_ids = models.execute_kw(self.db, self.uid, self.password, 'project.project', 'search', [[]])
            projects = models.execute_kw(self.db, self.uid, self.password, 'project.project', 'read', [project_ids, ['id', 'name']])

        return projects
    


    ###### --- Bills --- ######
    def create_bill_return_id(self, sow_contact_id, bill_name, week_ending_str):        
        companyID = 3 # Services
        week_ending_date = datetime.strptime(week_ending_str, '%Y%m%d').strftime('%Y-%m-%d')
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))

        week_ending = datetime.strptime(week_ending_str, '%Y%m%d')
        week_ending_subtract = 6
        week_starting = week_ending - timedelta(days=week_ending_subtract)
        
        # Format the dates
        week_starting_formatted = week_starting.strftime("%m/%d/%Y")
        week_ending_formatted_display = week_ending.strftime("%m/%d/%Y")
        
        bill_data = {
            'name': bill_name,
            'move_type': 'in_invoice',
            'company_id': companyID,
            'invoice_date': week_ending_date,
            'x_studio_service_date': week_ending_date,
            'create_date': week_ending_date,
            'date': week_ending_date,
            'x_studio_message_on_invoice': f"Start Date: {week_starting_formatted}\nEnd Date: {week_ending_formatted_display}"
        }
        
        if isinstance(sow_contact_id, int):
            bill_data['partner_id'] = sow_contact_id
        
        writeBillID = models.execute_kw(self.db, self.uid, self.password, 'account.move', 'create', [bill_data])
        
        return writeBillID

    def add_line_item_to_bill(self, writeBillID, line_item_name, hours, rate, accountID):
        currencyID = 2 # USD
        productID = 35 # Engineering Services
        uomID = 6 # Hours        
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        # name key-value was  'W.E ******** - Overtime'
        stLineID = models.execute_kw(self.db, self.uid, self.password, 'account.move.line', 'create', [
            {'name': line_item_name,
            'move_id':writeBillID,
            'quantity': float(hours),
            'product_id': productID,
            'product_uom_id': uomID,
            'analytic_distribution': {str(accountID): 100.0}, # 100% distribution into the account ID defined earlier
            'price_unit': float(rate)}])
        
    def add_line_item_expense(self, writeBillID, line_item_name, expense_total, accountID):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))

        productID = [254, 'Flight']
        productID = [340, 'Lodging']
        productID = [256, 'Mileage']
        productID = [257, 'Per Diem']
        productID = [258, 'Transport']
        productID = [413, 'Expense Flight']
        productID = [40, 'Reimbursements']

        productID = 35 # Engineering Services ----> Replace with actual product ID from above.

        productID = 285 #, 'All'] # --->> USING ALL FOR PRODUCT FOR NOW

        uomID = [23, 'Unit(s)']
        uomID = [72, 'Hours']
        uomID = [83, 'Unit']
        

        uomID = 6 # Hours ----> Replace with actual uomID from above.

        uomID = 1 #, 'Unit'] # ---->> USING UNIT FOR EXPENSES FOR NOW

        models.execute_kw(self.db, self.uid, self.password, 'account.move.line', 'create', [
            {
                'name': line_item_name,
                'move_id': writeBillID,
                #'analytic_distribution': {str(accountID): 100.0},  # BLOCKED OUT FOR NOW
                'quantity': 1,  # Typically 1 for expense items unless otherwise specified
                'product_id': productID,
                'product_uom_id': uomID,
                'price_unit': expense_total  # Total amount of the expense
            }
        ])

    def add_line_item_name(self, writeBillID, contractor_name):        
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        # name key-value was  'W.E ******** - Overtime'
        stLineID = models.execute_kw(self.db, self.uid, self.password, 'account.move.line', 'create', [
            {'name': contractor_name,
            'move_id':writeBillID,
            'display_type':'line_section'}])
        
    def create_invoice_lines_in_bulk(self, lines_data):
        """
        Create multiple invoice lines in a single request
        
        Args:
            lines_data (list): List of dictionaries containing line item data
            
        Returns:
            list: List of created line IDs
        """
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        try:
            return models.execute_kw(self.db, self.uid, self.password, 'account.move.line', 'create', [lines_data])
        except xmlrpc.client.Fault as e:
            error_msg = f"OdooService error in create_invoice_lines_in_bulk: faultCode: {str(e.faultCode)}, faultString: {str(e.faultString)}"
            raise Exception(error_msg) from e

## HELPERS

    def convert_to_datetime(self, date_str):
        # Try different formats and convert to datetime object
        for fmt in ("%Y-%m-%d", "%Y%m%d", "%d-%m-%Y", "%d/%m/%Y"):
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue
        raise ValueError(f"Date string {date_str} does not match any expected format")

def compare_dates(anticipated_end_date, selectedDate):
    try:
        comparison_date = selectedDate - timedelta(days=7) 
        if anticipated_end_date: # if value
            end_date = datetime.strptime(anticipated_end_date, '%Y-%m-%d').date()
            if end_date <= comparison_date: # check if the work order ended before week start
                return 2 # if end date is before today, then it's completed.
        return 1 # if anticipated_end_date is None, it's still active.
    except ValueError:
        airtanker_app.logger.error('Unknown end date: %s', anticipated_end_date)
        return 2