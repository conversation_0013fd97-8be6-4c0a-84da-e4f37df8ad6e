from main import airtanker_app, redis_client
import time
from flask import Response, stream_with_context


def update_progress(task_id, progress):
    # progress should be a percentage or some other metric that indicates how far along the task is
    try:
        redis_client.set(f'progress_{task_id}', progress)
    except Exception as e:
        airtanker_app.logger.error('Error Updating Progress: %s', str(e))


def get_progress(task_id):
    # Returns the progress of a specific task
    progress = redis_client.get(f'progress_{task_id}')
    return progress.decode('utf-8') if progress is not None else 0


def is_float(value):
    try:
        float(value)
        return True
    except ValueError:
        return False

@airtanker_app.route('/progress/<task_id>')
def progress(task_id):
    def generate():
        progress = 0
        continue_looping = True
        while continue_looping:
            progress = get_progress(task_id=task_id)
            yield f"data:{progress}\n\n"
            if progress in ["Completed", "Failed"] or (is_float(progress) and float(progress) > 99.0):
                continue_looping = False
                break
            time.sleep(0.5)
    response = Response(stream_with_context(generate()), mimetype='text/event-stream')
    response.headers['Cache-Control'] = 'no-cache'
    response.headers['Connection'] = 'keep-alive'
    return response
