<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" href="{{ url_for('static', filename='assets/favicon-32x32.png') }}" type="image/png">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css"
          integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <link href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" rel="stylesheet">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" rel="stylesheet">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.7.1/css/buttons.dataTables.min.css">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/styles_customer.css') }}"  />
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <style>
        .flash-message {
            color: white;
            background-color: rgb(216, 49, 49);
            padding: 10px;
            margin: 0 auto;
            border-radius: 5px;
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            display: none; /* Initially not displayed */
            opacity: 0; /* Start fully transparent */
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }
        body {
        font-family: "Helvetica Neue", Helvetica, Arial;
        font-size: 14px;
        line-height: 20px;
        font-weight: 400;
        color: #3b3b3b;
        -webkit-font-smoothing: antialiased;
        background: #2b2b2b;
        }
        @media screen and (max-width: 580px) {
        body {
            font-size: 16px;
            line-height: 22px;
        }
        }

        .wrapper {
        margin: 0 auto;
        padding: 40px;
        max-width: 100%;
        }

        .table {
        margin: 0 0 0px 0;
        width: 100%;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        display: table;
        }
        @media screen and (max-width: 580px) {
        .table {
            display: block;
        }
        }

        .row {
        display: table-row;
        background: #f6f6f6;
        }
        .row:nth-of-type(odd) {
        background: #e9e9e9;
        }
        .row.header {
        font-weight: 900;
        color: #ffffff;
        background: #ea6153;
        }
        .row.green {
        background: #27ae60;
        }
        .row.blue {
        background: #2980b9;
        }
        @media screen and (max-width: 580px) {
        .row {
            padding: 14px 0 7px;
            display: block;
        }
        .row.header {
            padding: 0;
            height: 10px;
        }
        .row.header .cell {
            display: none;
        }
        .row .cell {
            margin-bottom: 10px;
        }
        .row .cell:before {
            margin-bottom: 3px;
            content: attr(data-title);
            min-width: 98px;
            font-size: 10px;
            line-height: 10px;
            font-weight: bold;
            text-transform: uppercase;
            color: #969696;
            display: block;
        }
        }

        .cell {
        padding: 6px 12px;
        display: table-cell;
        }
        @media screen and (max-width: 580px) {
        .cell {
            padding: 2px 16px;
            display: block;
        }
        }

        .grayed-out {
            background-color: #e7e7e7; /* Light gray */
            color: #969696; /* Darker text color */
        }
        .hidden {
            display: none;
        }

    </style>
</head>

<br>
<body class="bg-gradient-white">
    <!-- Place this within the body of your HTML template -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        {% from "includes/_formhelpers.html" import render_field %}
        <div class="wrapper fadeIn">

            <div style="text-align: center; font-size: large;">
                <h2 style="font-size: larger;">Errors Found in Internal TimeSheets<br><small>Please review the details below</small></h2>
            </div>
                
            <div class="wrapper">
                <div class="table" id="errorsTable">
                    <div class="row header">
                        <div class="cell">Employee</div>
                        <div class="cell">Message</div>
                        <div class="cell">FileName</div>
                        <div class="cell">Dates</div>
                        <div class="cell">Select Work Order</div>
                        <!-- <div class="cell">Select Task</div> -->
                        <div class="cell" style="text-align: center;">Skip Update?</div>
                    </div>
                    <!-- Dynamic error rows will be inserted here -->
                </div>
                <div id="formFooter" style="width: 100%;">
                    <a class="underlineHover fadeIn third" href="#" onclick="handleAction(true); return false;">Cancel</a>
                </div>
                <input type="submit" value="Continue" class="btn underlineHover" onclick="submitUpdates();">
            </div>
        </div>
</body>
</html>                            


<script src="https://code.jquery.com/jquery-3.2.1.slim.min.js"
        integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN"
        crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
        integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q"
        crossorigin="anonymous"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"
        integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl"
        crossorigin="anonymous"></script>
<script>
    
    document.addEventListener("DOMContentLoaded", function() {
        var flashMessages = document.querySelectorAll('.flash-message');
        flashMessages.forEach(function(flashMessage) {
            // Show the flash message immediately with a fade-in effect
            flashMessage.style.display = 'block';
            flashMessage.style.animation = 'fadeIn 1s forwards';
    
            // After the fade-in, plus the duration of visibility, start the fade-out
            setTimeout(function() {
                flashMessage.style.animation = 'fadeOut 2s forwards';
    
                // Wait for the fade-out animation to complete before setting display to none
                setTimeout(function() {
                    flashMessage.style.display = 'none';
                }, 2000); // Duration of the fadeOut animation
            }, 4000); // 1s for fadeIn to complete + 3s visible = 4s total before fadeOut begins
        });
    });

    
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
        }).replace(/\//g, '-');
    }


    function isEmpty(obj) {
        return Object.keys(obj).length === 0;
    }


    // Define the specific messages to track
    const specificMessages = [
        "This file has already been processed. Delete the sheet at '/edit-files' to re-parse.",
        "This file had formulas in cell where actual hours were expected. Please correct this and reupload.",
        "These files had 'Your Name' in the name field. Please correct this and reupload.",
        "Database doesn't contain timesheet hours for these employees! Please upload their internal timesheets first.",
        "These files had the name field not filled out. Expecting name in 'F4'. Please correct this and reupload."
    ];

    // Object to store references to row indices for specific messages
    let messageToRowMap = {};


    document.addEventListener('DOMContentLoaded', function() {
        const errorsContainer = document.getElementById('errorsTable');

        // Assuming the errors are stored as a JSON string in sessionStorage
        const errorsJSON = sessionStorage.getItem('errors');
        const errors = JSON.parse(errorsJSON) || [];        

        // Assuming the errors are stored as a JSON string in sessionStorage
        const name_errorsJSON = sessionStorage.getItem('name_errors');
        const name_errors = JSON.parse(name_errorsJSON) || [];

        errors.forEach(function(error) {
            if (specificMessages.includes(error.Message)) {
                if (messageToRowMap.hasOwnProperty(error.Message)) {
                    // Append filename to the existing row
                    const existingRow = errorsContainer.children[messageToRowMap[error.Message]];
                    const fileNameCell = existingRow.querySelector('.file-name-cell');
                    const newFileName = document.createElement('div'); // create a new div for each filename
                    newFileName.textContent = error.FileName;
                    fileNameCell.appendChild(newFileName);
                } else {
                    // Create a new row and store its index
                    const rowIndex = createErrorRow(error, errorsContainer);
                    messageToRowMap[error.Message] = rowIndex;
                }
            } else {
                // Create a new row normally
                createErrorRow(error, errorsContainer);
            }
        });

        name_errors.forEach(function(name_error) {
            if (specificMessages.includes(name_error.Message)) {
                if (messageToRowMap.hasOwnProperty(name_error.Message)) {
                    // Append filename to the existing row
                    const existingRow = errorsContainer.children[messageToRowMap[name_error.Message]];
                    const fileNameCell = existingRow.querySelector('.file-name-cell');
                    const newFileName = document.createElement('div'); // create a new div for each filename
                    newFileName.textContent = name_error.FileName;
                    fileNameCell.appendChild(newFileName);
                } else {
                    // Create a new row and store its index
                    const rowIndex = createNameErrorRow(name_error, errorsContainer);
                    messageToRowMap[name_error.Message] = rowIndex;
                }
            } else {
                // Create a new row normally
                createNameErrorRow(name_error, errorsContainer);
            }
        });
    });

    function createNameErrorRow(name_error, errorsContainer){
        // Create a new row for the name_error
        const row = document.createElement('div');
        row.className = 'row fadeIn';
        row.setAttribute('data-id', name_error.ID); // Set the ID as a data attribute

        // Create a dropdown cell - EMPLOYEE
        const employee_dropdownCell = document.createElement('div');
        employee_dropdownCell.className = 'cell';
        const employee_select = document.createElement('select')
        employee_select.className = 'employee_dropdown';
        const optgroup = document.createElement('optgroup');
        name_error.EmployeeData.forEach(emp_data => {
            const option = document.createElement('option');
            option.textContent = emp_data.EmployeeName;                
            // Store the WorkOrders as a stringified JSON in a data attribute
            option.setAttribute('data-workorders', JSON.stringify(emp_data.WorkOrders));
            employee_select.appendChild(option);
        });
        employee_select.appendChild(optgroup);
        
        employee_dropdownCell.appendChild(employee_select);
        
        // Create cell for Message
        const messageCell = document.createElement('div');
        messageCell.className = 'cell';
        messageCell.innerHTML = name_error.Message;
        
        // Create cell for FileName
        const fileNameCell = document.createElement('div');
        fileNameCell.className = 'cell file-name-cell';
        const initialFileName = document.createElement('div'); // create a new div for the first filename
        initialFileName.textContent = name_error.FileName;
        fileNameCell.appendChild(initialFileName);
        
        // Create cell for Dates (optional if you want to display dates)
        const datesCell = document.createElement('div');
        datesCell.className = 'cell';
        //datesCell.textContent = name_error.Hours.map(hour => formatDate(hour.Date)).join(', ');
        
        // Create a dropdown cell
        
        
        const dropdownCell = document.createElement('div');
        dropdownCell.className = 'cell';
        const select = document.createElement('select');
        select.className = 'workOrderDropdown';
        select.id = "workOrderSelect"
        
        dropdownCell.appendChild(select);

        row.appendChild(employee_dropdownCell);
        row.appendChild(messageCell);
        row.appendChild(fileNameCell);
        row.appendChild(datesCell);
        row.appendChild(dropdownCell);


        // Create a cell for the Toggle Button
        const toggleCell = document.createElement('div');
        toggleCell.className = 'cell';
        toggleCell.style = "text-align: center;";
        const toggleButton = document.createElement('button');
        
        toggleButton.addEventListener('click', function() {
            toggleButton.textContent = toggleButton.textContent === 'No' ? 'Yes' : 'No';

            row.classList.toggle('grayed-out'); // Toggle the grayed-out class for the row
            
            const dropdown = row.querySelector('.workOrderDropdown');
            if (dropdown) {
                // Toggle the hidden class to show or hide the dropdown
                dropdown.classList.toggle('hidden');
            }
        });

        
        toggleButton.textContent = 'No'; // Customize this text or use an icon
        toggleCell.appendChild(toggleButton);
        row.appendChild(toggleCell);


        // Add the new row to the errorsContainer
        errorsContainer.appendChild(row);
        return errorsContainer.children.length - 1; // Return index of the newly added row
    }


    function createErrorRow(error, errorsContainer){
        // Create a new row for the error
        const row = document.createElement('div');
        row.className = 'row fadeIn';
        row.setAttribute('data-id', error.ID); // Set the ID as a data attribute

        // Create cell for Employee
        const employeeCell = document.createElement('div');
        employeeCell.className = 'cell';
        employeeCell.textContent = error.Employee;
        
        // Create cell for Message
        const messageCell = document.createElement('div');
        messageCell.className = 'cell';
        messageCell.innerHTML = error.Message;

        // Create cell for FileName
        const fileNameCell = document.createElement('div');
        fileNameCell.className = 'cell file-name-cell';
        const initialFileName = document.createElement('div'); // create a new div for the first filename
        initialFileName.textContent = error.FileName;
        fileNameCell.appendChild(initialFileName);

        // Create cell for Dates (optional if you want to display dates)
        const datesCell = document.createElement('div');
        datesCell.className = 'cell';
        if (error.Hours?.length > 0){
            datesCell.textContent = error.Hours[0].Date;
        }

        // Create a dropdown cell
        if (!isEmpty(error.WorkOrderEntry)) {
            const dropdownCell = document.createElement('div');
            dropdownCell.className = 'cell';
            const select = document.createElement('select');
            select.className = 'workOrderDropdown';
            Object.keys(error.WorkOrderEntry).forEach(projectNumber => {
                const optgroup = document.createElement('optgroup');
                optgroup.label = projectNumber;
                error.WorkOrderEntry[projectNumber].forEach(workOrder => {
                    const option = document.createElement('option');
                    // Assuming workOrder is an object with one key-value pair
                    const workOrderId = Object.keys(workOrder)[0];
                    const workOrderNumber = workOrder[workOrderId];
                    option.value = workOrderId;
                    option.textContent = workOrderNumber;
                    optgroup.appendChild(option);
                });
                select.appendChild(optgroup);
            });
            dropdownCell.appendChild(select);

            row.appendChild(employeeCell);
            row.appendChild(messageCell);
            row.appendChild(fileNameCell);
            row.appendChild(datesCell);
            row.appendChild(dropdownCell);
        }
        else{
            const dropdownCell = document.createElement('div');                
            dropdownCell.className = 'cell';
            dropdownCell.textContent = '';

            row.appendChild(employeeCell);
            row.appendChild(messageCell);
            row.appendChild(fileNameCell);
            row.appendChild(datesCell);
            row.appendChild(dropdownCell);
        }

        // Create a cell for the Toggle Button
        const toggleCell = document.createElement('div');
        toggleCell.className = 'cell';
        toggleCell.style = "text-align: center;";

        const toggleButton = document.createElement('button');            
        toggleButton.addEventListener('click', function() {
            toggleButton.textContent = toggleButton.textContent === 'No' ? 'Yes' : 'No';

            row.classList.toggle('grayed-out'); // Toggle the grayed-out class for the row
            
            const dropdown = row.querySelector('.workOrderDropdown');
            if (dropdown) {
                // Toggle the hidden class to show or hide the dropdown
                dropdown.classList.toggle('hidden');
            }
        });

        toggleButton.textContent = 'No'; // Customize this text or use an icon
        
        if (isEmpty(error.WorkOrderEntry)) {
            toggleButton.style.display = 'none';   
        }

        toggleCell.appendChild(toggleButton);
        row.appendChild(toggleCell);

        // Add the new row to the errorsContainer
        errorsContainer.appendChild(row);
        return errorsContainer.children.length - 1; // Return index of the newly added row
    }


    document.addEventListener('DOMContentLoaded', function() {
        // Assuming the name_errors data is available here
        const name_errors = [/* your data here */];

        const employeeSelects = document.querySelectorAll('.employee_dropdown');

        employeeSelects.forEach((selectElement) => {
            // Populate each select with employee names and data
            name_errors.forEach(name_error => {
                name_error.EmployeeData.forEach(emp => {
                    const option = document.createElement('option');
                    option.value = emp.EmployeeID;
                    option.textContent = emp.EmployeeName;
                    // Store the WorkOrders as a stringified JSON in a data attribute
                    option.setAttribute('data-workorders', JSON.stringify(emp.WorkOrders));
                    selectElement.appendChild(option);
                });
            });

            // Add change event listener to each employee select dropdown
            selectElement.addEventListener('click', function() {
                const selectedOption = this.options[this.selectedIndex];
                const workOrders = JSON.parse(selectedOption.getAttribute('data-workorders') || '[]');

                // Assuming there is a way to identify the corresponding work order select for this employee select
                let workOrderDropdown = this.closest('.row').querySelector('.workOrderDropdown');
                workOrderDropdown.innerHTML = '';

                Object.entries(workOrders).forEach(([projectNumber, workOrdersArray]) => {
                    // Create an optgroup for each project
                    const optgroup = document.createElement('optgroup');
                    optgroup.label = projectNumber;
                    
                    // Iterate through the work orders for this project
                    workOrdersArray.forEach(workOrder => {
                        // Since each workOrder is an object with one key-value pair
                        Object.entries(workOrder).forEach(([workOrderID, workOrderName]) => {
                            // Create an option for each work order
                            const option = document.createElement('option');
                            option.value = workOrderID; // Use the work order ID as the value
                            option.textContent = workOrderName; // Use the work order name as the display text
                            optgroup.appendChild(option); // Add the option to the optgroup
                        });
                    });

                    workOrderDropdown.appendChild(optgroup); // Add the optgroup to the workOrderDropdown
                });
            });
        });
    });


    function handleAction(cancel = false) {
        const selectedRows = Array.from(document.querySelectorAll('.row:not(.header)'));
        console.log(selectedRows);
        const dataToSend = [];
        const fileIDs = [];

        const errorsJSON = sessionStorage.getItem('errors');
        const errors = JSON.parse(errorsJSON) || [];
        
        const name_errorsJSON = sessionStorage.getItem('name_errors');
        const name_errors = JSON.parse(name_errorsJSON) || [];
        console.log(selectedRows);
        selectedRows.forEach(row => {
            const id = row.getAttribute('data-id');  // Retrieve the ID from the data attribute
            const rowData = errors.find(data => data['ID'] === Number(id)) || name_errors.find(data => data['ID'] === Number(id));
            console.log(rowData);
            // Check if rowData and rowData.Hours are defined and rowData.Hours is an array with at least one item
            if (rowData && rowData.FileID) {
                const fileID = rowData.FileID;
                fileIDs.push(fileID);
            } else {
                console.log(`No Hours data found for ID: ${id}`);
            }
        });
        console.log(fileIDs);
        deleteFiles(fileIDs); // Call deleteFiles function if cancel action is confirmed
    }

    function handleResponse(response) {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    }

    function deleteFiles(fileIDs) {
        fetch('/edit-files', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ fileIDs: fileIDs }),
        })
        .then(handleResponse)
        .then(data => {
            console.log('Files deleted successfully:', data);
            window.location.href = '/';
        })
        .catch(error => console.error('Error:', error));
    }


    function submitUpdates() {
        const selectedRows = Array.from(document.querySelectorAll('.row:not(.grayed-out):not(.header)')).filter(row => {
            // Check if the row contains a select element with the class 'workOrderDropdown'
            return row.querySelector('select.workOrderDropdown') !== null;
        });
        const dataToSend = [];

        const errorsJSON = sessionStorage.getItem('errors');
        const errors = JSON.parse(errorsJSON) || [];
        
        const name_errorsJSON = sessionStorage.getItem('name_errors');
        const name_errors = JSON.parse(name_errorsJSON) || [];

        //Check that all work orders are checked.
        let allRowsValid = true; // Flag to track if all rows have a selected WorkOrder

        selectedRows.forEach(row => {
            const id = row.getAttribute('data-id'); // Retrieve the ID from the data attribute

            // Find the original data entry by ID
            const rowData = errors.find(data => data['ID'] === Number(id));
            if (rowData) {
                // Extract the selected WorkOrderID from the dropdown
                const dropdown = row.querySelector('.workOrderDropdown');
                const selectedWorkOrderID = dropdown.value;
                if (!selectedWorkOrderID || selectedWorkOrderID === "defaultOptionValue") { // Check if a valid WorkOrder is not selected
                    allRowsValid = false; // Set flag to false if any row doesn't have a valid WorkOrder selected
                    dropdown.classList.add('needs-selection'); // Optional: Add a class to highlight dropdowns that need selection
                } else {
                    dataToSend.push({
                        'WorkOrderID': selectedWorkOrderID,
                        'Data': rowData['Data'],
                        'FileID': rowData['FileID'],
                        'WeekEnding': rowData['WeekEnding'],
                        "JobSite": rowData['JobSite'],
                        "FileName": rowData['FileName']
                    });
                }
            }
            else{
                const rowData = name_errors.find(data => data['ID'] === Number(id));
                if (rowData) {
                    const dropdown = row.querySelector('.workOrderDropdown');
                    const selectedWorkOrderID = dropdown.value;
                    if (!selectedWorkOrderID) { // Check if a valid WorkOrder is not selected
                        allRowsValid = false; // Set flag to false if any row doesn't have a valid WorkOrder selected
                        dropdown.classList.add('needs-selection'); // Optional: Add a class to highlight dropdowns that need selection
                    } else {
                        //Modify to suit the new data.
                        dataToSend.push({
                            'OriginalName':rowData['OriginalName'],
                            'WorkOrderID': selectedWorkOrderID,
                            'Data': rowData['Data'],
                            'FileID': rowData['FileID'],
                            'WeekEnding': rowData['WeekEnding'],
                            "JobSite": rowData['JobSite'],
                            "FileName": rowData['FileName']
                        });
                    }
                }
            }
        });

        if (!allRowsValid) {
            alert("Please select a WorkOrder for all rows before proceeding or toggle the Skip Update button to 'Yes'.");
            // Stop the function from proceeding further
            return;
        }

        // Now, send dataToSend to your server as before
        fetch('/resolve_internal_errors_expenses', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(dataToSend),
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok ' + response.statusText);
            }
            return response.json(); // Process and return the JSON body as a Promise
        })
        .then(data => {
            console.log("Errors: ", data.push_update_errors);
            if (data.push_update_errors && data.push_update_errors.length > 0) {
                console.log("Errors exist");
                let errorHtml = `
                <div style="max-height: 800px; max-width: 100%;"> <!-- Container with scrollbars -->
                    <ul style="text-align: left" class="list-group">`; // Ensures the text stays on one line
                
                data.push_update_errors.forEach((error) => {
                    errorHtml += `<li class="list-group-item list-group-item-danger">${error.Message}</li>`;
                });

                errorHtml += `
                    </ul>
                </div>`; // Close the container div

                Swal.fire({
                    title: 'Errors Detected',
                    html: errorHtml,
                    icon: 'error',
                    width: '1500px', // Adjust the width as needed
                    confirmButtonText: 'Acknowledge',
                    allowOutsideClick: false,
                    preConfirm: () => {
                        // Acknowledge and proceed to show data popup
                        if (data.completion_html) {
                            sessionStorage.removeItem('errors');
                            sessionStorage.removeItem('name_errors');
                            // Render Completion template
                            document.body.innerHTML = data.completion_html;
                            // Redirect after 5 seconds
                            setTimeout(function() {
                                window.location.href = '/approvals/expenses'; // Replace '/approvals' with your desired endpoint
                            }, 2500); // 5000 milliseconds = 5 seconds
                            //window.location.href = data.redirect_url;
                        }
                    }
                });
            } else {
                if (data.completion_html) {
                    sessionStorage.removeItem('errors');
                    sessionStorage.removeItem('name_errors');
                    // Render Completion template
                    document.body.innerHTML = data.completion_html;
                    // Redirect after 5 seconds
                    setTimeout(function() {
                        window.location.href = '/approvals/expenses'; // Replace '/approvals' with your desired endpoint
                    }, 2500); // 5000 milliseconds = 5 seconds
                    //window.location.href = data.redirect_url;
                }
            }
        })
        .catch(error => console.error('Error:', error));

    }
    </script>
