import os
import subprocess
import platform
import xmlrpc.client
from datetime import datetime, timedelta
import pprint
from dotenv import load_dotenv
import ast
import xlwings as xw
import pythoncom


class OdooAPI:
    def __init__(self, url, db):
        load_dotenv()
        self.url = os.getenv('ODOO_URL')
        self.db = os.getenv('ODOO_DB')

        self.username = os.getenv("ODOO_USER")
        self.password = os.getenv("ODOO_PASS")

        common = xmlrpc.client.ServerProxy('{}/xmlrpc/2/common'.format(self.url))
        self.uid = common.authenticate(self.db, self.username, self.password, {})

    def get_recent_timesheets(self):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        fields = models.execute_kw(self.db, self.uid, self.password, 'account.move', 'fields_get', [], {'attributes': ['string', 'type']})
        for field, attributes in fields.items():
            print(field, attributes)        
        # # Search for the most recent timesheets
        # timesheet_ids = models.execute_kw(
        #     self.db, 
        #     self.uid, 
        #     self.password, 
        #     'account.analytic.line', 
        #     'search', 
        #     [
        #         [('project_id', '!=', False)]  # Adjust this domain as needed to filter the timesheets
        #     ], 
        #     {
        #         'order': 'create_date desc',  # Sort by create_date in descending order
        #         'limit': 5  # Adjust the number of timesheets you want to fetch
        #     }
        # )
        
        # if not timesheet_ids:
        #     return "No recent timesheets found."

        # # Read details of these timesheets
        # timesheets = models.execute_kw(self.db, self.uid, self.password, 'account.analytic.line', 'read', 
        #                                [timesheet_ids, 
        #                                 ['date', 'employee_id', 'project_id', 'task_id', 'name', 'unit_amount', 'amount']]
        #                               )
        
        timesheets = models.execute_kw(
            self.db, 
            self.uid, 
            self.password, 
            'account.analytic.line', 
            'search_read', 
            [
                [('project_id', '!=', False)]  # Adjust this domain as needed to filter the timesheets
            ], 
            {
                'fields': ['date', 'employee_id', 'project_id', 'task_id', 'name', 'unit_amount', 'amount'],  # Specify the fields you need, including 'date'
                'order': 'date desc',  # Sort by 'date' in descending order
                'limit': 5  # Adjust the number of timesheets you want to fetch
            }
        )
        
        return timesheets
    
    def get_work_orders(self, selectedDate_dt):
        '''Gets the work orders from Odoo to
        store into the database.'''
        selectedDate_dt = datetime.strptime('2024-06-09', '%Y-%m-%d')
        # Convert the selectedDate string to a datetime object for manipulation
        #selectedDate_dt = datetime.strptime(selectedDate, '%Y-%m-%d')
        # Calculate the date for "selectedDate - 7 days"
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))

        date_minus_7_days_dt = selectedDate_dt - timedelta(days=7)
        # Convert dates back to string format expected by Odoo
        selectedDate_str = selectedDate_dt.strftime('%Y-%m-%d')
        date_minus_7_days_str = date_minus_7_days_dt.strftime('%Y-%m-%d')

       # Establish Odoo object repository        

        # Search filtered active (In Progress) tasks
        stage_id_field = os.getenv("STAGE_ID_FIELD")
        start_date_field = os.getenv("START_DATE_FIELD")
        end_date_field = os.getenv("END_DATE_FIELD")


        # TODO comment this out when we figure out end date situation, do the same in excel service.
        taskIDs = models.execute_kw(self.db, self.uid, self.password, 'project.task', 'search', [
            [
                '|',  # OR
                '&',  # AND
                (stage_id_field, '=', 187), 
                (start_date_field, '<=', selectedDate_str),  
                '|',  # OR
                '&',  # AND
                (stage_id_field, '!=', 187),  
                (end_date_field, '>=', date_minus_7_days_str),  
                (end_date_field, '=', False)  # This is now part of the OR group correctly
            ]
        ])
        
        
        # TODO comment this out when we figure out end date situation

        #taskIDs = models.execute_kw(self.db, self.uid, self.password, 'project.task', 'search', [[(stage_id_field, '=', 187)]]) # Stage ID 187 = 'WO - In Progress'

        # Pull detailed information with provided task IDs
       # Pull detailed information with provided task IDs
        fields_str = os.getenv("ODOO_FIELDS")
        fields = ast.literal_eval(fields_str)

        tasks = models.execute_kw(self.db, self.uid, self.password, 'project.task', 'read', [taskIDs], 
                         {'fields': fields })
        return tasks
    

    def get_all_subcontractor_contacts(self):        
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        # Define the search domain to find contacts whose parent has the "Subcontractor" tag
        #domain = [['parent_id.category_id.name', '=', 'Subcontractor']]
        domain = []
        # Include 'parent_id' in the fields list to fetch the parent ID
        fields = ['id', 'display_name', 'parent_id']

        contacts = models.execute_kw(self.db, self.uid, self.password, 
                                    'res.partner', 'search_read', 
                                    [domain], {'fields': fields})

        return contacts
    

    def get_customer_by_id(self, customer_id):
        # Define the model name and the domain to filter customers
        model_name = 'res.partner'
        domain = [('customer_rank', '>', 0)]  # Filtering for customers
        fields = [
            'id', 'name', 'email', 'phone', 'street', 'street2', 'city', 'state_id', 'zip', 'country_id'
        ]  # Fields to fetch    
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        
        # Read customer details using the fetched IDs
        customers = models.execute_kw(self.db, self.uid, self.password, model_name, 'read', [[customer_id], fields])
        
        return customers
    

    def pretty_print_timesheets(self, timesheets):
        if isinstance(timesheets, str):
            print(timesheets)  # In case the returned is a string message
            return
        print('\n')
        for timesheet in timesheets:
            print(f"Timesheet Description: {timesheet['name']}")
            print(f"Date: {timesheet['date']}")
            print(f"Hours Spent: {timesheet['unit_amount']}")
            print(f"Project: {timesheet['project_id'][1]}")  # Adjust if project_id is a list
            print(f"ProjectID: {timesheet['project_id'][0]}")  # Adjust if project_id is a list
            if timesheet.get('task_id'):
                print(f"Task: {timesheet['task_id'][1]}")  # Adjust if task_id is a list
                print(f"TaskID: {timesheet['task_id'][0]}")  # Adjust if task_id is a list
            print("-" * 40 + "\n")


    def update_work_order_sow_partner(self, employee_id, selected_contact):        
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        # Assuming selected_contact is a dictionary like {'id': 123, 'name': 'Subcontractor A'}
        # Extract the ID of the selected contact
        # Prepare the update values
        # Assuming x_studio_sow_partner expects just the ID of the contact
        update_values = {'display_name': selected_contact['name'],
                         os.getenv("SOW_CONTACT"): selected_contact['id']}
        
        success = models.execute_kw(self.db, self.uid, self.password, 
                                    'hr.employee', 'write', 
                                    [[employee_id], update_values])

        return success
    


def convert_excel_to_pdf(excel_path, pdf_filename):
    # Convert the relative path to an absolute path
    excel_full_path = os.path.abspath(excel_path)
    pdf_path = os.path.abspath(pdf_filename)

    try:
        if platform.system() == 'Windows':
            # Use subprocess to call libreoffice on Windows
            libreoffice_path = 'C:\\Program Files\\LibreOffice\\program\\soffice.exe'
            subprocess.run([libreoffice_path, '--headless', '--convert-to', 'pdf', '--outdir', os.path.dirname(pdf_path), excel_full_path], check=True)
        else:
            # Use subprocess to call libreoffice on Linux
            subprocess.run(['libreoffice', '--headless', '--convert-to', 'pdf', '--outdir', os.path.dirname(pdf_path), excel_full_path], check=True)
        
        if not os.path.exists(pdf_path):
            raise Exception(f"PDF file not created: {pdf_path}")
    except Exception as e:
        print(f"Error converting Excel to PDF: {e}")
        pdf_path = None

    return pdf_path


excel_template_path = 'static\\assets\\AtomTech_Timesheet_Template.xlsx'
pdf_path = convert_excel_to_pdf(excel_template_path, excel_template_path.replace(".xlsx", ".pdf"))

print('Done.')



