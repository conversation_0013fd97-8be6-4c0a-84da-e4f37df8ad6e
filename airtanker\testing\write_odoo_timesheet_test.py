import os
import xmlrpc.client
from datetime import datetime, timedelta
import pprint
from dotenv import load_dotenv


class OdooAPI:
    def __init__(self, url, db):
        load_dotenv()
        self.url = os.getenv('ODOO_URL')
        self.db = os.getenv('ODOO_DB')

        self.username = os.getenv("ODOO_USER")
        self.password = os.getenv("ODOO_PASS")

        common = xmlrpc.client.ServerProxy('{}/xmlrpc/2/common'.format(self.url))
        self.uid = common.authenticate(self.db, self.username, self.password, {})

    def get_recent_timesheets(self):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        
        # Search for the most recent timesheets
        timesheet_ids = models.execute_kw(self.db, self.uid, self.password, 'account.analytic.line', 'search', [
            [('project_id', '!=', False)],  # Adjust this domain as needed to filter the timesheets
            0, 5, 'create_date desc'  # Adjust the number of timesheets you want to fetch
        ])
        
        if not timesheet_ids:
            return "No recent timesheets found."

        # Read details of these timesheets
        timesheets = models.execute_kw(self.db, self.uid, self.password, 'account.analytic.line', 'read', [timesheet_ids, ['name', 'date', 'unit_amount', 'user_id', 'project_id', 'task_id']])
        
        return timesheets

    def create_timesheet(self, description, date, hours, project_id):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))

        timesheet_data = {
            'name': description, # Name of timesheet its: WO_Name - Contractor Name ("Double Time", "Overtime", "")
            'date': date,
            'unit_amount': hours,
            'user_id': False,
            'employee_id': 565,
            'project_id': project_id,
            'task_id': 3190
        }
        
        timesheet_id = models.execute_kw(self.db, self.uid, self.password, 'account.analytic.line', 'create', [timesheet_data])
        
        return timesheet_id

    def pretty_print_timesheets(self, timesheets):
        if isinstance(timesheets, str):
            print(timesheets)  # In case the returned is a string message
            return
        print('\n')
        for timesheet in timesheets:
            print(f"Timesheet Description: {timesheet['name']}")
            print(f"User: {timesheet['user_id'][1]}")  # Adjust if user_id is a list
            print(f"Date: {timesheet['date']}")
            print(f"Hours Spent: {timesheet['unit_amount']}")
            print(f"Project: {timesheet['project_id'][1]}")  # Adjust if project_id is a list
            if timesheet.get('task_id'):
                print(f"Task: {timesheet['task_id'][1]}")  # Adjust if task_id is a list
            print("-" * 40 + "\n")


# Example usage
load_dotenv()

api = OdooAPI(os.getenv('ODOO_URL'), os.getenv('ODOO_DB'))
recent_timesheets = api.get_recent_timesheets()
api.pretty_print_timesheets(recent_timesheets)

# Create a new timesheet entry
new_timesheet_id = api.create_timesheet(
    description="Development work on module XYZ",
    date=str(datetime.now().date()),
    hours=4.0,
    user_id=1,  # Adjust with a valid user ID
    project_id=1,  # Adjust with a valid project ID
    task_id=1  # Adjust with a valid task ID, if needed
)

print(f"New timesheet created with ID: {new_timesheet_id}")

print('Done.')
