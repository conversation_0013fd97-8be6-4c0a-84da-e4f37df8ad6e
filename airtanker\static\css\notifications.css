#notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    pointer-events: none;
  }
  
  .notification {
    width: 384px;
    margin-bottom: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: #fff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    pointer-events: auto;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease-in-out;
  }
  
  .notification.show {
    transform: translateX(0);
    opacity: 1;
  }
  
  .notification.success { background-color: rgb(240 253 244); border: 1px solid rgb(187 247 208); }
  .notification.error { background-color: rgb(254 242 242); border: 1px solid rgb(254 202 202); }
  .notification.info { background-color: rgb(239 246 255); border: 1px solid rgb(191 219 254); }
  .notification.warning { background-color: rgb(254 252 232); border: 1px solid rgb(254 240 138); }
  .notification.odoo { background-color: rgb(245 243 255); border: 1px solid rgb(216 180 254); }
    
  .notification-content {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .notification-icon {
    flex-shrink: 0;
  }
  
  .notification-text {
    flex: 1;
  }
  
  .notification-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
  }
  
  .notification-title.success { color: rgb(22 101 52); }
  .notification-title.error { color: rgb(153 27 27); }
  .notification-title.info { color: rgb(30 58 138); }
  .notification-title.warning { color: rgb(133 77 14); }
  .notification-title.odoo { color: rgb(107 33 168); }
  
  .notification-message {
    color: rgb(55 65 81);
  }
  
  .notification-close {
    flex-shrink: 0;
    color: rgb(156 163 175);
    font-size: 1.5rem;
    cursor: pointer;
    transition: color 0.2s;
  }
  
  .notification-close:hover {
    color: rgb(107 114 128);
  }