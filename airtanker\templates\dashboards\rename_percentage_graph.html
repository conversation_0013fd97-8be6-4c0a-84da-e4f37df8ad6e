<!-- Approvals -->
<div class="card chart-container">
  <canvas id="approvals_percentage_chart"></canvas>
</div>

<div class="card chart-container" style="margin-top: 25px;">
<canvas id="approvals_count_chart"></canvas>
</div>

<!-- Bills -->
<div class="card chart-container" style="margin-top: 25px;">
<canvas id="bills_percentage_chart"></canvas>
</div>

<div class="card chart-container" style="margin-top: 25px;">
<canvas id="bills_count_chart"></canvas>
</div>

<!-- Invoices -->
<div class="card chart-container" style="margin-top: 25px;">
<canvas id="invoices_percentage_chart"></canvas>
</div>

<div class="card chart-container" style="margin-top: 25px;">
<canvas id="invoices_count_chart"></canvas>
</div>

<!-- Timesheets -->
<div class="card chart-container" style="margin-top: 25px;">
<canvas id="timesheets_percentage_chart"></canvas>
</div>

<div class="card chart-container" style="margin-top: 25px;">
<canvas id="timesheets_count_chart"></canvas>
</div>

<link rel="stylesheet" href="../contrast-bootstrap-pro/css/bootstrap.min.css" />
<link rel="stylesheet" href="../contrast-bootstrap-pro/css/cdb.css" />
<script src="../contrast-bootstrap-pro/js/cdb.js"></script>
<script src="../contrast-bootstrap-pro/js/bootstrap.min.js"></script>
<script src="https://kit.fontawesome.com/9d1d9a82d2.js" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.4/Chart.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
  fetch('/api/approvals/timesheets')
    .then(response => response.json())
    .then(data => {
      const labels = data.map(item => item.WeekEnding);
      const totalCounts = data.map(item => item.TotalCount);

      const approvalPercentages = data.map(item => item.ApprovalPercentage);
      const approvalCounts = data.map(item => item.ApprovedCount);

      const billPercentages = data.map(item => item.BillPercentage);
      const billCounts = data.map(item => item.BillCount);

      const invoicePercentages = data.map(item => item.InvoicePercentage);
      const invoiceCounts = data.map(item => item.InvoiceCount);

      const timesheetPercentages = data.map(item => item.TimesheetPercentage);
      const timesheetCounts = data.map(item => item.TimesheetCount);

      function createChart(ctx, label, data, remainingData, isPercentage) {
        return new Chart(ctx, {
          type: 'horizontalBar',
          data: {
            labels: labels,
            datasets: [
              {
                label: label,
                backgroundColor: 'rgba(161, 198, 247, 1)',
                borderColor: 'rgb(47, 128, 237)',
                data: data,
                hoverBackgroundColor: 'rgba(47, 128, 237, 0.8)',
                stack: 'Stack 0' // Ensure the total count bars are on the same stack
              },  
              {
                label: 'Remaining',
                backgroundColor: 'rgba(200, 200, 200, 0.5)',
                borderColor: 'rgba(200, 200, 200, 1)',
                data: remainingData,
                hoverBackgroundColor: 'rgba(180, 180, 180, 0.8)',
                stack: 'Stack 0' // Ensure the total count bars are on the same stack
              }
            ]
          },
          options: {
            title: {
              display: true,
              text: label
            },
            tooltips: {
              callbacks: {
                label: function(tooltipItem, data) {
                  return data.datasets[tooltipItem.datasetIndex].label + ': ' + tooltipItem.xLabel + (isPercentage ? '%' : '');
                }
              }
            },
            legend: {
              display: false
            },
            scales: {
              xAxes: [{
                ticks: {
                  beginAtZero: true,
                  max: isPercentage ? 100 : undefined,
                  callback: function(value) {
                    return value + (isPercentage ? '%' : '');
                  }
                },
                scaleLabel: {
                  display: true,
                  labelString: isPercentage ? 'Percentage' : 'Total'
                }
              }],
              yAxes: [{
                scaleLabel: {
                  display: true,
                  labelString: 'Week Ending'
                }
              }]
            },
            responsive: true,
            maintainAspectRatio: false,
          }
        });
      }


      createChart(document.getElementById('approvals_percentage_chart').getContext('2d'), 'Approvals Percentage', approvalPercentages, [], true);
      createChart(document.getElementById('approvals_count_chart').getContext('2d'), 'Total Approvals', approvalCounts, totalCounts.map((total, index) => total - approvalCounts[index]), false);
      createChart(document.getElementById('bills_percentage_chart').getContext('2d'), 'Bills Percentage Exported', billPercentages, [], true);
      createChart(document.getElementById('bills_count_chart').getContext('2d'), 'Bills Exported', billCounts, totalCounts.map((total, index) => total - billCounts[index]), false);
      createChart(document.getElementById('invoices_percentage_chart').getContext('2d'), 'Invoices Percentage Exported', invoicePercentages, [], true);
      createChart(document.getElementById('invoices_count_chart').getContext('2d'), 'Invoices Exported', invoiceCounts, totalCounts.map((total, index) => total - invoiceCounts[index]), false);
      createChart(document.getElementById('timesheets_percentage_chart').getContext('2d'), 'Timesheets Percentage Exported', timesheetPercentages, [], true);
      createChart(document.getElementById('timesheets_count_chart').getContext('2d'), 'Timesheets Exported', timesheetCounts, totalCounts.map((total, index) => total - timesheetCounts[index]), false);
    })
    .catch(error => console.error('Error fetching approval data:', error));
});

</script>
