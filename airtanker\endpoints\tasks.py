from main import airtanker_app
from endpoints.progress import update_progress
from services.DatabaseService import DatabaseService
from services.OdooService import OdooService
import os
from datetime import datetime

import pprint

def post_vendor_bills_task(entries,
                            odoo_service, # type: OdooService
                            task_id):
    
    database_service = DatabaseService()
    database_service.connect()

    contact_bills = {}

    # Get JSON data from the request
    total_entries = sum(len(value_list) for value_list in entries.values())
    progress = 100 / (total_entries * 8)  # Adjust denominator if needed
    total_progress = 0

    try:
        for key, value_list in entries.items():
            airtanker_app.logger.info('Exporting data for %s', key)
            for index, entry in enumerate(value_list):
                # Assuming 'ContactID' and 'ContactName' are part of what each dictionary might represent
                contractor_id = entry['ContractorID']

                rate_type = entry['RateType'] # type: str
                is_fixed_rate = False
                if rate_type:
                    is_fixed_rate = 'fix' in rate_type.lower()
                    
                total_expenses = None

                # get expenses
                if entry['Expenses']:
                    total_expenses = entry['Expenses']['ApprovedTotalExpenses']

                sow_contact_id = int(key)

                if contact_bills and sow_contact_id in contact_bills:
                    write_bill_id = contact_bills[sow_contact_id]
                else:
                    # Create a new bill in Odoo and retrieve the ID
                    write_bill_id = odoo_service.create_bill_return_id(
                        sow_contact_id, # ## Make this the Contact ID - key to the dictionary
                        f"{entry['LineItemWeekEnding']}{entry['ParentCode']}", # name we 3424 ATCA
                        entry['LineItemWeekEnding']
                    )

                    if sow_contact_id:
                        contact_bills[sow_contact_id] = write_bill_id

                # Toggle the "bill created" flag in your database
                database_service.toggle_timesheet_bill_created(entry['TimesheetID'], write_bill_id)
                total_progress += progress
                update_progress(task_id=task_id, progress=total_progress)
                # Add line item names
                odoo_service.add_line_item_name(writeBillID=write_bill_id, contractor_name=entry['ContractorName'])            
                approved_hours = entry['ApprovedData']
                total_progress += progress
                update_progress(task_id=task_id, progress=total_progress)
                if 'AccountID' in entry:
                        account_id = entry['AccountID']
                else:
                    account_id = None

                if is_fixed_rate:
                    # Adding standard hours line item
                    odoo_service.add_line_item_to_bill(
                        write_bill_id,
                        accountID=account_id,
                        line_item_name=f"W.E. {entry['LineItemWeekEnding']} - Standard",
                        hours=approved_hours['Standard']['Hours'],
                        rate=approved_hours['Standard']['Rate']
                    )
                    total_progress += progress * 4
                    update_progress(task_id=task_id, progress=total_progress)
                else:
                    # Adding standard hours line item

                    odoo_service.add_line_item_to_bill(
                        write_bill_id,
                        accountID=account_id,
                        line_item_name=f"W.E. {entry['LineItemWeekEnding']} - Standard",
                        hours=approved_hours['Standard']['Hours'],
                        rate=approved_hours['Standard']['Rate']
                    )
                    total_progress += progress
                    update_progress(task_id=task_id, progress=total_progress)

                    # Adding travel hours line item
                    odoo_service.add_line_item_to_bill(
                        write_bill_id,
                        accountID=account_id,
                        line_item_name=f"W.E. {entry['LineItemWeekEnding']} - Travel",
                        hours=approved_hours['Travel']['Hours'],
                        rate=approved_hours['Travel']['Rate']
                    )
                    total_progress += progress
                    update_progress(task_id=task_id, progress=total_progress)
                    # Adding overtime hours line item
                    odoo_service.add_line_item_to_bill(
                        write_bill_id,
                        accountID=account_id,
                        line_item_name=f"W.E. {entry['LineItemWeekEnding']} - Over Time",
                        hours=approved_hours['OT']['Hours'],
                        rate=approved_hours['OT']['Rate']
                    )
                    total_progress += progress
                    update_progress(task_id=task_id, progress=total_progress)
                    # Adding double time hours line item
                    odoo_service.add_line_item_to_bill(
                        write_bill_id,
                        accountID=account_id,
                        line_item_name=f"W.E. {entry['LineItemWeekEnding']} - Double Time",
                        hours=approved_hours['DT']['Hours'],
                        rate=approved_hours['DT']['Rate']
                    )
                    total_progress += progress
                    update_progress(task_id=task_id, progress=total_progress)
                    # Adding holiday hours line item
                    odoo_service.add_line_item_to_bill(
                        write_bill_id,
                        accountID=account_id,
                        line_item_name=f"W.E. {entry['LineItemWeekEnding']} - Holiday Time",
                        hours=approved_hours['Holiday']['Hours'],
                        rate=approved_hours['Holiday']['Rate']
                    )

                if total_expenses is not None:
                    odoo_service.add_line_item_expense(
                        writeBillID=write_bill_id,
                        accountID="",
                        line_item_name="[Expenses] All\nPer Diem, Fuel, Hotel, Misc. items.",
                        expense_total=total_expenses
                    )
                
                if 'Contacts' in entry and sow_contact_id:
                    odoo_service.update_work_order_sow_partner(int(contractor_id), {'id': sow_contact_id, 'name': entry['SOW_Contact'][1]}) 

                total_progress += progress
                update_progress(task_id=task_id, progress=total_progress)
        update_progress(task_id=task_id, progress="Completed")
    except Exception as e:
        airtanker_app.logger.exception("Error posting vendor bills: %s", e)
        update_progress(task_id=task_id, progress="Failed")
    finally:
        database_service.disconnect()

def post_customer_invoices_task(entries, odoo_service, task_id):
    """
    Creates customer invoices in Odoo from the provided entries using bulk creation.
    
    Args:
        entries (dict): Dictionary of invoice entries grouped by key
        odoo_service (OdooService): Instance of OdooService for Odoo operations
        task_id: ID for tracking progress
    """
    error_log = {
        'processing_errors': [],
        'invoice_creation_errors': [],
        'line_creation_errors': [],
        'expense_errors': []
    }

    try:
        database_service = DatabaseService()
        database_service.connect()
        project_bill_dict = {}
    except Exception as e:
        error_msg = f'Failed to initialize database connection: {str(e)}'
        airtanker_app.logger.error(error_msg)
        raise

    # Calculate progress increments
    total_entries = sum(len(value_list) for value_list in entries.values())
    progress = 100 / (total_entries * 5.5)  # Adjust denominator if needed
    total_progress = 0

    ats_expenses_all = {}

    try:
        for key, value_list in entries.items():
            expenses_all = {}
            invoice_lines_batch = []
            
            for index, entry in enumerate(value_list):
                try:
                    contractor_name = entry["ContractorName"]
                    contractor_number = entry["ContractorNumber"]
                    week_ending = entry["WeekEnding"]
                    approved_data = entry["ApprovedData"]
                    accountID = entry["AccountID"]
                    plant_name = entry["PlantName"]
                    expenses = entry["Expenses"]

                    if expenses:
                        try:
                            if "ATS" in entry["ProjectName"].upper():
                                if plant_name:
                                    if plant_name not in ats_expenses_all:
                                        ats_expenses_all[plant_name] = {
                                            "TotalExpenses": float(expenses["ApprovedTotalExpenses"]),
                                            "ExpenseString": f"{contractor_name} - ${expenses['ApprovedTotalExpenses']}\n",
                                            "WeekEnding": week_ending,
                                            "AccountID": accountID,
                                            "CustomerID": entry["CustomerID"],
                                            "CustomerPO": entry["CustomerPO"]
                                        }
                                    else:
                                        ats_expenses_all[plant_name]["TotalExpenses"] += float(expenses["ApprovedTotalExpenses"])
                                        ats_expenses_all[plant_name]["ExpenseString"] += f"{contractor_name} - ${expenses['ApprovedTotalExpenses']}\n"
                            else:
                                if plant_name:
                                    if plant_name not in expenses_all:
                                        expenses_all[plant_name] = {
                                            "TotalExpenses": float(expenses["ApprovedTotalExpenses"]),
                                            "ExpenseString": f"{contractor_name} - ${expenses['ApprovedTotalExpenses']}\n"
                                        }
                                    else:
                                        expenses_all[plant_name]["TotalExpenses"] += float(expenses["ApprovedTotalExpenses"])
                                        expenses_all[plant_name]["ExpenseString"] += f"{contractor_name} - ${expenses['ApprovedTotalExpenses']}\n"
                        except Exception as e:
                            error_msg = f"Error processing expenses for contractor {contractor_name}: {str(e)}"
                            error_log['expense_errors'].append(error_msg)

                    try:
                        if entry["ProjectID"] in project_bill_dict:
                            invoice_bill_id = project_bill_dict[entry["ProjectID"]]
                        else:
                            invoice_bill_id = odoo_service.create_invoice_bill(
                                customerID=entry["CustomerID"],
                                customerPO=entry["CustomerPO"],
                                week_ending_str=week_ending
                            )
                            project_bill_dict[entry["ProjectID"]] = invoice_bill_id
                    except Exception as e:
                        error_msg = f"Error creating invoice for project {entry['ProjectID']}: {str(e)}"
                        error_log['invoice_creation_errors'].append(error_msg)
                        continue

                    try:
                        database_service.toggle_timesheet_invoice_created(entry['TimesheetID'], invoice_bill_id)
                        total_progress += progress
                        update_progress(task_id=task_id, progress=total_progress)

                        invoice_lines_batch.append({
                            'name': contractor_name + contractor_number,
                            'move_id': invoice_bill_id,
                            'display_type': 'line_section'
                        })

                        base_line_item = {
                            'move_id': invoice_bill_id,
                            'product_id': 35,
                            'product_uom_id': 6,
                            'analytic_distribution': {str(accountID[0]): 100.0}
                        }

                        rate_type = entry['RateType']
                        is_fixed_rate = False
                        if rate_type:
                            is_fixed_rate = 'fix' in rate_type.lower()

                        if is_fixed_rate:
                            invoice_lines_batch.append({
                                **base_line_item,
                                'name': f"{contractor_name} - ST\nW.E. {week_ending}",
                                'quantity': approved_data['Standard']['Hours'],
                                'price_unit': approved_data['Standard']['Rate']
                            })
                            total_progress += progress * 4
                        else:
                            invoice_lines_batch.extend([
                                {
                                    **base_line_item,
                                    'name': f"{contractor_name} - ST\nW.E. {week_ending}",
                                    'quantity': approved_data['Standard']['Hours'],
                                    'price_unit': approved_data['Standard']['Rate']
                                },
                                {
                                    **base_line_item,
                                    'name': f"{contractor_name} - OT\nW.E. {week_ending}",
                                    'quantity': approved_data['OT']['Hours'],
                                    'price_unit': approved_data['OT']['Rate']
                                },
                                {
                                    **base_line_item,
                                    'name': f"{contractor_name} - DT\nW.E. {week_ending}",
                                    'quantity': approved_data['DT']['Hours'],
                                    'price_unit': approved_data['DT']['Rate']
                                }
                            ])
                            total_progress += progress * 3
                        update_progress(task_id=task_id, progress=total_progress)
                    except Exception as e:
                        error_msg = f"Error creating invoice lines for contractor {contractor_name}: {str(e)}"
                        error_log['line_creation_errors'].append(error_msg)

                except Exception as e:
                    error_msg = f"Error processing entry {index} for key {key}: {str(e)}"
                    error_log['processing_errors'].append(error_msg)
                    continue

            # Create all line items in bulk
            if invoice_lines_batch:
                try:
                    odoo_service.create_invoice_lines_in_bulk(invoice_lines_batch)
                except Exception as e:
                    error_msg = f"Error creating bulk invoice lines: {str(e)}"
                    error_log['line_creation_errors'].append(error_msg)

            # Handle expenses for current batch
            if expenses_all:
                try:
                    expense_lines_batch = []
                    for plant, expense_data in expenses_all.items():
                        expense_lines_batch.extend([
                            {
                                'name': f"EE {entry['ProjectName']}",
                                'move_id': invoice_bill_id,
                                'display_type': 'line_section'
                            },
                            {
                                'name': f"[Expenses] All - Project # (PROJECT # HERE)\nPer Diem, Fuel, Hotel, Misc. items.,\n\n{expense_data['ExpenseString']}",
                                'move_id': invoice_bill_id,
                                'quantity': 1,
                                'product_id': 285,
                                'product_uom_id': 1,
                                'price_unit': expense_data['TotalExpenses']
                            }
                        ])
                    
                    if expense_lines_batch:
                        odoo_service.create_invoice_lines_in_bulk(expense_lines_batch)
                except Exception as e:
                    error_msg = f"Error creating expense lines: {str(e)}"
                    error_log['expense_errors'].append(error_msg)

        # Handle ATS expenses
        for plant_name, ats_data in ats_expenses_all.items():
            try:
                ats_invoice_bill_id = odoo_service.create_invoice_bill(
                    customerID=ats_data["CustomerID"],
                    customerPO=ats_data["CustomerPO"],
                    week_ending_str=ats_data["WeekEnding"]
                )
                
                ats_expense_lines = [
                    {
                        'name': f"Expenses {plant_name}",
                        'move_id': ats_invoice_bill_id,
                        'display_type': 'line_section'
                    },
                    {
                        'name': f"[Expenses] All - Project # (PROJECT # HERE)\nPer Diem, Fuel, Hotel, Misc. items.,\n\n{ats_data['ExpenseString']}",
                        'move_id': ats_invoice_bill_id,
                        'quantity': 1,
                        'product_id': 285,
                        'product_uom_id': 1,
                        'price_unit': ats_data['TotalExpenses']
                    }
                ]
                odoo_service.create_invoice_lines_in_bulk(ats_expense_lines)
            except Exception as e:
                error_msg = f"Error processing ATS expenses for plant {plant_name}: {str(e)}"
                error_log['expense_errors'].append(error_msg)
                    
        update_progress(task_id=task_id, progress="Completed")
    except Exception as e:
        airtanker_app.logger.error(e)
        update_progress(task_id=task_id, progress="Failed")
    finally:    
        database_service.disconnect()

def post_timesheets_task(entries, odoo_service, task_id):
    database_service = None

    try:
        # Filtrar y clasificar entradas
        valid_entries = [entry for entry in entries if entry["IsChecked"]]
        internal_updates = []
        regular_updates = []

        for entry in valid_entries:
            if entry.get("InternalID"):
                internal_updates.append(entry)
            elif entry.get("TimesheetID"):
                regular_updates.append(entry)

        if not valid_entries:
            update_progress(task_id=task_id, progress="Completed")
            return

        # Procesamiento batch en Odoo
        odoo_ids = odoo_service.create_timesheets_batch(valid_entries)

        update_progress(task_id=task_id, progress=50)

        # Preparar datos para actualización
        database_service = DatabaseService()
        if not database_service.connect():
            raise Exception("Database connection failed")

        # Procesar actualizaciones regulares
        if regular_updates:
            regular_data = [
                (odoo_id, entry['TimesheetID']) 
                for odoo_id, entry in zip(odoo_ids, regular_updates)
            ]
            database_service.batch_update_timesheets(regular_data)

        # Procesar actualizaciones internas
        if internal_updates:
            internal_data = [
                (odoo_id, entry['InternalID']) 
                for odoo_id, entry in zip(odoo_ids, internal_updates)
            ]
            database_service.batch_update_timesheets(internal_data, is_internal=True)

        update_progress(task_id=task_id, progress="Completed")

    except Exception as e:
        airtanker_app.logger.error(f"Error during post_timesheets_task excution: {str(e)}")
        update_progress(task_id=task_id, progress="Failed")
        # raise # Given it was started in a thread, raising the error wont be caught by the main thread it is just printe to the terminal, need to send information directly to the frontend
    finally:
        if database_service:
            database_service.disconnect()