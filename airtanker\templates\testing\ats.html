
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AirTanker</title>
    <link rel="icon" href="{{ url_for('static', filename='favicon-32x32.png') }}" type="image/png">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" rel="stylesheet">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap4.min.css">

    <style>
        .file-upload-wrapper {
            border: 2px dashed #91b0b3;
            border-radius: 10px;
            padding: 10px;
            text-align: center;
            position: relative;
            cursor: pointer;
        }
        /* Style the console-like area */
        #console {
            border: 1px solid #ccc;
            padding: 10px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
        }
        /* Style different types of messages */
        .error {
            color: red;
        }
        #detailsModal .modal-dialog {
            max-width: 90%;
        }
        /* Add a horizontal scrollbar to the modal body */
        #detailsModal .modal-body {
            overflow-x: auto;
        }
        .file-upload-wrapper:hover {
            background-color: #f3f4f6;
        }
        .file-upload-wrapper i {
            color: #5cb85c;
        }
        .list-group-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .list-group-item i.fa-times {
            color: red;
            cursor: pointer;
        }
        #browse-btn {
            color: blue; /* Set the text color */
            text-decoration: underline; /* Underline the text to mimic a hyperlink */
            cursor: pointer; /* Change the cursor to indicate it's clickable */
        }
    </style>
</head>
<body>
    <!-- Modal for the spinner overlay -->
    <div class="modal" id="spinnerModal" tabindex="-1" role="dialog" aria-labelledby="spinnerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-2">Processing...</p>
                </div>
            </div>
        </div>
    </div>


    <!-- The file upload areas -->
    <div class="container mt-4">
        <div class="row">
            <!-- Left side upload form for Customer Sheets -->
            <div class="col-md-6">
                <h3 class="text-center">ATS Customer Sheets</h3> <!-- Title for the left container -->
                <form id="upload-form-left" enctype="multipart/form-data">
                    <div class="file-upload-wrapper" onclick="document.getElementById('file-upload-left').click()">
                        <i class="fas fa-cloud-upload-alt fa-2x"></i>
                        <p>Drag files here or <a href="#" onclick="handleBrowseClickLeft(event)">Browse</a></p>
                    </div>
                    <input id="file-upload-left" type="file" name="customerFiles[]" multiple style="display: none;" onchange="addFilesCustomer()">
                    <ul class="list-group mt-3" id="file-list-left" style=" margin-bottom: 20px;">
                        <!-- Files will be listed here -->
                    </ul>
                </form>
            </div>
            
            <!-- Right side upload form for Internal Sheets -->
            <div class="col-md-6">
                <h3 class="text-center">Internal Sheets</h3> <!-- Title for the right container -->
                <form id="upload-form-right" enctype="multipart/form-data">
                    <div class="file-upload-wrapper" onclick="document.getElementById('file-upload-right').click()">
                        <i class="fas fa-cloud-upload-alt fa-2x"></i>
                        <p>Drag files here or <a href="#" onclick="handleBrowseClickRight(event)">Browse</a></p>
                    </div>
                    <input id="file-upload-right" type="file" name="internalFiles[]" multiple style="display: none;" onchange="addFilesInternal()">
                    <ul class="list-group mt-3" id="file-list-right" style=" margin-bottom: 20px;">
                        <!-- Files will be listed here -->
                    </ul>
                </form>
            </div>
        </div>
    </div>

    <div class="d-flex justify-content-center">
        <button id="process-files-btn" onclick="processFiles()" class="btn btn-primary" style="display:none;margin-bottom: 20px;">Compare</button>
    </div>

    <div id="console"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.3/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.3/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>
    <script type="text/javascript" charset="utf8" src="static/ats.js"></script>
</body>
