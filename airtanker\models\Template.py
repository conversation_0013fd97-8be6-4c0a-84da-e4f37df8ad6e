from dataclasses import dataclass
from typing import Optional, List, Dict, Any
import json


@dataclass
class Template:
    """
    Template model class for managing AI prompt templates.
    
    Attributes:
        id: Unique identifier for the template (None for new templates)
        name: Human-readable name for the template
        identifiers: List of identifier strings for template matching
        type: Template type/category (e.g., 'customer_timesheets')
        prompt: The AI prompt text
        response_schema: JSON schema defining expected response structure
    """
    name: str
    prompt: str
    response_schema: Dict[str, Any]
    type: Optional[str] = None
    identifiers: Optional[List[str]] = None
    id: Optional[int] = None
    
    def __post_init__(self):
        """Validate template data after initialization."""
        if not self.name or not self.name.strip():
            raise ValueError("Template name cannot be empty")
        
        if not self.prompt or not self.prompt.strip():
            raise ValueError("Template prompt cannot be empty")
        
        if not isinstance(self.response_schema, dict):
            raise ValueError("Response schema must be a dictionary")
        
        # Ensure identifiers is a list if provided
        if self.identifiers is not None and not isinstance(self.identifiers, list):
            raise ValueError("Identifiers must be a list")
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Template':
        """Create a Template instance from a dictionary."""
        return cls(
            id=data.get('id'),
            name=data.get('name', ''),
            identifiers=data.get('identifiers'),
            type=data.get('type'),
            prompt=data.get('prompt', ''),
            response_schema=data.get('response_schema', {})
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert template to dictionary representation."""
        return {
            'id': self.id,
            'name': self.name,
            'identifiers': self.identifiers,
            'type': self.type,
            'prompt': self.prompt,
            'response_schema': self.response_schema
        }
    
    def validate_response_schema(self) -> bool:
        """Validate that the response schema is a valid JSON schema structure."""
        try:
            # Basic validation - ensure it has required JSON schema properties
            if not isinstance(self.response_schema, dict):
                return False
            
            # Check for basic JSON schema structure
            if 'type' not in self.response_schema:
                return False
            
            return True
        except Exception:
            return False
    
    def get_identifiers_as_string(self) -> str:
        """Get identifiers as a comma-separated string for form display."""
        if not self.identifiers:
            return ""
        return ", ".join(self.identifiers)
    
    def set_identifiers_from_string(self, identifiers_str: str):
        """Set identifiers from a comma-separated string."""
        if not identifiers_str or not identifiers_str.strip():
            self.identifiers = None
        else:
            # Split by comma and clean up whitespace
            self.identifiers = [id.strip() for id in identifiers_str.split(',') if id.strip()]
    
    def get_response_schema_as_json_string(self) -> str:
        """Get response schema as a formatted JSON string."""
        try:
            return json.dumps(self.response_schema, indent=2)
        except Exception:
            return "{}"
    
    def set_response_schema_from_json_string(self, json_str: str):
        """Set response schema from a JSON string."""
        try:
            self.response_schema = json.loads(json_str)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in response schema: {str(e)}")
