// var customerSelectedFiles = [];
// var internalSelectedFiles = [];

// // Customer Methods
// function addFilesCustomer() {
//     var files = document.getElementById('file-upload-left').files;
//     for (var i = 0; i < files.length; i++) {
//         customerSelectedFiles.push(files[i]);
//     }
//     updateFileListCustomer();
//     document.getElementById('file-upload-left').value = ''; // Clear the current selection
// }

// function updateFileListCustomer() {
//     var output = document.getElementById('file-list-left');
//     output.innerHTML = '';
//     for (var i = 0; i < customerSelectedFiles.length; ++i) {
//         output.innerHTML += '<li class="list-group-item">' +
//                             '<i class="fas fa-file-alt"></i> ' +
//                             customerSelectedFiles[i].name +
//                             '<i class="fas fa-times" onclick="removeFileCustomer(' + i + ')"></i>' +
//                             '</li>';
//     }
//     updateVisibilityOfProcessButton();
// }

// function removeFileCustomer(index) {
//     customerSelectedFiles.splice(index, 1); // Remove the file from the array
//     updateFileListCustomer(); // Update the list
// }



// // Internal
// function addFilesInternal() {
//     var files = document.getElementById('file-upload-right').files;
//     for (var i = 0; i < files.length; i++) {
//         internalSelectedFiles.push(files[i]);
//     }
//     updateFileListInternal();
//     document.getElementById('file-upload-right').value = ''; // Clear the current selection
// }

// function updateFileListInternal() {
//     var output = document.getElementById('file-list-right');
//     output.innerHTML = '';
//     for (var i = 0; i < internalSelectedFiles.length; ++i) {
//         output.innerHTML += '<li class="list-group-item">' +
//                             '<i class="fas fa-file-alt"></i> ' +
//                             internalSelectedFiles[i].name +
//                             '<i class="fas fa-times" onclick="removeFileInternal(' + i + ')"></i>' +
//                             '</li>';
//     }
//     updateVisibilityOfProcessButton();
// }

// function removeFileInternal(index) {
//     internalSelectedFiles.splice(index, 1); // Remove the file from the array
//     updateFileListInternal(); // Update the list
// }


// function updateVisibilityOfProcessButton() {
//     // Check if both arrays have at least one file
//     if (internalSelectedFiles.length > 0 && customerSelectedFiles.length > 0) {
//         document.getElementById('process-files-btn').style.display = 'block'; // Show the button
//     } else {
//         document.getElementById('process-files-btn').style.display = 'none'; // Hide the button
//     }
// }

// // this is the upload actual. Might not need this portion. Uses Ajax but commented out.
// document.getElementById('upload-form').onsubmit = function(e) {
//     e.preventDefault();
//     var formData = new FormData();
//     for (var i = 0; i < selectedFiles.length; i++) {
//         formData.append('files[]', selectedFiles[i], selectedFiles[i].name);
//     }
//     // Here you would add an AJAX request to send formData to the server

//                 $('#upload-form').on('submit', function(e) {
//         e.preventDefault();
//         var formData = new FormData(this);
//         for (var i = 0; i < selectedFiles.length; i++) {
//             formData.append('files[]', selectedFiles[i], selectedFiles[i].name);
//         }
//         // $.ajax({
//         //     url: '/upload',  // The URL to your Flask route that handles the upload
//         //     type: 'POST',
//         //     data: formData,
//         //     contentType: false,
//         //     processData: false,
//         //     success: function(response) {
//         //         console.log('Files uploaded successfully');
//         //     },
//         //     error: function() {
//         //         console.log('An error occurred');
//         //     }
//         // });
//     });
// };




// // Compare button click
// function processFiles() {
//     let formData = new FormData();

//     internalSelectedFiles.forEach(file => {
//         formData.append('internalFiles[]', file.name);
//     });

//     customerSelectedFiles.forEach(file => {
//         formData.append('customerFiles[]', file.name);
//     });

//     fetch('/process-files', {
//         method: 'POST',
//         body: formData,
//     })
//     .then(response => {
//         if (response.ok) {
            
//         }
//     return response.json(); // Parse the JSON of the response
//     })
//     .then(data => console.log(data))
//     .catch(error => console.error('Error:', error));
// }

// function handleBrowseClickLeft(event) {
//     event.stopPropagation(); // Stop event propagation to prevent double triggering
//     document.getElementById('file-upload-left').click();
// }
// function handleBrowseClickRight(event) {
//     event.stopPropagation(); // Stop event propagation to prevent double triggering
//     document.getElementById('file-upload-right').click();
// }
document.addEventListener('DOMContentLoaded', function () {
    const loadingScreen = document.getElementById('loadingScreen');
    const mainContent = document.getElementById('mainContent');
  
    setTimeout(() => {
      loadingScreen.style.opacity = '0';
      setTimeout(() => {
        loadingScreen.style.display = 'none';
        mainContent.style.display = 'block';
        setTimeout(() => {
          mainContent.classList.add('fade-in');
        }, 10); // A small delay to ensure the display change is applied
      }, 500); // Match the CSS transition duration
    }, 2000); // Adjust this delay based on your loading time expectations
  });
  
