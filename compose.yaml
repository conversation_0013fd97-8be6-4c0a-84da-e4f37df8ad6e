services:
  # NGINX - Proxy and web server
  nginx:
    image: nginx:alpine
    restart: always
    ports:
      - 80:80
      - 443:443
    depends_on:
      - airtanker
    volumes:
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/cert:/app
    healthcheck:
      test: ["CMD-SHELL", "curl --silent --fail localhost:80/health-check || exit 1"]
      interval: 10s
      timeout: 10s
      retries: 3
  # Redis - Cache DB server
  redis:
    image: redis:latest
    ports:
      - 6379:6379
  # AirTanker DEV - Development web app using debugpy for VS Code debug
  airtanker-dev:
    hostname: airtanker-dev
    build:
      context: ./airtanker
      target: dev
    restart: always
    ports: 
      - '5001:5000'
      # - '5678:5678' # For VS Code debugging (comment out when not debugging/default/mode)
    environment:
      - PRODUCTION=false
    volumes:
      - ./airtanker:/home/<USER>/airtanker is the correct path to your code
      - pip-cache:/root/.cache/pip        # 2) persist pip cache across rebuilds
      - ./airtanker/.env.dev:/home/<USER>/.env:ro # 3) mount the .env file from .env.dev
    command: python app.py # For default/flask debugging (comment out when debugging with VS Code)
    # command: python -m debugpy --listen 0.0.0.0:5678 --wait-for-client -m flask run --host 0.0.0.0 --port 5000 --no-reload # For VS Code debugging (comment out when not debugging)
  # AirTanker PROD - Production web app
  airtanker:
    hostname: airtanker
    build:
      context: ./airtanker
      target: prod
    restart: always
    ports: 
      - '5000:5000'
    environment:
      - PRODUCTION=true
    #volumes:
      #- python-packages:/usr/local/python-packages
    healthcheck:
      test: ["CMD-SHELL", "curl --silent --fail localhost:8000/flask-health-check || exit 1"]
      interval: 10s
      timeout: 10s
      retries: 3
    command: gunicorn -k eventlet -w 4 -t 360 -b 0.0.0.0:5000 app:airtanker_app
    # command explanation:
    # gunicorn - Gunicorn is a Python WSGI HTTP server for UNIX. It's a pre-fork worker model, 
    #            meaning it starts a master process and then forks off multiple worker processes.
    #     -k eventlet - Use the eventlet worker class. Eventlet is a networking library that uses epoll or kqueue for scalable network applications.
    #     -w 4 - Start 4 worker processes. This can be adjusted based on the number of CPU cores available.
    #     -t 360 - Set the timeout to 360 seconds. This is the maximum amount of time a worker process can spend on a single request before it's killed and restarted.
    #     -b 0.0.0.0:5000 - Bind to all network interfaces on port 5000.
    #     app:airtanker_app - The module and application to run. In this case, it's the app module and the airtanker_app application.

volumes:
  pip-cache: