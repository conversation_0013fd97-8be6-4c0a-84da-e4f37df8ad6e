// Enable Bootstrap ToolTips
const spinnerModal = new bootstrap.Modal('#spinnerModal', {
    backdrop: 'static',
    keyboard: false 
});
const detailsModal = new bootstrap.Modal('#detailsModal', {});
const detailsDailyModal = new bootstrap.Modal('#detailsModalDaily', {});
const hourSelectModal = new bootstrap.Modal('#hoursSelectionModal', {
    backdrop: 'static', // Disable the backdrop
    keyboard: false // Allow closing the modal with keyboard ESC key
});

const headerTooltips = document.querySelectorAll('.static-tt');
headerTooltips.forEach(t => {
    new bootstrap.Tooltip(t, {
        trigger: 'hover'
    })
});

document.addEventListener('DOMContentLoaded', (event) => {

    $( '#divDailyTimesheets' ).hide();
    $( '#divWeeklyTimesheets' ).hide();
    
});

function reinitTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('.dyn-tt'));
    var tooltipList = tooltipTriggerList.map( function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            trigger: 'hover'
        });
    });
}

function GetWeekEnding(){
    return $('#weekEndingPicker').val();
}

function fetchInitialData() {
    spinnerModal.show();
    $( '#divWeeklyTimesheets' ).hide();

    let selectedWeekEnding = document.getElementById('weekEndingPicker').value;
    console.log(selectedWeekEnding);
    
    $('#employeeWeeklyDetailsTable').DataTable().destroy();
    $('#employeeDetailsTable').DataTable().destroy();    

    fetch(`/api/employee_details_daily?weekEnding=${selectedWeekEnding}`)
    .then(response => response.json())
    .then(dataArray => {
        let table = $('#employeeDetailsTable').DataTable({
            data: dataArray,
            columns: [
                { data: 'Date' },
                { data: 'FirstName' },
                { data: 'LastName' },
                { data: 'WorkOrderNumber' },
                { data: 'CustomerName' },
                {
                    data: 'ADPReportedHours',
                    visible: false
                },
                { data: 'EmployeeReportedHours' },
                { data: 'CustomerReportedHours' },
                {
                    data: 'Notes',
                    render: function (data, type, row) {
                        return getDailyErrors(row);
                    }
                },
                {
                    data: 'ErrorIndicator',
                    visible: false
                }
            ],
            order: [
                [9, "desc"],
                [3, "asc"],
                [0, "asc"]
            ],
            createdRow: (row, data, index) => {
                if (data.ErrorIndicator == 1) {
                    row.classList.add('table-warning');
                }
            }
        });
        //table.clear();
        console.log("table created");

        $( '#employeeDetailsTitle' ).empty();
        $( '#employeeDetailsTitle' ).append(`<h4>Daily Entries for Week Ending: ${selectedWeekEnding}</h4>`)

        // Event listener for when a row is clicked
        table.on('click', 'tr', function() {
            let rowData = table.row(this).data();
            if (rowData) {
                // Fetch additional data based on the clicked row
                fetchAdditionalData(rowData.TimeSheetEntryID)
                    .then(moreData => {
                        // Populate the additional details table
                        populateAdditionalTable(moreData);

                        // Open the modal
                        detailsModal.show();
                        console.log('Modal opened');
                    })
                    .catch(error => {
                        console.error('Error fetching additional data:', error);
                    }
                );
            }
        });

        $( '#divDailyTimesheets' ).show();
    })
    .catch(error => console.error('Error:', error))
    .finally(() => {
        spinnerModal.hide();
    })
}

function getDailyErrors(data) {
    var adpHours = data.ADPReportedHours;
    var eHours = data.EmployeeReportedHours;
    var cHours = data.CustomerReportedHours;
    var notes = ''

    if ((!eHours || eHours == 0) && (!adpHours || adpHours == 0)) {
        data.ErrorIndicator = 1;
        notes = "Missing Hours. Please upload employee timesheet (ADP / Contractor).\n";                
    }
    if (!cHours || cHours == 0) {
        data.ErrorIndicator = 1;
        notes = "Missing Hours. Please upload customer timesheet (ATS, KUKA, HTI, etc.).\n";                
    }

    if (eHours != 0 || adpHours != 0) {
        let difference = 0;
        if (eHours != 0) {
            if (Math.abs(cHours - eHours) <= 0.031) {                
                if (difference !== 0) {
                    data.ErrorIndicator = 1;
                    notes += "ADP hours don't match customer's, but they meet the tolerance spec <= 0.03.\n";
                }
            }
        }
        
        if (adpHours != 0) {
            if (Math.abs(cHours - adpHours) <= 0.031) {
                if (difference !== 0) {
                    data.ErrorIndicator = 1;
                    notes += "Internal hours don't match customer's, but they meet the tolerance spec <= 0.03.\n";
                }
            }
        }
    }

    var notesClass = '';

    return `<p ${notesClass}>${notes}</p>`
}

function fetchWeeklyData() {
    // Check to see if the weekly table is already created and save some time
    var weeklyDiv = $( '#divWeeklyTimesheets' );
    if (weeklyDiv.is(":visible")) {
        refreshWeeklyTable();
        return;
    }

    $( '#divDailyTimesheets' ).hide();
    weeklyDiv.show();
    
    $('#employeeWeeklyDetailsTable').DataTable().destroy();
    let table = $('#employeeWeeklyDetailsTable').DataTable({
        ajax: {
            url: '/api/employee_details_weekly',
            data: function(d) {
                d.weekEnding = GetWeekEnding();
            },
            dataSrc: ''
        },
        //data: dataArray,
        columns: [
            { data: 'FirstName' },
            { data: 'LastName' },
            { data: 'WorkOrderNumber' },
            {
                data: 'ADPReportedHours',
                visible: false
            },
            { data: 'InternalReportedHours' },
            { data: 'TotalCustomerReportedHours' },
            { data: 'ApprovedHours' },
            {
                render: (data, type, row) =>  {
                    return getActionButtons(row);
                }
            },
            {
                render: (data, type, row) => {
                    return getWeeklyErrors(row);
                }
            },
            {
                data: 'Status',
                render: (data, type, row) => {
                    if (!row.ExpensesUploaded || row.ExpensesUploaded == 0) {
                        return data + '<i class="ri-error-warning-fill warning-icon"></i>';
                    }
                    else {
                        return data;
                    }
                }
            }
        ],
        rowCallback: (row, data) => {
            //console.log(`Drawing classes for ${data.FirstName} ${data.LastName}`);
            setRowClasses(data, row);
        },
        pageLength: 25
    });

    // Handle row clicks. Show daily details unless it is the action buttons
    table.on('click', 'tr', function(e) {
        let parentRow = e.target.closest('tr');
        let rowData = table.row(parentRow).data();
    
        // Check if any action button was clicked (including export)
        if (e.target && (e.target.classList.contains('status-action-btn') || 
                        e.target.closest('.status-action-btn') ||
                        e.target.classList.contains('button-pdf-export') || 
                        e.target.closest('.button-pdf-export'))) {
            e.stopPropagation();
            
            // Get the button that was clicked (or its parent if we clicked on an icon inside the button)
            let button = e.target.closest('.status-action-btn') || e.target.closest('.button-pdf-export');
            if (button) {
                var action = button.dataset.bsTitle;
                if (action && action.includes('Approve')) {
                    approveDenyTimesheet(parentRow, rowData, 3);
                } else if (action && action.includes('Deny')) {
                    approveDenyTimesheet(parentRow, rowData, 4);
                } else if (action && action.includes('Export')) {
                    exportTimesheet(rowData);
                }
            }
            return; // Stop processing this click event further
        }
    
        // If it wasn't an action button, proceed to show daily details
        if (rowData) {
            var timesheetID = rowData.TimeSheetID;
    
            // Add loading class
            $(parentRow).addClass('row-loading');
    
            // Fetch additional data based on the clicked row
            fetchAdditionalDataDaily(timesheetID)
                .then(moreData => {
                    // Populate the additional details table
                    populateAdditionalDailyTable(moreData);
                    // Open the modal
                    detailsDailyModal.show();
                })
                .catch(error => {
                    console.error('Error fetching additional data:', error);
                    // Optionally display an error message to the user
                    alert('Failed to load daily details. Please try again.');
                })
                .finally(() => {
                    // Remove loading class regardless of success or failure
                    $(parentRow).removeClass('row-loading');
                });
        }
    });
        
    table.on('draw', function () {
        reinitTooltips();
    })

    // Search by status dropdown
    $.fn.dataTable.ext.search.push(
        function(settings, data, dataIndex) {
            let selectedStatuses = [];
            $('.status-checkbox:checked').each(function() {
                selectedStatuses.push($(this).val());
            });

            let status = data[9]; // Assuming status is the 10th column (index 9)

            if (selectedStatuses.length === 0 || selectedStatuses.includes(status)) {
                return true;
            }
            return false;
        }
    );
    
    // Event listener for the status checkboxes
    $('.status-checkbox').on('change', function() {
        table.draw();
    });
}

function getActionButtons(data) {
    var approveButton = `
        <button class="btn btn-success dyn-tt status-action-btn" data-bs-toggle="tooltip" data-bs-title="Approve Hours">
            <i class="button-icon ri-checkbox-circle-fill"></i>
            <span class="action-spinner spinner-border spinner-border-sm" role="status" style="display: none;"></span>
        </button>
    `;
    var denyButton = `
        <button class="btn btn-danger dyn-tt status-action-btn" data-bs-toggle="tooltip" data-bs-title="Deny Hours">
            <i class="button-icon ri-close-circle-fill"></i>
            <span class="action-spinner spinner-border spinner-border-sm" role="status" style="display: none;"></span>
        </button>
    `;
    var exportButton = `
        <button class="btn btn-primary dyn-tt button-pdf-export status-action-btn disabled-button" data-bs-toggle="tooltip" data-bs-title="Export Hours to PDF Timesheet">
            <i class="button-icon ri-download-fill"></i>
            <span class="action-spinner spinner-border spinner-border-sm" role="status" style="display: none;"></span>
        </button>
    `;

    return approveButton + denyButton + exportButton;
}

function getWeeklyErrors(row) {
    let errorIndicatorValue = true;
    let notes = '';

    let eHours = row.InternalReportedHours == null ? 0 : row.InternalReportedHours;
    let cHours = row.TotalCustomerReportedHours == null ? 0 : row.TotalCustomerReportedHours;
    let adpHours = row.ADPReportedHours == null ? 0 : row.ADPReportedHours;
    let approvedHours = row.ApprovedHours == null ? 0 : row.ApprovedHours;

    if (eHours == 0 && adpHours == 0){
        notes = "Missing Hours. Please upload employee timesheet (ADP / Contractor).\n";                
    }

    if (cHours == 0){
        notes = "Missing Hours. Please upload customer timesheet (ATS, KUKA, HTI, etc.).\n";                
    }

    if (eHours != 0 || adpHours != 0) {
        let difference = 0;
        if (eHours != 0) {
            if (Math.abs(cHours - eHours) <= 0.031) {
                errorIndicatorValue = false;
                if (difference !== 0) {
                    notes += "ADP hours don't match customer's, but they meet the tolerance spec <= 0.03.\n";
                }
            }
        }
        
        if (adpHours != 0) {
            errorIndicatorValue = true;
            if (Math.abs(cHours - adpHours) <= 0.031) {
                errorIndicatorValue = false;
                if (difference !== 0) {
                    notes += "Internal hours don't match customer's, but they meet the tolerance spec <= 0.03.\n";
                }
            }
        }
    }

    if (cHours >= 40 || eHours >= 40  || adpHours >= 40) {
        notes += "Logged 40+ hours this week.\n";
    }

    return `<p data-haserror="${errorIndicatorValue}">${notes}</p>`
}

function setRowClasses(row, addedRow) {   
    let notesCell = $( addedRow ).find('p').first();
    let hasError = $( notesCell ).data("haserror");
    //console.log(`Assigning row classes: Original [${addedRow.className}] - Adding for [${row.Status}]`)
    if (row.Status == "Exported") {
        $(addedRow).addClass('gray-out-row');
        $(addedRow).addClass('table-secondary');
        $(addedRow).find('.status-action-btn')
            .removeClass('btn-success btn-danger status-action-btn')
            .addClass('disabled-button');
        $(addedRow).find('.button-pdf-export')
            .removeClass('disabled-button');
            // .addClass('status-action-btn');
    } else if (row.Status == "Approved With Errors") {
        $(addedRow).addClass('table-success');
    } else if (row.Status == "Denied With Errors") {
        $(addedRow).addClass('table-danger');
    } else if (row.Status == "Denied") {
        $(addedRow).addClass('table-danger');
    } else if (row.Status == "Approved") {
        $(addedRow).addClass('table-success');
    } else if (hasError == true) {
        $(addedRow).addClass('table-warning');
    } else {
        // $(addedRow).addClass('table-secondary');
    }
}

function approveDenyTimesheet(parentRow, row, statusId) {
    // Show spinner and hide icon on the clicked button
    const button = $(parentRow).find(statusId === 3 ? '.btn-success' : '.btn-danger');
    const spinner = button.find('.action-spinner');
    const icon = button.find('.button-icon');
    
    button.prop('disabled', true);
    icon.hide();
    spinner.show();

    let notesCell = $(parentRow).find('p').first();
    let hasError = $(notesCell).data("haserror");
    if (row == null) {
        console.log('Approve/Deny timesheet on null row identifier!');
        return;
    }

    let timeSheetId = row.TimeSheetID;
    let adpHours = row.ADPReportedHours;
    let eHours = row.InternalReportedHours;
    let cHours = row.TotalCustomerReportedHours;
    let hours = null;

    if (hasError) {
        if (statusId == 3) {
            // Reset button state before showing modal
            button.prop('disabled', false);
            spinner.hide();
            
            console.log("Showing popup now");
            $('#approveModal').data('timesheetId', timeSheetId);
            $('#approveModal').data('statusId', 6);
            openHoursSelectionModal(eHours, cHours, adpHours, timeSheetId, 6, 'approve');
            return;
        }
        else if (statusId == 4) {
            console.log("status was 4 and had error");
            statusId = 5;
        }
    } else {
        hours = cHours;
    }

    let sourceType = null;
    if (statusId == 3) {
        if (adpHours > 0) {
            sourceType = "adpHours";
        }
        else if (eHours > 0) {
            sourceType = "eHours";
        }
    }

    updateStatus(timeSheetId, statusId, hours, sourceType)
        .then(data => {
            console.log(data);
            if (data) {
                refreshWeeklyTable();
            }
        })
        .catch(error => {
            console.error('Update status failed:', error);
        })
        .finally(() => {
            // Reset button state
            button.prop('disabled', false);
            spinner.hide();
            icon.show();
        });
}

function exportTimesheet(data) {
    var adpHours = 0;
    var eHours = data.InternalReportedHours;
    var cHours = data.TotalCustomerReportedHours;

    openHoursSelectionModal(eHours, cHours, adpHours, data.TimeSheetID, 6, 'export');
}

function updateStatusCell(rowData, statusId) {
    let statusText = 'Unknown';
    if (rowData) {
        switch (statusId){
            case 3:
                statusText = 'Approved';
                break;
            case 4:
                statusText = 'Denied';
                break;
            case 5:
                statusText = 'Denied with Errors';
                break;
            case 6:
                statusText = 'Approved with Errors';
        }

        rowData.Status = statusText;
    }
};

function refreshWeeklyTable() {
    //console.log('Refreshing weekly table...');
    $('#employeeWeeklyDetailsTable').DataTable().ajax.reload();
}

function download_pdf(timesheet_id, selectedHoursType) {
    console.log("get_pdfs function called to download pdf.");
    hourSelectModal.hide();
    spinnerModal.show();
    
    return fetch(`/create_single_pdf/${timesheet_id}/${selectedHoursType}`)
        .then(response => {
            console.log("Response status:", response.status); // Log response status
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(pdfData => {
            if (pdfData.error) {
                throw new Error(pdfData.error);
            }

            const downloadFile = (url) => {
                return new Promise((resolve) => {
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = url.split('/').pop();
                    document.body.appendChild(a);
                    console.log("Triggering download for:", url);
                    a.click();
                    document.body.removeChild(a);
                    setTimeout(resolve, 500);
                });
            };

            console.log(`Downloading ${pdfData.pdf_path}`);
            return downloadFile(pdfData.pdf_path);
        })
        .catch(err => {
            console.error('Error fetching PDF paths:', err); // Log any errors that occur during the fetch process
        })
        .finally(() => {
            spinnerModal.hide();
        });
}

function openHoursSelectionModal(eHours, cHours, adpHours, timesheetId, statusId, action) {
    $( '#saveSelectedHours' ).attr('data-action', action);
    $( '#saveSelectedHours' ).prop('disabled', true);
    
    // Clear existing options
    const form = document.getElementById('hoursSelectionForm');
    form.innerHTML = '';

    // Save timesheetId and statusId in the form for later access
    form.dataset.timesheetId = timesheetId;
    form.dataset.statusId = statusId;

    // Mapping for custom labels
    const labelsMapping = {
        adpHours: "ADP",
        eHours: "Internal"
    };

    // Create radio options dynamically
    const hoursTypes = {adpHours, eHours};
    for (const [type, value] of Object.entries(hoursTypes)) {
        const label = document.createElement('label');
        label.classList.add('form-check-label');

        const input = document.createElement('input');
        input.type = 'radio';
        input.classList.add('form-check-input');
        input.name = 'approvedHours';
        input.value = type;
        input.dataset.hoursValue = value; // Store the hours value in dataset for easy access
        
        // Event listener to enable the Save Changes button when a selection is made
        input.addEventListener('change', () => {
            document.getElementById('saveSelectedHours').disabled = false;
        });

        // Use the custom label from mapping
        const customLabel = labelsMapping[type] || type;

        label.appendChild(input);
        label.appendChild(document.createTextNode(`${customLabel}: ${value}`));

        const div = document.createElement('div');
        div.classList.add('form-check');
        div.appendChild(label);

        form.appendChild(div);
    }

    hourSelectModal.show();
}

function hoursSelected() {
    var selectedHoursType = document.querySelector('input[name="approvedHours"]:checked').value;
    var selectedHoursValue = document.querySelector('input[name="approvedHours"]:checked').dataset.hoursValue;
    var form = document.getElementById('hoursSelectionForm');
    var timesheetId = form.dataset.timesheetId;

    var action = $( '#saveSelectedHours' ).attr('data-action')
    if (action === 'export') {
        download_pdf(timesheetId, selectedHoursType);
    } else {
        console.log(`Selected hours type: ${selectedHoursType}, value: ${selectedHoursValue}`);
        var statusId = form.dataset.statusId;

        console.log('Saving approved hours:', selectedHoursValue, 'for timesheetId:', timesheetId, 'with statusId:', statusId);
        updateStatus(timesheetId, statusId, selectedHoursValue, selectedHoursType)
        .then(data => {
            if (data) {
                refreshWeeklyTable();
            }
        })
        .finally(() => {                
            hourSelectModal.hide();
        });
    }
}

function updateStatus(timeSheetId, statusId, selectedHours, selectedHoursType) {
    return fetch(`/api/update_status/timesheets`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `timesheetID=${encodeURIComponent(timeSheetId)}&statusID=${encodeURIComponent(statusId)}&hours=${encodeURIComponent(selectedHours)}&type=${encodeURIComponent(selectedHoursType)}`
    })
    .then(response => {
        if (!response.ok) {
            // If the response is not ok, throw an error to be caught by the catch block
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .catch(error => {
        // Handle fetch errors
        alert('Error: ' + error.message); // Make sure to use error.message to get the actual error message
        throw error; // Rethrow the error if you want to chain a catch outside of this function
    });
};

function fetchAdditionalData(timesheetEntryID) {
    return new Promise((resolve, reject) => {
        fetch(`/api/employee_details_allEntries?timesheetEntryID=${timesheetEntryID}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => resolve(data))
            .catch(error => reject(error));
    });
}

function populateAdditionalTable(data) {
    $('#additionalDetailsTable').DataTable().destroy();
    let additionalTable = $('#additionalDetailsTable').DataTable({
        data: data,
        columns: [
            { data: 'Date' },
            { data: 'Source' },
            { data: 'FirstName' },
            { data: 'LastName' },
            { data: 'WorkOrderNumber' },
            { data: 'ReportedHours' },
            { data: 'TaskName' },
            { data: 'FileName' }
        ],
        ordering: false,
        paging: false,
        searching: false
    });
}

function fetchAdditionalDataDaily(timesheetID) {
    return new Promise((resolve, reject) => {
        fetch(`/api/employee_details_daily_by_timesheetID?timesheetID=${timesheetID}`)
        .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => resolve(data))
            .catch(error => reject(error));
    });
}

function populateAdditionalDailyTable(data) {
    // Assuming additionalTable is already initialized
    $('#additionalDetailsTableDaily').DataTable().destroy();
    let table = $('#additionalDetailsTableDaily').DataTable({
        data: data,
        columns: [
            { data: 'Date' },
            { data: 'FirstName' },
            { data: 'LastName' },
            { data: 'WorkOrderNumber' },
            { data: 'CustomerName' },
            {
                data: 'ADPReportedHours',
                visible: false
            },
            { data: 'EmployeeReportedHours' },
            { data: 'CustomerReportedHours' },
            {
                data: 'Notes',
                render: function (data, type, row) {
                    return getDailyErrors(row);
                }
            },
            {
                visible: false,
                data: 'ErrorIndicator'
            }
        ],        
        order: [
            [0, "asc"]  // "Error" column is at index 3 (zero-based), sorting in descending order
        ],
        paging: false,
        ordering: false,
        searching: false
    });
}
