function startProgress(url) {
    const evtSource = new EventSource(url);
    const progressBar = document.getElementById("progress-bar");
    const progress_container = document.getElementById("progress-container");
    progress_container.style.display = "block";

    evtSource.onmessage = function(event) {
        const data = event.data;
        try {
            if (data === "Completed" || data === "Failed") {
                animateProgress(data === "Completed" ? 100 : 0); // Animate to 100% or reset to 0%
                progressBar.textContent = data;
                evtSource.close();
                setTimeout(() => {
                    window.location.reload();  // Reload the page or redirect
                }, 1000);  // Wait 1 second before refreshing
            } else {
                const newProgress = Math.round(parseFloat(data));  // Convert to float and round
                if (isNaN(newProgress)) {
                    throw new Error(`Invalid progress value: ${data}`);
                }
                animateProgress(newProgress);  // Animate progress update
            }
        } catch (error) {
            console.error("Error processing SSE data:", error);
            progressBar.textContent = "Error!";
            evtSource.close();  // Close the event source on errors
        }
    };

    evtSource.onerror = function(event) {
        console.error("SSE failed:", event);
        progressBar.textContent = "Error!";
        evtSource.close();  // Close the event source on errors
    };

    let queuedProgress = null;

    function animateProgress(targetPercentage) {

        if (progressBar.hasAttribute('aria-valuenow')) {
            progressBar.setAttribute('aria-valuenow', targetPercentage);
            $('#progress-bar .progress-bar').css('width', targetPercentage + '%');
            return;
        }

        if (queuedProgress !== null) {
            clearInterval(queuedProgress.animation);
            queuedProgress = null;
        }

        const currentPercentage = parseFloat(progressBar.style.width) || 0;
        if (currentPercentage === targetPercentage) return;  // No change needed

        const diff = Math.abs(targetPercentage - currentPercentage);
        const increment = diff > 10 ? 0.5 : 0.1;

        const animation = setInterval(() => {
            let currentWidth = parseFloat(progressBar.style.width) || 0;
            if ((targetPercentage > currentWidth && currentWidth + increment >= targetPercentage) ||
                (targetPercentage < currentWidth && currentWidth - increment <= targetPercentage)) {
                progressBar.style.width = targetPercentage + '%';
                progressBar.textContent = targetPercentage + '%';
                clearInterval(animation);
                queuedProgress = null;
            } else {
                currentWidth += (targetPercentage > currentWidth ? increment : -increment);
                progressBar.style.width = currentWidth + '%';
                progressBar.textContent = Math.round(currentWidth) + '%';
            }
        }, 10);

        queuedProgress = { target: targetPercentage, animation: animation };
    }
}
