$(function () {
    const $flash = $('#flash-message-container');
    if ($flash.length) {
      setTimeout(() => $flash.fadeOut('slow', () => $flash.remove()), 2000);
    }
  
    const table = $('#filesTable').DataTable({
      ajax: { url: '/get-processed-files', dataSrc: 'data' },
      fixedColumns: { start: 2 },
      select: { style: 'multi' },
      columns: [
        { orderable: false, render: DataTable.render.select() },
        { data: 'FileID' },
        { data: 'FileName' },
        { data: 'SheetName' },
        { data: 'SourceType' },
        { data: 'DateProcessed' }
      ],
      order: [[1, 'desc']],
      paging: false,
      scrollY: 'calc(100vh - 300px)',
      scrollCollapse: true
    });

    const $openBtn = $('#openDeleteModal');

    // initially disable it
    $openBtn.prop('disabled', true);

    // whenever selection changes, enable if at least one row is selected
    table.on('select deselect', () => {
    const hasSelection = table.rows({ selected: true }).count() > 0;
    $openBtn.prop('disabled', !hasSelection);
    });
  
    $('#confirmDeleteModal').on('show.bs.modal', function () {
      $('#deleteSpinner').hide();
      $('#deleteButtonText').text('Delete');
      $('#deleteConfirmed').prop('disabled', false);
  
      const names = table
        .rows({ selected: true })
        .data()
        .pluck('FileName')
        .toArray();
  
      if (!names.length) {
        return alert('No files selected for deletion.');
      }
  
      $('#fileListToDelete')
        .empty()
        .append(names.map(n => `<li>${n}</li>`).join(''));
    });
  
    $('#deleteConfirmed').click(async function () {
      const $btn = $(this);
      $btn.prop('disabled', true);
      $('#deleteSpinner').show();
      $('#deleteButtonText').text('Deleting...');
  
      try {
        const ids = table
          .rows({ selected: true })
          .data()
          .pluck('FileID')
          .toArray();
        const res = await fetch('/edit-files', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ fileIDs: ids })
        });
        if (!res.ok) throw new Error('Deletion failed');
      } catch (e) {
        alert('Error deleting files. Please try again.');
        $('#deleteSpinner').hide();
        $('#deleteButtonText').text('Delete');
        $btn.prop('disabled', false);
        return;
      }
  
      $('#confirmDeleteModal').modal('hide');
      table.destroy();
      location.reload();
    });
  });
  