{% extends 'wizard-base.html' %}

{% block styles %}
<style>
    #progressBar div {
        animation-name: loadProgress;
        animation-duration: 2s; /* Customize this value */
        animation-fill-mode: forwards; /* Keeps the state at 66% after animation */
    }
    
    @keyframes loadProgress {
        from {
            width: 1%;
        }
        to {
            width: 33%;
        }
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    // Date Picker
    document.addEventListener('DOMContentLoaded', function () {
        setMostRecentSunday();
    });

    function setMostRecentSunday() {
        const selectedWeekEnding = '{{ selected_week_ending }}';
        let formattedDate;

        if (selectedWeekEnding != 'False' && selectedWeekEnding != '') {
            formattedDate = selectedWeekEnding;
        } else {
            const today = new Date();
            const dayOfWeek = today.getDay(); // Sunday - 0, Monday - 1, ..., Saturday - 6
            const difference = dayOfWeek % 7; // Calculate difference to get back to the previous Sunday
            const mostRecentSunday = new Date(today.setDate(today.getDate() - difference));
            
            // Format the date as YYYY-MM-DD
            formattedDate = mostRecentSunday.toISOString().split('T')[0];
        }
        
        document.getElementById('weekEndingPicker').value = formattedDate;
    }

    function setDateField(value) {
        date = new Date(value);
        if (checkSunday(date)){
            const formattedDate = date.toISOString().split('T')[0];
            document.getElementById('weekEndingPicker').value = formattedDate;
        }
    }

    function checkSunday(selectedDate) {
        console.log(selectedDate);
        if (selectedDate.getDay() !== 6) {
            alert('Please select a Sunday.');
            setMostRecentSunday(); // Reset to the most recent Sunday if the check fails
            return false;
        }
        else{
            return true;
        }
    }
</script>
<script type="text/javascript">
    let selectedFiles = [];

    $( document ).ready(function() {
        $( "#stepLabel" ).append("<p>Step 1: Import Paycor Timesheets</p>");
        $( "#stepProgress" ).attr("aria-valuenow", "33")
    });

    function continueImport() {
        loading();

        const formData = new FormData();
        formData.append('week_ending_date', $( "#weekEndingPicker" ).val());
        formData.append('type', 'internal');
        selectedFiles.forEach(file=> {
            formData.append(`uploaded_files[]`, file)
        });

        sessionStorage.removeItem('errors');
        fetch('/import_paycor_timesheets', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if(data.errors || data.nameErrors) {
                // process errors
                processErrors(data.errors, data.name_errors, data.redirect_url, data.fileIds);
                console.log("Done Processing");
            }
            else if(data.redirect_url) {
                window.location.href = data.redirect_url;
            }
        })
        .finally(() => {
            // $("#loading").hide();
            // $("#content").show();
        })
    }

    function skipImport() {
        let weekEnding = $( "#weekEndingPicker" ).val();
        window.location.href = "/export_fixed_timesheets?week_ending=" + encodeURIComponent(weekEnding);
    }
    
    function addFiles() {
        var files = $( '#file-upload' )[0].files;
        for (var i = 0; i < files.length; i++) {
            selectedFiles.push(files[i]);
        }

        updateFileList();
        document.getElementById('file-upload').value = ''; // Clear the current selection
    }

    function updateFileList() {
        var output = $( '#file-list' );
        output.html('');
        for (var i = 0; i < selectedFiles.length; ++i) {
            output.append('<li class="list-group-item d-flex justify-content-between">' +
                                '<i class="ri-file-excel-2-line"></i>' +
                                '<p>' + selectedFiles[i].name + '</p>' +
                                '<i class="ri-close-line text-danger" onclick="removeFile(' + i + ')"></i>' +
                                '</li>');
        }        
        
        updateCanContinue();
    }

    function removeFile(index) {
        selectedFiles.splice(index, 1); // Remove the file from the array
        updateFileList(); // Update the list
    }

    function updateCanContinue() {
        var canContinue = selectedFiles.length > 0 && $( "#weekEndingPicker" ).val();
        if (canContinue) {
            $( '#continue' ).prop('disabled', false);
            $( '#continue' ).removeClass('btn-secondary');
            $( '#continue' ).addClass('btn-primary');
        }
        else {
            $( '#continue' ).prop('disabled', true);
            $( '#continue' ).removeClass('btn-primary');
            $( '#continue' ).addClass('btn-secondary');
        }
    }

</script>
{% endblock %}

{% block content %}
<div id="formContent">
    <h4 class="text-gray-900 my-4 fadeIn">Import Fixed-Bid Timesheets</h4>
    <div class="date-picker-container">
        <label for="weekEndingPicker">Select a Week Ending:</label>
        <br>
        <input type="date" id="weekEndingPicker" name="weekEndingPicker" class="date-picker" onchange="setDateField(this.value);">
    </div>
    <div class="border-secondary rounded m-3" style="border: 2px dashed;"
        onclick="document.getElementById('file-upload').click()">
        <i class="ri-upload-cloud-2-line fs-1"></i>
        <br />
        <label for="file-upload">Upload Paycor Export File(s):</label>
    </div>
    <input type="file" id="file-upload" name="uploaded_files[]" multiple accept=".xlsx" style="display: none;" onchange="addFiles()"/>
    <ul class="list-group mt-2" id="file-list"></ul>
    <button class="btn btn-secondary my-4" id="continue" disabled onclick="continueImport()">Continue</button>
    <div id="formFooter">        
        <a class="underlineHover fadeIn second" onclick="skipImport()" href="#">Skip</a>
    </div>
    <div id="formFooter">        
        <a class="underlineHover fadeIn third" href="/">Cancel</a>
    </div>
</div>
{% endblock %}

{% block modal %}

{% endblock %}