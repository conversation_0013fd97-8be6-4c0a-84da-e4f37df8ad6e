#!/usr/bin/env python3
"""
Test script for GeminiService
"""

import sys
import os

# Add the current directory to Python path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from services.GeminiService import GeminiService
    print("✅ GeminiService imported successfully")
    
    # Test initialization
    gemini_service = GeminiService()
    print("✅ GeminiService initialized successfully")
    
    # Test simple content generation
    test_prompt = "Hello! This is a test of the Gemini AI integration for AirTanker timesheet processing."
    print(f"\n🤖 Testing with prompt: {test_prompt}")
    
    # Test the print_stream_response method
    gemini_service.print_stream_response(test_prompt)
    
    print("\n✅ Test completed successfully!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure the google-genai package is installed:")
    print("pip install google-genai")
    
except Exception as e:
    print(f"❌ Error: {e}")
    print("Check your GOOGLE_GEMINI_API_KEY environment variable")
