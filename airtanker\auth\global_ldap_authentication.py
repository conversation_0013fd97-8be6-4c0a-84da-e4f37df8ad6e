from ldap3 import Server, Connection, ALL, SUBTREE, ALL_ATTRIBUTES
from ldap3.core.exceptions import LDAPException, LDAPBindError
import os
from dotenv import load_dotenv
from nameparser import Human<PERSON>ame
from main import airtanker_app

def global_ldap_authentication(user_name, user_pwd):
    """
      Function: global_ldap_authentication
       Purpose: Make a connection to encrypted LDAP server.
       :params: ** Mandatory Positional Parameters
                1. user_name - LDAP user Name
                2. user_pwd - LDAP User Password
       :return: None
    """
    load_dotenv()    

    ldap_user_name = user_name.strip() if user_name else ''
    ldap_user_pwd = user_pwd.strip() if user_pwd else ''
    
    if os.getenv('PRODUCTION', 'True').lower() == 'false':
        if ldap_user_name == '1':
            return 'finances', "DevUser"
    # ldap server hostname and port
    ldsp_server = os.getenv("LDAPADDRESS")

    server = Server(ldsp_server, get_info=ALL)

    connection = Connection(server,
                            user=ldap_user_name,
                            password=ldap_user_pwd)
    name = None
    if connection.bind():
        # Special users with hardcoded access
        if ldap_user_name in ["<EMAIL>", "<EMAIL>"]:
            group_name = "timekeeping"
            root_dn = "dc=atomtech,dc=local"
            search_filter = f'(&(objectClass=user)(mail={ldap_user_name}))'

            # Perform the search to find the user
            connection.search(root_dn, search_filter, SUBTREE, attributes=ALL_ATTRIBUTES)
            if not connection.entries:
                return False, None  # User not found

            user_entry = connection.entries[0]
            dn = user_entry.entry_dn
            components = dn.split(',')

            # Look for the CN= component and extract the name
            cn = ldap_user_name
            for component in components:
                if component.startswith('CN='):
                    cn = component[3:]  # Skip the 'CN=' part
                    break
            name = HumanName(cn).first
            return 'finances', name
        else:
            group_name = "timekeeping"    
            root_dn = "dc=atomtech,dc=local"
            search_filter = f'(&(objectClass=user)(mail={ldap_user_name}))'

            # Perform the search to find the user
            connection.search(root_dn, search_filter, SUBTREE, attributes=ALL_ATTRIBUTES)

            if not connection.entries:
                l_success_msg = f' ** User not found in LDAP server.'
                return l_success_msg, None

            user_entry = connection.entries[0]

            # Check if the user is a member of the specified group
            v = any(group_name.lower() in group.lower() for group in user_entry['memberOf'].values)
            if v:
                dn = user_entry.entry_dn
                components = dn.split(',')

                # Look for the CN= component and extract the name
                cn = ldap_user_name
                for component in components:
                    if component.startswith('CN='):
                        cn = component[3:]  # Skip the 'CN=' part
                        break

                name = HumanName(cn).first
                l_success_msg = 'timekeeping'  
                group_name = "finance"
                v = any(group_name.lower() in group.lower() for group in user_entry['memberOf'].values)
                # if user is part of finances too
                if v:
                    l_success_msg = 'finances'    
            else:
                # if user not part of timekeeping
                group_name = "finance"
                v = any(group_name.lower() in group.lower() for group in user_entry['memberOf'].values)
                if v:
                    dn = user_entry.entry_dn
                    components = dn.split(',')

                    # Look for the CN= component and extract the name
                    cn = ldap_user_name
                    for component in components:
                        if component.startswith('CN='):
                            cn = component[3:]  # Skip the 'CN=' part
                            break

                    name = HumanName(cn).first
                    l_success_msg = 'finances'
                # if user not part of finances or timekeeping
                else:
                    l_success_msg = f' ** User not part of valid group.'
    else:
        print(f" *** Cannot bind to ldap server: {connection.last_error} ")
        l_success_msg = f' ** Failed Authentication: {connection.last_error}'
    
    return l_success_msg, name

