from main import airtanker_app
from flask import render_template, request, session, flash, url_for, jsonify

from endpoints.decorators import requires_authentication, requires_odoo_authentication, requires_ldap_group_authentication
from endpoints.tasks import post_customer_invoices_task, post_vendor_bills_task, post_timesheets_task

from services.OdooService import OdooService
from services.DatabaseService import DatabaseService
from services.DateParser import check_holiday

from forms.WorkOrderForm import *
from auth.global_ldap_authentication import *
from threading import Thread
from enum import Enum
from dotenv import load_dotenv
from decimal import Decimal
from endpoints.pdf_creation import create_pdfs

class OT_Rules(Enum):
    Over40Wk = 1
    Over40Wk_or_Saturday = 2
    Over8Dy_or_Saturday = 3

class DT_Rules(Enum):
    Over40Wk_and_Sunday = 1
    AnySunday = 2

def get_rule_from_value(value, enum):
    if value:
        try:
            # Try to return the corresponding enum member
            return enum(value)
        except ValueError:
            # If value is not a valid enum, return the default enum (first one)
            return list(enum)[0]
    else:
        # If value is falsey (e.g., None, False), return the default enum
        return list(enum)[0]

#### STEP 5 - EXPORTS MAIN
@airtanker_app.route('/exports', methods=['GET'])
@requires_authentication
@requires_odoo_authentication
@requires_ldap_group_authentication
def exports():
    form = WorkOrderForm()

    session.pop('export_errors_found', None)
    return render_template('endpoints/exports.html', form=form, selected_week_ending=session.get('selected_week_ending', False))

@airtanker_app.route('/get_parent_code', methods=['POST'])
@requires_odoo_authentication
@requires_authentication
def get_parent_code():
    data = request.json
    parent_id = data.get('parent_id')
    contact_id = data.get('contact_id')
    odoo_service = session['odoo_service'] # type: OdooService
    # Call the method from odoo_service with the parent_id
    parent_code = None
    
    if is_convertible_to_int(parent_id):
        parent_code = odoo_service.get_parent_code_field(int(parent_id))

    if not parent_code:
        parent_code = odoo_service.get_initials_from_contact_id(int(contact_id))

    # Return the parent_code in the response
    return jsonify(parent_code=parent_code)

def is_convertible_to_int(string):
    try:
        int(string)
        return True
    except ValueError:
        return False

##### BILLS #####
@airtanker_app.route('/export_bills')
@requires_ldap_group_authentication
@requires_odoo_authentication
@requires_authentication
def export_bills():
    week_ending = request.args.get('week_ending')
    if week_ending:
        session['selected_week_ending'] = week_ending
    return render_template('endpoints/export_bills.html')

@airtanker_app.route('/get_vendor_bills', methods=['POST', 'GET'])
@requires_ldap_group_authentication
@requires_odoo_authentication
@requires_authentication
def get_vendor_bills():
    form = WorkOrderForm()
    if request.method in ('POST', 'GET'):
        data = {}
        errors = []

        selected_week_ending = session['selected_week_ending']

        # Use the database service and the Odoo service to get the information.
        odoo_service = session['odoo_service'] # type: OdooService
        database_service = DatabaseService()
        database_service.connect()

        # get work order ID's and also the approved hours total so we can use it for fixed rate
        query_return_data = database_service.get_employee_details_weekly_work_ids(selected_week_ending,
                                                                                  isBill=False, #os.getenv('PRODUCTION', 'True').lower() == 'true',
                                                                                  isInvoice=False,
                                                                                  include_expenses=True)
        ######### CHANGE THE ABOVE FOR isBill = True after testing.   

        if not query_return_data:
            return jsonify({"status": "error"})
        
        work_order_ids = [x["WorkOrderID"] for x in query_return_data]
        
        current_work_order_info = odoo_service.get_work_orders_for_talent_desk_by_ids(work_order_ids)
        # get the work_order information which contains rate and type
        bill_counter = 0
        contacts = odoo_service.get_all_subcontractor_contacts()
        error_id = 0
        for work_order in current_work_order_info:
            if work_order['x_studio_employee_type'] == 'contractor' or work_order['x_studio_employee_type'] == 'freelance':
                bill_counter += 1
                
                contractor_id = work_order['x_studio_assigned_to'][0]
                contractor_name = work_order['x_studio_assigned_to'][1]

                work_order_name = work_order['name']
                work_order_id = work_order['id']
                project_id = work_order['project_id'][0]
                analytic_account = work_order['project_analytic_account_id']
                
                time_sheet_entry = list(filter(lambda x: x['WorkOrderID'] == work_order['id'], query_return_data))[0]                    
                approved_hours = database_service.get_employee_reported_hours(time_sheet_entry)
                expenses = None
                if 'Expenses' in time_sheet_entry:
                    expenses = time_sheet_entry['Expenses']

                ot_rule = get_rule_from_value(work_order['x_sow_overtime_rule'], OT_Rules)
                dt_rule = get_rule_from_value(work_order['x_sow_double_time_rule'], DT_Rules)
                
                standard_rate = work_order['x_sow_rate']
                travel_rate = work_order['x_sow_tt_rate']
                ot_rate = work_order['x_sow_ot_rate']
                dt_rate = work_order['x_sow_dt_rate']
                holiday_rate = work_order['x_sow_ht_rate']

                # ######## REMOVE LATER ########
                # if bill_counter == 1:
                #     work_order['x_studio_sow_partner'] = False
                # ##############################
                rate_type = work_order['x_sow_rate_type'] # type: str

                if not work_order['x_studio_sow_partner'] and not work_order['x_studio_sow_company']:
                    error_id += 1
                    standard_hours, travel_hours, ot_hours, dt_hours, holiday_hours = get_approved_hours(approved_hours=approved_hours,
                                                                                                            ot_rule=ot_rule,
                                                                                                            dt_rule=dt_rule)
                    # combine all hours if rate is fixed type.
                    # TODO This will add all rates to the bill even though standard hours will only have the hours
                    # if adding just one line item for fixed types, we'll need to add the type into the dictionary to know what it is in javascript
                    if rate_type:
                        if 'fix' in rate_type.lower():
                            standard_hours = standard_hours + travel_hours + ot_hours + dt_hours + holiday_hours
                            travel_hours = 0
                            ot_hours = 0
                            dt_hours = 0
                            holiday_hours = 0

                    st_total = standard_hours * Decimal(standard_rate)
                    ot_total = ot_hours * Decimal(ot_rate)
                    dt_total = dt_hours * Decimal(dt_rate)

                    travel_total = travel_hours * Decimal(travel_rate)
                    holiday_total = holiday_hours * Decimal(holiday_rate)

                    data_object = {
                        "Contacts": contacts,
                        "Bill_Info": f"{time_sheet_entry['WeekEnding'].strftime('%Y%m%d')}",

                        #### TODO: get this information from the contacts data. Append it later to the error object.
                        "ParentCode": "",
                        #"SOW_Contact": sow_contact,
                        "SOW_Company": "",

                        "RateType": rate_type,
                        "WorkOrderID":work_order_id,
                        "WorkOrderName": work_order_name,
                        "ProjectID": project_id,
                        "TimesheetID": time_sheet_entry['TimeSheetID'],
                        "LineItemWeekEnding": time_sheet_entry['WeekEnding'].strftime('%Y%m%d'),
                        "ContractorName": contractor_name,
                        "ContractorID": contractor_id,
                        "ApprovedData": {
                            "Standard": { 
                                "Hours": format_number(standard_hours), 
                                "Rate": format_decimal(standard_rate),
                                "Total": format_number(st_total)
                            },
                            "Travel": { 
                                "Hours": format_number(travel_hours), 
                                "Rate": format_decimal(travel_rate),
                                "Total": format_number(travel_total)
                            },
                            "OT": { 
                                "Hours": format_number(ot_hours), 
                                "Rate": format_decimal(ot_rate),
                                "Total": format_number(ot_total)
                            },
                            "DT": { 
                                "Hours": format_number(dt_hours), 
                                "Rate": format_decimal(dt_rate),
                                "Total": format_number(dt_total)
                            },
                            "Holiday": { 
                                "Hours": format_number(holiday_hours), 
                                "Rate": format_decimal(holiday_rate),
                                "Total": format_number(holiday_total)
                            }
                        },
                        "Expenses": expenses
                    }
                    errors.append(data_object)
                    continue
                else:
                    sow_contact = work_order['x_studio_sow_partner'] # contact information

                    parent_code = None
                    sow_company = None
                    if work_order['x_studio_sow_company']:
                        sow_company = work_order['x_studio_sow_company']
                        parent_code = odoo_service.get_parent_code_field(sow_company[0])
                    
                    if not parent_code:
                        parent_code = odoo_service.get_initials_from_contact_id(work_order['x_studio_sow_partner'][0])

                if not parent_code:
                    airtanker_app.logger.debug(f"Couldn't find parent code for {work_order['x_studio_sow_company']}")
                    parent_code = ""                            
                if approved_hours:
                    standard_hours, travel_hours, ot_hours, dt_hours, holiday_hours = get_approved_hours(approved_hours=approved_hours,
                                                                                                            ot_rule=ot_rule,
                                                                                                            dt_rule=dt_rule)
                    # combine all hours if rate is fixed type.
                    # TODO This will add all rates to the bill even though standard hours will only have the hours
                    # if adding just one line item for fixed types, we'll need to add the type into the dictionary to know what it is in javascript
                    if rate_type:
                        if 'fix' in rate_type.lower():
                            standard_hours = standard_hours + travel_hours + ot_hours + dt_hours + holiday_hours
                            travel_hours = 0
                            ot_hours = 0
                            dt_hours = 0
                            holiday_hours = 0

                    st_total = standard_hours * Decimal(standard_rate)
                    ot_total = ot_hours * Decimal(ot_rate)
                    dt_total = dt_hours * Decimal(dt_rate)

                    travel_total = travel_hours * Decimal(travel_rate)
                    holiday_total = holiday_hours * Decimal(holiday_rate)

                    # add the data to the array. Return the array in JSON at the end.
                    data_object = {
                        "Bill_Info": f"{time_sheet_entry['WeekEnding'].strftime('%Y%m%d')}",
                        "ParentCode": parent_code,
                        "SOW_Contact": sow_contact,
                        "SOW_Company": sow_company,
                        "RateType": rate_type,
                        "WorkOrderID":work_order_id,
                        "WorkOrderName": work_order_name,
                        "ProjectID": project_id,
                        "TimesheetID": time_sheet_entry['TimeSheetID'],
                        "LineItemWeekEnding": time_sheet_entry['WeekEnding'].strftime('%Y%m%d'),
                        "ContractorName": contractor_name,
                        "ContractorID": contractor_id,
                        "AccountID": analytic_account[0],
                        "AccountName": analytic_account[1],
                        "ApprovedData": {
                            "Standard": { 
                                "Hours": format_number(standard_hours), 
                                "Rate": format_decimal(standard_rate),
                                "Total": format_number(st_total)
                            },
                            "Travel": { 
                                "Hours": format_number(travel_hours), 
                                "Rate": format_decimal(travel_rate),
                                "Total": format_number(travel_total)
                            },
                            "OT": { 
                                "Hours": format_number(ot_hours), 
                                "Rate": format_decimal(ot_rate),
                                "Total": format_number(ot_total)
                            },
                            "DT": { 
                                "Hours": format_number(dt_hours), 
                                "Rate": format_decimal(dt_rate),
                                "Total": format_number(dt_total)
                            },
                            "Holiday": { 
                                "Hours": format_number(holiday_hours), 
                                "Rate": format_decimal(holiday_rate),
                                "Total": format_number(holiday_total)
                            }
                        },
                        "Expenses": expenses # This will contain the total approved expenses
                    }

                    if sow_contact[0] in data:
                        # append the data to the existing array under sow_contact_id
                        data[sow_contact[0]].append(data_object)
                    else:
                        # create array object under sow_contact_id
                        data[sow_contact[0]] = []
                        data[sow_contact[0]].append(data_object)
                else:
                    airtanker_app.logger.error(f"Error getting reported hours from TimeSheet Entries using timesheet ID {time_sheet_entry['TimeSheetID']}, {time_sheet_entry['ApprovedHoursSource']}")
        database_service.disconnect()
        if bill_counter > 0:
            return jsonify({
                "status": "success",
                "data": data,
                "errors": errors
            })
        else:
            flash("No approved hours for contractor / freelancer type.", 'info')
            return jsonify({'redirect_url': url_for('exports')})
    

@airtanker_app.route('/post_vendor_bills', methods=['POST'])
@requires_odoo_authentication
@requires_authentication
def post_vendor_bills():
    if not request.is_json:
        return jsonify({"error": "Missing JSON in request"}), 200
    odoo_service = session['odoo_service']  # Assume this is retrieved properly
    entries = request.get_json()
    task_id = os.urandom(24).hex()
    thread = Thread(target=post_vendor_bills_task, args=(entries, odoo_service, task_id))
    thread.start()

    return jsonify({"message": "Processing started", "task_id": task_id}), 202


###### INVOICES #######
@airtanker_app.route('/export_invoices')
@requires_ldap_group_authentication
@requires_odoo_authentication
@requires_authentication
def export_invoices():
    week_ending = request.args.get('week_ending')
    if week_ending:
        session['selected_week_ending'] = week_ending
    return render_template('endpoints/export_invoice.html')


@airtanker_app.route('/get_customer_invoices', methods=['POST', 'GET'])
@requires_ldap_group_authentication
@requires_odoo_authentication
@requires_authentication
def get_customer_invoices():
    if request.method in ('POST', 'GET'):        
            
        selected_week_ending = session['selected_week_ending']

        data = {}
        errors = []

        saleOrders_dict = {}

        load_dotenv()


        # Use the database service and the Odoo service to get the information.
        odoo_service = session["odoo_service"] # type: OdooService
        database_service = DatabaseService()
        database_service.connect()

        # get work order ID's and also the approved hours total so we can use it for fixed rate
        query_return_data = database_service.get_employee_details_weekly_work_ids(selected_week_ending,
                                                                                  isBill=False,
                                                                                  isInvoice=False,#os.getenv('PRODUCTION', 'True').lower() == 'true')
                                                                                  include_expenses=True)

        if not query_return_data:
            return jsonify({"status": "error"})
        
        work_order_ids = [x["WorkOrderID"] for x in query_return_data]
        
        current_work_order_info = odoo_service.get_work_orders_for_talent_desk_by_ids(work_order_ids)
        # get the work_order information which contains rate and type
        bill_counter = 0
        for work_order in current_work_order_info:
            bill_counter += 1
            rate_type = work_order['x_sow_rate_type'] # type: str

            contractor_name = work_order['x_studio_assigned_to'][1]
            work_order_id = work_order['id']
            work_order_name = work_order["name"]

            time_sheet_entry = list(filter(lambda x: x['WorkOrderID'] == work_order_id, query_return_data))[0]

            # add this to dictionary at the end - need projectID:billID
            projectID = work_order['project_id'][0]
            project_name = work_order['project_id'][1]

            ot_rule = get_rule_from_value(work_order['x_inv_overtime_rule'], OT_Rules)
            dt_rule = get_rule_from_value(work_order['x_inv_double_time_rule'], DT_Rules)

            # Let's read the first task to get some important info.
            customerID = work_order['partner_id'][0]
            customer_name = work_order['partner_id'][1]

            accountID = work_order['project_analytic_account_id']
            saleOrderID = work_order['project_sale_order_id']

            if saleOrderID:
                saleOrderID = saleOrderID[0]
                # Get Sale Order information used in the Invoice
                saleOrders = None
                if saleOrderID not in saleOrders_dict:

                    # Doing this to make it faster. Won't have to pull from Odoo API more than once this way per sale_order_id
                    saleOrders = odoo_service.get_sale_order_by_id(saleOrderID)
                    saleOrders_dict[saleOrderID] = saleOrders
                else:
                    saleOrders = saleOrders_dict[saleOrderID]

                customerPO = saleOrders[0]['x_studio_message_on_document']
            else:
                customerPO = ''            

            st_rate = work_order['x_inv_rate']
            ot_rate = work_order['x_inv_ot_rate']
            dt_rate = work_order['x_inv_dt_rate']
            plant_name = work_order['x_studio_plant_name']
                                
            approved_hours = database_service.get_employee_reported_hours(time_sheet_entry)
            if approved_hours:
                standard_hours, travel_hours, ot_hours, dt_hours, holiday_hours = get_approved_hours(approved_hours=approved_hours,
                                                                                                        ot_rule=ot_rule,
                                                                                                        dt_rule=dt_rule)
                dt_hours += holiday_hours
                standard_hours += travel_hours
                if st_rate == ot_rate == dt_rate:
                    rate_type = "fixed rate"
                    standard_hours = standard_hours + ot_hours + dt_hours
                    travel_hours = 0
                    ot_hours = 0
                    dt_hours = 0
                    holiday_hours = 0
                else:
                    rate_type = "Base Rate w/ Multipliers"

                # if rate_type:
                #     if 'fix' in rate_type.lower():
                #         standard_hours = standard_hours + travel_hours + ot_hours + dt_hours + holiday_hours
                #         travel_hours = 0
                #         ot_hours = 0
                #         dt_hours = 0
                #         holiday_hours = 0
                # else:
                #     if st_rate == ot_rate == dt_rate:
                #         rate_type = "fixed rate"
                #         standard_hours = standard_hours + travel_hours + ot_hours + dt_hours + holiday_hours
                #         travel_hours = 0
                #         ot_hours = 0
                #         dt_hours = 0
                #         holiday_hours = 0
                #     else:
                #         rate_type = "Base Rate w/ Multipliers"
            else:
                continue

            contractor_id = work_order[os.getenv("EMP_INFO_FIELD")][0]

            employee = odoo_service.get_employee(contractor_id)
            contractor_number = ""
            if employee[0].get('x_studio_customer_id') and 'ATS' in customer_name:
                contractor_number = " - " + employee[0].get('x_studio_customer_id')

            st_total = standard_hours * Decimal(st_rate)
            ot_total = ot_hours * Decimal(ot_rate)
            dt_total = dt_hours * Decimal(dt_rate)

            data_object = {
                "TimesheetID": time_sheet_entry['TimeSheetID'],
                "WeekEnding": selected_week_ending.replace("-",""),

                "ContractorName": contractor_name,
                "ContractorNumber": contractor_number,
                "ContractorID": contractor_id,

                "ProjectID": projectID,
                "ProjectName": project_name,

                "WorkOrderName": work_order_name,
                
                "CustomerID": customerID,
                "CustomerName": customer_name,
                "CustomerPO": customerPO,

                "AccountID": accountID,
                "AccountName": accountID[1],

                "RateType": rate_type,
                
                "ApprovedData": {
                    "Standard": { "Hours": format_number(standard_hours), "Rate": format_decimal(st_rate), "Total": format_number(st_total) },
                    "OT": { "Hours": format_number(ot_hours), "Rate": format_decimal(ot_rate), "Total": format_number(ot_total) },
                    "DT": { "Hours": format_number(dt_hours), "Rate": format_decimal(dt_rate), "Total": format_number(dt_total) }
                },
                "Expenses": time_sheet_entry['Expenses'],
                "PlantName": plant_name

            }

            if projectID not in data:
                data[projectID] = []

            # Add the data to the array
            data[projectID].append(data_object)

        database_service.disconnect()
        if bill_counter > 0:
            #return jsonify(data)
            return jsonify({
                "status": "success",
                "data": data,
                "errors": errors
            })


@airtanker_app.route('/post_customer_invoices', methods=['POST'])
@requires_odoo_authentication
@requires_authentication
def post_customer_invoices():
    if not request.is_json:
        return jsonify({"error": "Missing JSON in request"}), 200
    odoo_service = session['odoo_service']  # Assume this is retrieved properly
    entries = request.get_json()
    
    task_id = os.urandom(24).hex()
    pdf_task_id = os.urandom(24).hex()

    thread = Thread(target=post_customer_invoices_task, args=(entries, odoo_service, task_id))
    thread.start()

    pdf_thread = Thread(target=create_pdfs, args=(entries, pdf_task_id))
    pdf_thread.start()

    return jsonify(
        {
            "message": "Processing started", 
            "task_id": task_id,
            "pdf_task_id": pdf_task_id
        }
    ), 202

##### TIMESHEETS #####
@airtanker_app.route('/export_timesheets')
@requires_ldap_group_authentication
@requires_odoo_authentication
@requires_authentication
def export_timesheets():
    week_ending = request.args.get('week_ending')
    if week_ending:
        session['selected_week_ending'] = week_ending
    return render_template('endpoints/export_timesheets.html')

@airtanker_app.route('/export_fixed_timesheets')
@requires_ldap_group_authentication
@requires_odoo_authentication
@requires_authentication
def export_fixed_timesheets():
    week_ending = request.args.get('week_ending')
    if week_ending:
        session['selected_week_ending'] = week_ending
    return render_template('timesheet_wizard/export_fixed_timesheets.html')

@airtanker_app.route('/get_timesheets', methods=['POST', 'GET'])
@requires_ldap_group_authentication
@requires_odoo_authentication
@requires_authentication
def get_timesheets():
    if request.method in ('POST', 'GET'):

        data = [] # create an array to house all the data that where going to gather.
        invalid_entries = []

        selected_week_ending = session['selected_week_ending']

        row_id = 1
        bill_counter = 0

        timesheet_type = 'all'
        if request.args and len(request.args) > 0:
            timesheet_type = request.args['type']

        # Use the database service and the Odoo service to get the information.
        odoo_service = session['odoo_service'] # type: OdooService
        database_service = DatabaseService()
        database_service.connect()

        if timesheet_type == 'all' or timesheet_type == 'external':
            contractor_hours_query = """
                SELECT [WorkOrderID]
                    ,[ApprovedHours]
                    ,[WeekStarting]
                    ,[WeekEnding]
                    ,[ApprovedHoursSource]
                    ,[TimeSheetID]
                    ,[Bill_Created]
                    ,[Invoice_Created]
                FROM [dbo].[vw_EmployeeDetailsWeekly]
                WHERE [WeekEnding] = ? AND [StatusID] IN (3, 6, 8)
                AND (Timesheet_Created = 0 OR Timesheet_Created is NULL)
            """
            # Check if StatusID is Approved, Approved with errors, or already exported. Ensure timesheet hasn't already been created
            contractor_hours = database_service.execute_query(contractor_hours_query, (selected_week_ending))
            if contractor_hours:
                # get all the work order ids in the contractors hours
                work_order_ids = [x["WorkOrderID"] for x in contractor_hours]
                # get the work_order information which contains rate and type
                current_contractors_work_order_info = odoo_service.get_work_orders_for_talent_desk_by_ids(work_order_ids)                
                error_id = 0
                
                # For each work order that is associated with a contractor
                for work_order in current_contractors_work_order_info:
                    if work_order['x_studio_employee_type'] == 'contractor' or \
                        work_order['x_studio_employee_type'] == 'freelance' or \
                        work_order['x_studio_employee_type'] == 'employee':
                        
                        contractor_id = work_order['x_studio_assigned_to'][0]
                        contractor_name = work_order['x_studio_assigned_to'][1]

                        work_order_name = work_order['name']
                        work_order_id = work_order['id']
                        project_id = work_order['project_id'][0]
                        project_name = work_order['project_id'][1]
                        
                        time_sheet_entry = list(filter(lambda x: x['WorkOrderID'] == work_order['id'], contractor_hours))[0]                    
                        approved_hours = database_service.get_employee_reported_hours(time_sheet_entry) # get the hours from the database, uses vw_EmployeeHoursWithSource
                        
                        ot_rule = get_rule_from_value(work_order['x_sow_overtime_rule'], OT_Rules)
                        dt_rule = get_rule_from_value(work_order['x_sow_double_time_rule'], DT_Rules)                

                        # ######## REMOVE LATER ########
                        # if bill_counter == 1:
                        #     work_order['x_studio_sow_partner'] = False
                        # ##############################
                        rate_type = "Base rate w/ Multiplier"
                        if 'x_sow_rate_type' in work_order:
                            if isinstance(work_order['x_sow_rate_type'], str):
                                rate_type = work_order['x_sow_rate_type'] # type: str
                                
                        bill_counter += 1
                        #if approved_hours:
                        bill_counter += 1

                        # get appproved hours 
                        standard_hours, travel_hours, ot_hours, dt_hours, holiday_hours = get_approved_hours(approved_hours=approved_hours,
                                                                                                                ot_rule=ot_rule,
                                                                                                                dt_rule=dt_rule)
                        # combine all hours if rate is fixed type.
                        # TODO This will add all rates to the bill even though standard hours will only have the hours
                        # if adding just one line item for fixed types, we'll need to add the type into the dictionary to know what it is in javascript
                        if 'fix' in rate_type.lower():
                            standard_hours = standard_hours + travel_hours + ot_hours + dt_hours + holiday_hours

                            # add the data to the array. Return the array in JSON at the end.
                            data_object = {
                                "RowID":row_id,
                                "IsChecked": True,
                                "TimesheetID": time_sheet_entry['TimeSheetID'],
                                "InternalID": None,
                                "Date": time_sheet_entry['WeekEnding'].strftime('%m/%d/%Y'),
                                "ContractorID": contractor_id,
                                "ContractorName": contractor_name,
                                "ProjectID": project_id,
                                "ProjectName": project_name,
                                "TaskID": work_order_id,
                                "TaskName": work_order_name,
                                "Description": f"{work_order_name} {contractor_name}", ## This is going to be the work order thing + double etc
                                "Hours": standard_hours
                            }
                            # Add the data to the array
                            row_id += 1
                            data.append(data_object)

                        else:
                            # Here the hours are being mixed, travel into standard, and holiday into DT
                            # This affects how they being uploaded to Odoo due to travel and holiday doesn't exist for being uploaded
                            # standard_hours += travel_hours
                            # dt_hours += holiday_hours

                            # Add each hour type and corresponding hours to the array. Return the array in JSON at the end.
                            ### Travel Hours
                            data_object = {
                                "RowID":row_id,
                                "IsChecked": True,
                                "TimesheetID": time_sheet_entry['TimeSheetID'],
                                "InternalID": None,
                                "Date": time_sheet_entry['WeekEnding'].strftime('%m/%d/%Y'),
                                "ContractorID": contractor_id,
                                "ContractorName": contractor_name,
                                "ProjectID": project_id,
                                "ProjectName": project_name,
                                "TaskID": work_order_id,
                                "TaskName": work_order_name,
                                "Description": f"{work_order_name} {contractor_name} - Travel", ## This is going to be the work order thing + double etc
                                "Hours": travel_hours
                            }
                            row_id += 1
                            data.append(data_object)

                            ### Holiday Hours
                            data_object = {
                                "RowID":row_id,
                                "IsChecked": True,
                                "TimesheetID": time_sheet_entry['TimeSheetID'],
                                "InternalID": None,
                                "Date": time_sheet_entry['WeekEnding'].strftime('%m/%d/%Y'),
                                "ContractorID": contractor_id,
                                "ContractorName": contractor_name,
                                "ProjectID": project_id,
                                "ProjectName": project_name,
                                "TaskID": work_order_id,
                                "TaskName": work_order_name,
                                "Description": f"{work_order_name} {contractor_name} - Holiday", ## This is going to be the work order thing + double etc
                                "Hours": holiday_hours
                            }
                            row_id += 1
                            data.append(data_object)

                            ### DT Hours
                            data_object = {
                                "RowID":row_id,
                                "IsChecked": True,
                                "TimesheetID": time_sheet_entry['TimeSheetID'],
                                "InternalID": None,
                                "Date": time_sheet_entry['WeekEnding'].strftime('%m/%d/%Y'),
                                "ContractorID": contractor_id,
                                "ContractorName": contractor_name,
                                "ProjectID": project_id,
                                "ProjectName": project_name,
                                "TaskID": work_order_id,
                                "TaskName": work_order_name,
                                "Description": f"{work_order_name} {contractor_name} - Double Time", ## This is going to be the work order thing + double etc
                                "Hours": dt_hours
                            }
                            row_id += 1
                            data.append(data_object)

                            ### OT Hours
                            data_object = {
                                "RowID":row_id,
                                "IsChecked": True,
                                "TimesheetID": time_sheet_entry['TimeSheetID'],
                                "InternalID": None,
                                "Date": time_sheet_entry['WeekEnding'].strftime('%m/%d/%Y'),
                                "ContractorID": contractor_id,
                                "ContractorName": contractor_name,
                                "ProjectID": project_id,
                                "ProjectName": project_name,
                                "TaskID": work_order_id,
                                "TaskName": work_order_name,
                                "Description": f"{work_order_name} {contractor_name} - Overtime", ## This is going to be the work order thing + double etc
                                "Hours": ot_hours
                            }
                            row_id += 1
                            data.append(data_object)

                            ### Standard Hours
                            data_object = {
                                "RowID":row_id,
                                "IsChecked": True,
                                "TimesheetID": time_sheet_entry['TimeSheetID'],
                                "InternalID": None,
                                "Date": time_sheet_entry['WeekEnding'].strftime('%m/%d/%Y'),
                                "ContractorID": contractor_id,
                                "ContractorName": contractor_name,
                                "ProjectID": project_id,
                                "ProjectName": project_name,
                                "TaskID": work_order_id,
                                "TaskName": work_order_name,
                                "Description": f"{work_order_name} {contractor_name}", ## This is going to be the work order thing + double etc
                                "Hours": standard_hours
                            }
                            row_id += 1
                            data.append(data_object)
                        # else:
                        #     airtanker_app.logger.error(f"Error getting reported hours from TimeSheet Entries using timesheet ID {time_sheet_entry['TimeSheetID']}, {time_sheet_entry['ApprovedHoursSource']}")

        if timesheet_type == 'all' or timesheet_type == 'internal':
            """
            It enters here if the requests comes from the export_fixed_timesheets.html page.

            What I think it is for is:
            Internal_Timesheets segregates internal, non-billable time tracking from potentially billable, external client time, allowing different processes (like imports or exports) to handle them separately based on their needs
            """
            employee_hours_query = '''
                SELECT [ID], [Date], [EmployeeID], [EmployeeName], [ProjectID], [ProjectName], 
                [TaskID], [TaskName], [Description], [Hours], [Timesheet_Created]
                FROM [dbo].[Internal_Timesheets]
                WHERE ([Date] BETWEEN DATEADD(day, -6, ?) AND ?)
                AND ISNULL([Timesheet_Created], 0) = 0
            '''
            parameters = (selected_week_ending, selected_week_ending)
            employee_hours = database_service.execute_query(employee_hours_query, parameters)
            if employee_hours:
                # Process Hours from internal
                errors_exist = False
                for time_entry in employee_hours:
                    if time_entry["TaskID"] is None or time_entry["ProjectID"] is None:
                        time_entry['Date'] = time_entry["Date"].strftime('%m/%d/%Y')
                        invalid_entries.append(time_entry)
                        errors_exist = True
                    elif errors_exist == False:
                        bill_counter += 1
                        # add the data to the array. Return the array in JSON at the end.
                        data_object = {
                            "RowID":row_id,
                            "IsChecked": True,
                            "InternalID": time_entry["ID"],
                            "TimesheetID": None,
                            "Date": time_entry["Date"].strftime('%m/%d/%Y'),
                            "ContractorID": time_entry["EmployeeID"],
                            "ContractorName": time_entry["EmployeeName"],
                            "ProjectID": time_entry["ProjectID"],
                            "ProjectName": time_entry["ProjectName"],
                            "TaskID": time_entry["TaskID"],
                            "TaskName": time_entry["TaskName"],
                            "Description": time_entry["Description"],
                            "Hours": time_entry["Hours"]
                        }

                        # Add the data to the array
                        row_id += 1
                        data.append(data_object)

        database_service.disconnect()
                
        if invalid_entries:
            projects = odoo_service.get_all_projects()

            invalid_entries_sorted = sorted(invalid_entries, key=lambda x: x["EmployeeName"])
            data_sorted = []
            if bill_counter > 0:
                data_sorted = sorted(data, key=lambda x: x["ContractorName"])

            return jsonify({
                "status": "error",
                "message": "Invalid entries found. Please select the correct ProjectID and TaskID.",
                "errors": invalid_entries_sorted,
                "data": data_sorted, # pass the data in too. Handle all.
                "projects": projects
            })
        
        if bill_counter > 0 or len(data) > 0:
            data_sorted = sorted(data, key=lambda x: x["ContractorName"])
            return jsonify({
                "status": "success",
                "message": "Success",
                "errors": None,
                "data": data_sorted, # pass the data in too. Handle all.
                "projects": None
            })
        else:
            flash("No approved hours or timesheets are already exported for the selected date.", 'info')
            return jsonify({'redirect_url': url_for('exports')})

@airtanker_app.route('/post_timesheets', methods=['POST'])
@requires_odoo_authentication
@requires_authentication
def post_timesheets():
    if not request.is_json:
        return jsonify({"error": "Missing JSON in request"}), 200
    odoo_service = session['odoo_service']  # Assume this is retrieved properly
    entries = request.get_json()
    task_id = os.urandom(24).hex()
    thread = Thread(target=post_timesheets_task, args=(entries, odoo_service, task_id))
    thread.start()

    return jsonify({"message": "Processing started", "task_id": task_id}), 202

### HELPER ###
def get_approved_hours(approved_hours, ot_rule, dt_rule):

    standard_hours = 0
    travel_hours = 0
    ot_hours = 0
    dt_hours = 0
    holiday_hours = 0

    for reported_hours in approved_hours:
        # check the task
        task_name = reported_hours['TaskName']
        date = reported_hours['Date']
        hours = reported_hours['Hours']

        if task_name and "travel" in task_name.lower():
            # add travel time to bill
            travel_hours += hours
        else:                    
            date_day = date.weekday()
            is_holiday = check_holiday(date)

            # TODO Check if it's a holiday or if the time is on a Friday or Monday then we need to check if there's a holiday on the weekend
            # because the Monday/Friday will become a holidays pay.
            if is_holiday:
                holiday_hours += hours

            elif date_day < 5: # Weekday
                # TODO check overtime rules
                if (ot_rule == OT_Rules.Over8Dy_or_Saturday):
                    if (hours > 8):
                        hours_over = hours - 8
                        standard_hours += 8
                        ot_hours += hours_over
                    else:
                        standard_hours += hours
                else:
                # add the hours accordingly
                    standard_hours += hours

                    if standard_hours > 40 and (ot_rule == OT_Rules.Over40Wk or ot_rule == OT_Rules.Over40Wk_or_Saturday):
                        hours_over = standard_hours - 40 # get the hours over 40
                        standard_hours -= hours_over # subtract hours over from the 40+
                        ot_hours += hours_over # add the hours over 40 to the OT
                    else:
                        # There might be another rule which is anything over 8 hours for the day - not just over 40 for the 
                        # TODO other rules
                        pass
            elif date_day == 5: # Saturday
                # check overtime rules
                standard_hours += hours
                if standard_hours > 40 and ot_rule == OT_Rules.Over40Wk:
                    hours_over = standard_hours - 40 # get the hours over 40
                    standard_hours -= hours_over # subtract hours over from the 40+
                    ot_hours += hours_over # add the hours over 40 to the OT
                elif ot_rule == OT_Rules.Over40Wk_or_Saturday or ot_rule == OT_Rules.Over8Dy_or_Saturday:
                    ot_hours += hours
                    standard_hours -= hours # undo hour add pre-if statement
            elif date_day == 6: # Sunday
                # TODO check overtime rules
                standard_hours += hours # add the hours to standard to see if it'll get over 40 with the addition
                if standard_hours > 40 and dt_rule == DT_Rules.Over40Wk_and_Sunday:
                    hours_over = standard_hours - 40 # get the hours over 40
                    standard_hours -= hours_over # subtract hours over from the 40+
                    dt_hours += hours_over # add the hours over 40 to the DT                           
                elif dt_rule == DT_Rules.AnySunday:
                    standard_hours -= hours
                    dt_hours += hours
    return standard_hours, travel_hours, ot_hours, dt_hours, holiday_hours

def format_number(num):
    # Ensure the number is a float and format it with commas and two decimal places
    formatted_number = "{:,.2f}".format(float(num))
    return formatted_number
        
def format_decimal(value, decimal_places=3):
        return Decimal(value).quantize(Decimal('1.' + '0' * decimal_places))

def get_rule_from_value(value, enum):
    if value:
        try:
            num = int(value)
            rule = enum(num)
            return rule
        except ValueError:
            # If value is not a valid enum, return the default enum (first one)
            rule = list(enum)[0]
            airtanker_app.logger.error(f'Could not determine premium time rule from Odoo value [{value}]. Using default value {rule.name}')
            return rule
    else:
        # If value is falsey (e.g., None, False), return the default enum
        rule = list(enum)[0]
        airtanker_app.logger.error(f'Missing premium time rule from Odoo value. Using default value {rule.name}')
        return rule

@airtanker_app.route('/get_tasks/<int:project_id>', methods=['GET'])
@requires_odoo_authentication
@requires_authentication
def get_tasks(project_id):
    odooService = session["odoo_service"] #type: OdooService
    tasks = odooService.get_all_tasks(project_id)
    return jsonify(tasks)

@airtanker_app.route('/update_timesheet_entries', methods=['POST'])
@requires_odoo_authentication
@requires_authentication
def update_timesheet_entries():
    timesheet_entries_update = request.get_json()
    odooService = session["odoo_service"] # type: OdooService

    database_service = DatabaseService()
    database_service.connect()

    update_query = """
        UPDATE [dbo].[Internal_Timesheets]
        SET 
            [ProjectID] = ?,
            [ProjectName] = ?,
            [TaskID] = ?,
            [TaskName] = ?
        WHERE
            [ID] = ?
    """

    for timesheet_entry in timesheet_entries_update:
        
        project = odooService.get_all_projects(timesheet_entry["ProjectID"])
        task = odooService.get_all_tasks(task_id=timesheet_entry["TaskID"])

        parameters = (int(project[0]['id']),
                      project[0]['name'],
                      int(task[0]['id']),
                      task[0]['name'],
                      int(timesheet_entry['ID']))
        
        database_service.execute_query(update_query, parameters) # Insert Hours into the internal table.

    database_service.disconnect()
    return jsonify({"status": "success", "message": "Timesheet entries updated successfully"}), 200
