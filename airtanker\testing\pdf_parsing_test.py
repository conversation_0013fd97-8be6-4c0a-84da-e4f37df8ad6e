import camelot
import pandas as pd
import os
from werkzeug.datastructures import FileStorage
import PyPDF2
from tabula import read_pdf 
from tabulate import tabulate 


def extract_table_from_pdf(file_path, page_number):
        
        # Process the DataFrame as needed, e.g., set the correct headers
        headers = df[0]
        df = df[1:]
        df.columns = headers
        return df

table = read_pdf("C:\\Users\\<USER>\\Downloads\\816 AtomTech USA 2024.07.14-1.pdf",pages=2,multiple_tables=False)
#print(table)

df = pd.DataFrame(table[0])

headers = df.iloc[2].fillna('-').apply(lambda x: ' '.join(x.split()))

print('\n')
#print(headers)
# Set the headers
df.columns = headers


# Split combined data in the last two rows based on space
df = df.iloc[4:].reset_index(drop=False)

# Display the intermediate DataFrame
print("\nIntermediate DataFrame with Headers:\n", df)




# Extract the data from the 7th row (index 6 relative to the original DataFrame)
data_row = df.iloc[3]  # 6th row relative to original df is now 3rd row relative to new df
data_values = data_row.apply(lambda x: x.split() if isinstance(x, str) else [x])

# Flatten the list of lists into a single list
data_values = [item for sublist in data_values for item in sublist]

# Create a DataFrame with the extracted data
df_cleaned = pd.DataFrame([data_values], columns=headers)

# Display the cleaned DataFrame
print("\nCleaned DataFrame:\n", df_cleaned)
# file = None
# with open('C:\\Users\\<USER>\\Downloads\\816 AtomTech USA 2024.07.14-1.pdf', 'rb') as fp:

#     PDFfilereader = PyPDF2.PdfReader(fp)

#     #print the number of pages
#     print(len(PDFfilereader.pages))

#     #provide the page number
#     page = PDFfilereader.pages[1]

#     #extracting the text in PDF file
#     text = page.extract_text()

#     # Split the text into lines
#     lines = text.split('\n')

#     # Extract the first three lines as headers
#     header_lines = lines[2]
#     week_ending = lines[3].split()[-1]
#     data_lines = lines[4]

#     # Combine the header lines into a single list of column names
#     headers = []
#     for header in header_lines.split(' '):
#         if "Employee"in header:
#              continue
#         headers.extend(header.split())

#     # Prepare the data rows
#     data_rows = []
#     for line in data_lines:
#         data_rows.append(line.split())

#     # Create the DataFrame
#     df = pd.DataFrame(data_rows, columns=headers)

#     # Display the DataFrame
#     print(df)



#     file = FileStorage(fp)

#      # Save the uploaded file to a temporary location
#     temp_pdf_path = f"output\\staging\\temp_pdf_file.pdf"
#     file.save(temp_pdf_path)
    
#     # Extract table from the saved PDF file
#     df = extract_table_from_pdf(temp_pdf_path, 2)
    
#     # Clean up the temporary file
#     os.remove(temp_pdf_path)

#     extract_table_from_pdf(file, 2)
#     print("Done")

