proxy_cache_path /tmp/cache levels=1:2 keys_zone=cache:10m max_size=500m inactive=60m use_temp_path=off;


upstream airtanker {
  server airtanker:5000 fail_timeout=0;
}

upstream airtanker-dev {  
  server airtanker-dev:5001 fail_timeout=0;
}

server {
  listen 80;
  #server_name localhost;

  location / {
    return 301 https://$host$request_uri;
  }
  
  location /health-check {
    add_header Content-Type text/plain;
    return 200 "success";
  }
}

server {
  listen 443 ssl;
  #server_name localhost;

  ssl_certificate /app/airtanker.cer;
  ssl_certificate_key /app/airtanker.key;

  # Set the maximum allowed size of the client request body to 1G
  client_max_body_size 1G;

  ssl_protocols TLSv1.2 TLSv1.3;
  ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384';
  ssl_prefer_server_ciphers on;

  location / {
    proxy_pass http://airtanker;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $server_name;
    proxy_set_header X-Scheme $scheme;
    proxy_http_version 1.1;
    proxy_set_header Connection "";
    proxy_set_header Cookie $http_cookie;
    proxy_set_header Set-Cookie $http_set_cookie;
    proxy_buffering off;  # Disable buffering for SSE
    proxy_cache off;  # Disable proxy caching
    proxy_read_timeout 600s;  # Extend timeout for long-lived connections
  }  
}