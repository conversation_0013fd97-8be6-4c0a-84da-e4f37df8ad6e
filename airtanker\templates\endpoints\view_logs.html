{% extends 'base.html' %}

{% block content %}
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}Log Viewer{% endblock %}</title>
    <!-- Include Bootstrap CSS -->
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    {% block head %}{% endblock %}
</head>
<style>
    .log-container div {
        margin: 2px 0;
        padding: 2px;
        line-height: 1.2;
        white-space: pre-wrap;
        font-family: monospace;
    }

    .log-container {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        padding: 10px;
        border-radius: 10px;

        max-height: calc(100vh - 350px);
        overflow-y: auto;
    }

    .filter-section {
        background-color: #f8f9fa;
        padding: 12px 20px;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    .form-group label {
        font-weight: 500;
    }
    .filter-log-buttons {
        margin-bottom: 1rem;
    }
    .daterangepicker {
        font-size: 14px;
    }
    .daterangepicker .ranges li {
        padding: 5px 12px;
    }
    .daterangepicker .ranges li.active {
        background-color: #007bff;
    }
    .form-check-input {
        width: 3em;
        height: 1.5em;
        margin-right: 10px;
    }

    .form-check-label {
        font-size: 1.1em;
        font-weight: 500;
    }

    .form-switch {
        padding-left: 3.5em;
    }
</style>
<div class="container mt-4">
    <div class="container d-flex justify-content-between align-items-center mb-3">
        <h1>AirTanker Log File</h1>
    </div>

    <div class="filter-section">
        <form action="/logs" method="get">
            <div class="row">
                <div class="col-md-9">
                    <div class="form-group">
                        <label for="search">Search:</label>
                        <input type="text" id="search" name="search" class="form-control" value="{{ search_query }}" placeholder="Search logs...">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="sort">Sort Order:</label>
                        <select name="sort" id="sort" class="form-control">
                            <option value="desc" {{ 'selected' if sort_order == 'desc' else '' }}>Newest First</option>
                            <option value="asc" {{ 'selected' if sort_order == 'asc' else '' }}>Oldest First</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="row date-inputs">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="start_date">Start Date:</label>
                        <input type="date" id="start_date" name="start_date" class="form-control" value="{{ start_date }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="end_date">End Date:</label>
                        <input type="date" id="end_date" name="end_date" class="form-control" value="{{ end_date }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="level">Log Level:</label>
                        <select name="level" id="level" class="form-control">
                            <option value="ALL" {{ 'selected' if log_level == 'ALL' else '' }}>All</option>
                            <option value="ERROR" {{ 'selected' if log_level == 'ERROR' else '' }}>Error</option>
                            <option value="DEBUG" {{ 'selected' if log_level == 'DEBUG' else '' }}>Debug</option>
                            <option value="INFO" {{ 'selected' if log_level == 'INFO' else '' }}>Info</option>
                            <option value="WARNING" {{ 'selected' if log_level == 'WARNING' else '' }}>Warning</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3 d-flex align-items-end filter-log-buttons">
                    <div class="w-100 d-flex justify-content-between">
                        <a href="/logs" class="btn btn-secondary">Clear Filters</a>
                        <button type="submit" class="btn btn-primary">Apply Filters</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div class="log-container">
        {% if log_content %}
            {% for line in log_content %}
                <div>{{ line|safe }}</div>
            {% endfor %}
        {% else %}
            <div class="text-center py-4">
                <p class="text-muted">No log entries found matching the current filters.</p>
            </div>
        {% endif %}
    </div>
</div>

{% endblock %}

{% block scripts %}
<!-- Add daterangepicker dependencies after base scripts -->
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize with existing values if they exist
    var start = "{{ start_date }}" ? moment("{{ start_date }}") : null;
    var end = "{{ end_date }}" ? moment("{{ end_date }}") : null;
    
    var initialRange = 'All Time';
    if (start && end) {
        initialRange = start.format('YYYY-MM-DD') + ' - ' + end.format('YYYY-MM-DD');
    } else if (start) {
        initialRange = 'After ' + start.format('YYYY-MM-DD');
    } else if (end) {
        initialRange = 'Before ' + end.format('YYYY-MM-DD');
    }

    $('#daterange').daterangepicker({
        startDate: start || moment(),
        endDate: end || moment(),
        autoUpdateInput: false,
        locale: {
            cancelLabel: 'Clear',
            format: 'YYYY-MM-DD'
        },
        ranges: {
           'Today': [moment(), moment()],
           'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
           'Last 7 Days': [moment().subtract(6, 'days'), moment()],
           'Last 30 Days': [moment().subtract(29, 'days'), moment()],
           'This Month': [moment().startOf('month'), moment().endOf('month')],
           'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
           'All Time': [null, null]
        }
    });

    $('#daterange').val(initialRange);

    $('#daterange').on('apply.daterangepicker', function(ev, picker) {
        if (picker.startDate.format('YYYY-MM-DD') === moment().format('YYYY-MM-DD') &&
            picker.endDate.format('YYYY-MM-DD') === moment().format('YYYY-MM-DD')) {
            $(this).val('Today');
        } else {
            $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
        }
        $('#start_date').val(picker.startDate.format('YYYY-MM-DD'));
        $('#end_date').val(picker.endDate.format('YYYY-MM-DD'));
    });

    $('#daterange').on('cancel.daterangepicker', function(ev, picker) {
        $(this).val('All Time');
        $('#start_date').val('');
        $('#end_date').val('');
    });
});
</script>
{% endblock %}
