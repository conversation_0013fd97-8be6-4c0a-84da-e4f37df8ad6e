from enum import Enum
from dataclasses import dataclass
from typing import List, Optional
from enum import Enum
import re

class Status(Enum):
    MATCHED = "matched"
    AMBIGUOUS = "ambiguous"
    NOT_FOUND = "not_found"

class Origin(Enum):
    ACTIVE_WO = "active_work_order"
    DATABASE = "database"

@dataclass
class NameMatch:
    name: str
    status: Status
    origin: Origin
    ids: Optional[List[int]] # If MATCHED, ids has length 1; AMBIGUOUS length >1; NOT_FOUND → None

def normalize_name(name: str) -> str:
    name = name.strip().lower()
    name = name = re.sub(r"[^\w\s]", "", name) # Remove punctuation and other symbols, leaving only letters, digits, underscores, and whitespace
    name = re.sub(r"\s+", " ", name) # Remove multiple spaces
    return name.strip().lower()