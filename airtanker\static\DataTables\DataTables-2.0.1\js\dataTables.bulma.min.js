/*! DataTables Bulma integration
 * ©2020 SpryMedia Ltd - datatables.net/license
 */
!function(t){var a,i;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(n){return t(n,window,document)}):"object"==typeof exports?(a=require("jquery"),i=function(n,e){e.fn.dataTable||require("datatables.net")(n,e)},"undefined"==typeof window?module.exports=function(n,e){return n=n||window,e=e||a(n),i(n,e),t(e,0,n.document)}:(i(window,a),module.exports=t(a,window,window.document))):t(jQuery,window,document)}(function(r,n,e){"use strict";var t=r.fn.dataTable;return r.extend(!0,t.defaults,{renderer:"bulma"}),r.extend(!0,t.ext.classes,{container:"dt-container dt-bulma",search:{input:"input"},length:{input:"custom-select custom-select-sm form-control form-control-sm"},processing:{container:"dt-processing card"}}),t.ext.renderer.pagingButton.bulma=function(n,e,t,a,i){var o=["pagination-link"],a=(a&&o.push("is-current"),r("<li>"));return{display:a,clicker:r("<a>",{href:i?null:"#",class:o.join(" "),disabled:i}).html(t).appendTo(a)}},t.ext.renderer.pagingContainer.bulma=function(n,e){var t=r('<nav class="pagination" role="navigation" aria-label="pagination"><ul class="pagination-list"></ul></nav>');return t.find("ul").append(e),t},t.ext.renderer.layout.bulma=function(n,e,t){var a={},i=r("<div/>",{class:"columns is-multiline"}).appendTo(e);r.each(t,function(n,e){var t;e.table?t="column is-full":"start"===n?t="column is-narrow":"end"===n?(t="column is-narrow",a.marginLeft="auto"):(t="column is-full",a.marginLeft="auto",a.marginRight="auto"),r("<div/>",{id:e.id||null,class:t+" "+(e.className||"")}).css(a).append(e.contents).appendTo(i)})},r(e).on("init.dt",function(n,e){"dt"===n.namespace&&(n=new r.fn.dataTable.Api(e),r("div.dt-length select",n.table().container()).wrap('<div class="select">'))}),t});