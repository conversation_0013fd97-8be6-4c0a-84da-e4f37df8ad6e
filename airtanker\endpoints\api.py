from main import airtanker_app
from flask import render_template, request, jsonify, session, flash
from endpoints.decorators import requires_authentication, requires_odoo_authentication
from services.DatabaseService import DatabaseService
import datetime
from services.OdooService import OdooService
from collections import defaultdict
import markdown
from decimal import Decimal


@airtanker_app.route('/api/employee_details_daily', methods=['GET'])
@requires_authentication
def get_employee_details_daily():
    '''This fetches all daily entries based on week ending date'''

    weekEnding = request.args.get('weekEnding')

    database_service = DatabaseService()
    database_service.connect()

    data = database_service.get_employee_details_daily(weekEnding)

    database_service.disconnect()

    return jsonify(data)


@airtanker_app.route('/api/employee_details_daily_by_timesheetID', methods=['GET'])
@requires_authentication
def get_employee_details_daily_by_timesheetID():
    '''This fetches all daily entries based on timesheet ID'''

    timesheetID = request.args.get('timesheetID')

    database_service = DatabaseService()
    database_service.connect()

    data = database_service.get_employee_details_daily_by_timesheetID(timesheetID)

    database_service.disconnect()

    return jsonify(data)


@airtanker_app.route('/api/employee_details_weekly', methods=['GET'])
@requires_authentication
def get_employee_details_weekly():
    '''This fetches all entries based on week ending date'''

    weekEnding = request.args.get('weekEnding')

    database_service = DatabaseService()
    database_service.connect()

    data = database_service.get_employee_details_weekly(weekEnding)

    database_service.disconnect()

    return jsonify(data)


@airtanker_app.route('/api/employee_details_allEntries', methods=['GET'])
@requires_authentication
def get_employee_details_allEntries():
    '''This fetches the all the entries based on a timesheetEntryID'''

    timesheetEntryID = request.args.get('timesheetEntryID')

    database_service = DatabaseService()
    database_service.connect()

    # Sample data for testing
    data = database_service.get_daily_breakdown_by_timesheetEntryID(timesheetEntryID)

    database_service.disconnect()

    return jsonify(data)


@airtanker_app.route('/api/update_status/timesheets', methods=['POST'])
@requires_authentication
def update_status_timesheets():
    '''This updates the rows in the approvals screen'''
    timeSheetID = request.form.get('timesheetID')
    statusID = request.form.get('statusID')
    hours_source = request.form.get('type')
    message = "" #request.form.get('message')
    hours = None
    message = None
    # If approved or approved with errors
    if (statusID == '3' or statusID == '6'):
        hours = request.form.get('hours')
        hours_type = request.form.get('type')
        if hours_type == "adpHours":
            hours_type = "ADP"
        elif hours_type == "cHours":
            hours_type = "Customer"
        elif hours_type == "eHours":
            hours_type = "Internal"
        message = f'Used {hours_type} as the hours for approved with errors.'


    database_service = DatabaseService()
    database_service.connect()

    # Sample data for testing
    error = database_service.update_timesheet_status(timeSheetID, statusID, hours, hours_source)
    if error:
        # Return an error response if timesheetID or statusID is missing
        database_service.disconnect()
        return jsonify(message=error, status='error'), 400
    else:
        database_service.insert_audit_record(timeSheetID, statusID, session['username'], message)
        database_service.disconnect()
        return jsonify(message='Status updated successfully', status='success'), 200
    

######## Edit Files in DB ########
@airtanker_app.route('/get-processed-files', methods=['GET'])
@requires_authentication
def get_processed_files():
    database_service = DatabaseService()
    database_service.connect()

    data = database_service.get_processed_files()
    database_service.disconnect()
    return jsonify(data=data)


@airtanker_app.route('/find_create_task_id', methods=['GET'])
@requires_authentication
def find_create_task_id():
    results = {}
    database_service = DatabaseService()
    database_service.connect()

    results["ArriveTask"] = database_service.find_or_create_task("Travel Arrival")
    results["DepartTask"]= database_service.find_or_create_task("Travel Departure")
    results["SupportHours"]= database_service.find_or_create_task("Support Hours")

    database_service.disconnect()
    return jsonify(results=results)


@airtanker_app.route('/edit-files', methods=['GET', 'POST'])
@requires_authentication
def edit_files():
    if request.method == 'POST':
        database_service = DatabaseService()
        database_service.connect()

        file_ids = request.json.get('fileIDs', [])
        for fileId in file_ids:
            database_service.delete_data_with_fileID(fileId)

        response_data = {'message': 'Files deleted successfully'}
        database_service.disconnect()

        return jsonify(response_data)
    else:
        return render_template('endpoints/edit_files.html')
# endregion

### User for Dashboards ###
@airtanker_app.route('/api/approvals/timesheets', methods=['GET'])
@requires_authentication
def api_approvals_timesheets():
    database_service = DatabaseService()
    database_service.connect()

    # Execute the query for the Timesheets table
    query_timesheets = """
        SELECT TOP(3)
            t.WeekEnding,
            COUNT(*) AS TotalCount,
            SUM(CASE WHEN t.ApprovedHours IS NOT NULL THEN 1 ELSE 0 END) AS ApprovedCount,
            SUM(CASE WHEN t.Bill_Created IS NOT NULL AND (e.Type IS NULL OR e.Type != 'Internal') THEN 1 ELSE 0 END) AS BillCount,
            SUM(CASE WHEN (e.Type IS NULL OR e.Type != 'Internal') THEN 1 ELSE 0 END) AS BillTotalCount,
            SUM(CASE WHEN t.Invoice_Created IS NOT NULL THEN 1 ELSE 0 END) AS InvoiceCount,
            SUM(CASE WHEN t.Timesheet_Created IS NOT NULL THEN 1 ELSE 0 END) AS TimesheetCount,
            (SUM(CASE WHEN t.ApprovedHours IS NOT NULL THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS ApprovalPercentage,
            (SUM(CASE WHEN t.Bill_Created IS NOT NULL AND (e.Type IS NULL OR e.Type != 'Internal') THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS BillPercentage,
            (SUM(CASE WHEN t.Invoice_Created IS NOT NULL THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS InvoicePercentage,
            (SUM(CASE WHEN t.Timesheet_Created IS NOT NULL THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) AS TimesheetPercentage
        FROM [dbo].[Timesheets] t
        INNER JOIN [dbo].[Employees] e
            ON t.EmployeeID = e.EmployeeID
        GROUP BY t.WeekEnding
        ORDER BY t.WeekEnding DESC;
    """

    results_timesheets = database_service.execute_query(query=query_timesheets)

    # Execute the query for the Internal_Timesheets table
    query_internal_timesheets = """
        SELECT TOP(3)
            it.WeekEnding,
            COUNT(*) AS TotalCount,
            SUM(CASE WHEN it.Timesheet_Created IS NOT NULL THEN 1 ELSE 0 END) AS TimesheetCount
        FROM [dbo].[Internal_Timesheets] it
        INNER JOIN [dbo].[Employees] e
            ON it.EmployeeID = e.EmployeeID
        GROUP BY it.WeekEnding
        ORDER BY it.WeekEnding DESC;
    """
    results_internal_timesheets = database_service.execute_query(query=query_internal_timesheets)

    # Check if the results are None and initialize as empty lists if needed
    if results_timesheets is None:
        results_timesheets = []

    if results_internal_timesheets is None:
        results_internal_timesheets = []

    # Convert results to dictionaries for easy merging
    timesheets_dict = {row["WeekEnding"]: row for row in results_timesheets}
    internal_timesheets_dict = {row["WeekEnding"]: row for row in results_internal_timesheets}

    # Merge results
    combined_data = []
    for week_ending, timesheets_row in timesheets_dict.items():
        internal_row = internal_timesheets_dict.get(week_ending, {})
        combined_total_count = timesheets_row["TotalCount"] + internal_row.get("TotalCount", 0)
        combined_timesheet_count = timesheets_row["TimesheetCount"] + internal_row.get("TimesheetCount", 0)
        
        combined_data.append({
            "WeekEnding": week_ending.strftime('%Y-%m-%d'),
            "TotalCount": timesheets_row["TotalCount"],

            "ApprovalPercentage": float(timesheets_row["ApprovalPercentage"]),
            "ApprovedCount": timesheets_row["ApprovedCount"],

            'BillCount': timesheets_row["BillCount"],
            'BillTotalCount': timesheets_row["BillTotalCount"],
            'BillPercentage': timesheets_row["BillPercentage"],

            'InvoiceCount': timesheets_row["InvoiceCount"],
            'InvoicePercentage': timesheets_row["InvoicePercentage"],

            'TimesheetTotalCount': combined_total_count, # Combined total all entries
            'TimesheetCount': combined_timesheet_count, # Make Combined - Total exported
            'TimesheetPercentage': (combined_timesheet_count * 100.0 / combined_total_count) if combined_total_count else 0, # Make Combined
        })

    # Sort combined data if needed
    combined_data_sorted = sorted(combined_data, key=lambda x: x["WeekEnding"], reverse=True)

    # Now combined_data_sorted contains the merged and calculated results

    database_service.disconnect()
    return jsonify(combined_data_sorted)


# TODO update the below function
@airtanker_app.route('/api/approvals/expenses', methods=['GET'])
@requires_odoo_authentication
@requires_authentication
def api_approvals_expenses():
    database_service = DatabaseService()
    database_service.connect()

    # Execute the query for the Timesheets table
    query_timesheets = """
        SELECT
            [ReportedExpenseID],
            [ExpenseDate],
            [Account],
            [Description],
            [Lodging],
            [RentalCar],
            [PerDiem],
            [Phone],
            [Misc],
            [MileageTotal],
            [FileID],
            [TimesheetEntryID],
            [EmployeeID],
            [WorkOrderID],
            [TimesheetID],
            [JobSite],
            [Airfare],
            [SiteSheetID]
        FROM [dbo].[ReportedExpenses]
    """

    results_timesheets = database_service.execute_query(query=query_timesheets)

    # Execute the query for the Internal_Timesheets table
    query_internal_timesheets = """
        SELECT TOP(3)
            it.WeekEnding,
            COUNT(*) AS TotalCount,
            SUM(CASE WHEN it.Timesheet_Created IS NOT NULL THEN 1 ELSE 0 END) AS TimesheetCount
        FROM [dbo].[Internal_Timesheets] it
        INNER JOIN [dbo].[Employees] e
            ON it.EmployeeID = e.EmployeeID
        GROUP BY it.WeekEnding
        ORDER BY it.WeekEnding DESC;
    """
    results_internal_timesheets = database_service.execute_query(query=query_internal_timesheets)

    # Check if the results are None and initialize as empty lists if needed
    if results_timesheets is None:
        results_timesheets = []

    if results_internal_timesheets is None:
        results_internal_timesheets = []

    # Convert results to dictionaries for easy merging
    timesheets_dict = {row["WeekEnding"]: row for row in results_timesheets}
    internal_timesheets_dict = {row["WeekEnding"]: row for row in results_internal_timesheets}

    # Merge results
    combined_data = []
    for week_ending, timesheets_row in timesheets_dict.items():
        internal_row = internal_timesheets_dict.get(week_ending, {})
        combined_total_count = timesheets_row["TotalCount"] + internal_row.get("TotalCount", 0)
        combined_timesheet_count = timesheets_row["TimesheetCount"] + internal_row.get("TimesheetCount", 0)
        
        combined_data.append({
            "WeekEnding": week_ending.strftime('%Y-%m-%d'),
            "TotalCount": timesheets_row["TotalCount"],

            "ApprovalPercentage": float(timesheets_row["ApprovalPercentage"]),
            "ApprovedCount": timesheets_row["ApprovedCount"],

            'BillCount': timesheets_row["BillCount"],
            'BillTotalCount': timesheets_row["BillTotalCount"],
            'BillPercentage': timesheets_row["BillPercentage"],

            'InvoiceCount': timesheets_row["InvoiceCount"],
            'InvoicePercentage': timesheets_row["InvoicePercentage"],

            'TimesheetTotalCount': combined_total_count, # Combined total all entries
            'TimesheetCount': combined_timesheet_count, # Make Combined - Total exported
            'TimesheetPercentage': (combined_timesheet_count * 100.0 / combined_total_count) if combined_total_count else 0, # Make Combined
        })

    # Sort combined data if needed
    combined_data_sorted = sorted(combined_data, key=lambda x: x["WeekEnding"], reverse=True)

    # Now combined_data_sorted contains the merged and calculated results

    database_service.disconnect()
    return jsonify(combined_data_sorted)


# TODO update the below function
@airtanker_app.route('/api/update_status/expenses', methods=['POST'])
@requires_authentication
def update_status_expenses():
    '''This updates the rows in the approvals screen'''
    timeSheetID = request.form.get('timesheetID')
    statusID = request.form.get('statusID')
    expenses_type = request.form.get('type')
    message = "" #request.form.get('message')
    expenses = None
    message = None
    # If approved or approved with errors
    if (statusID == '3' or statusID == '6'):
        expenses = request.form.get('expenses')
        expenses = float(expenses.replace('$', ''))
        message = f'Used {expenses_type} as the expenses for approved with errors.'
    else:
        message = "Denied errors"


    database_service = DatabaseService()
    database_service.connect()

    # Sample data for testing
    error = database_service.update_timesheet_status(timeSheetID, statusID, expenses, expenses_type, timesheets=False)
    if error:
        # Return an error response if timesheetID or statusID is missing
        database_service.disconnect()
        return jsonify(message=error, status='error'), 400
    else:
        database_service.insert_audit_record(timeSheetID, statusID, session['username'], message)
        database_service.disconnect()
        return jsonify(message='Status updated successfully', status='success'), 200


@airtanker_app.route('/api/employee_details_weekly/expenses', methods=['GET'])
@requires_odoo_authentication
@requires_authentication
def get_employee_details_weekly_expenses():
    '''This fetches all entries based on week ending date'''

    weekEnding = request.args.get('weekEnding')

    database_service = DatabaseService()
    database_service.connect()

    odoo_service = session['odoo_service'] # type: OdooService

    query = """
        SELECT *
        FROM [dbo].[vw_ReportedExpensesWithTimesheets]
        WHERE WeekEnding = ?
    """
    results = database_service.execute_query(query=query, parameters=(weekEnding))

    get_daily_entries_query = """
            SELECT [Source]
                ,[TimesheetID]
                ,[WeekEnding]
                ,[FileName]
                ,[TimeSheetEntryID]
                ,[FirstName]
                ,[LastName]
                ,[EmployeeID]
                ,[Date]
                ,[ReportedHours]
                ,[WorkOrderNumber]
                ,[WorkOrderID]
                ,[SiteSheetID]
                ,[ProjectNumber]
                ,[TaskName]
            FROM [dbo].[vw_EmployeeDetailsAllUnion]
            WHERE TimesheetEntryID IN ({}) AND (Source = 'Internal' OR Source = 'ADP') 
        """

    query_get_daily_reported_expenses = """
        SELECT *
        FROM [dbo].[ReportedExpenses]
        WHERE TimesheetEntryID IN ({})
    """
    
    for result in results: # results is all the expense reports, they contain timesheet entry IDs so we can compare travel hours

        expense_array = odoo_service.get_expenses_for_site_sheet(result['SiteSheetID'])
        result["ExpenseArray"] = expense_array
        result["Notes"] = ""
        result["DailyExpenseBreakdown"] = []
        total_expenses_allowed = 0

        # Loop through their time to see if they have travel
        if result['TimesheetEntryIDs']:
            timesheet_entry_ids = result['TimesheetEntryIDs'].split(',')
            placeholders = ', '.join(['?'] * len(timesheet_entry_ids))
            formatted_query = get_daily_entries_query.format(placeholders)
            reported_formated_query = query_get_daily_reported_expenses.format(placeholders)
            timesheet_entries = database_service.execute_query(
                query=formatted_query,
                parameters=timesheet_entry_ids
            )

            reported_expenses = database_service.execute_query(
                query=reported_formated_query,
                parameters=timesheet_entry_ids
            )
            result['TravelEntries'] = []

            first_name = result["FirstName"]
            last_name = result["LastName"]

            if not timesheet_entries:
                airtanker_app.logger.error(f'No timesheets found for {reported_expenses} for {first_name} {last_name}, Check the dates they used.')


            grouped_entries = defaultdict(list)
            for entry in timesheet_entries:
                grouped_entries[entry['Date']].append(entry)

            # Convert to 2D array
            grouped_entries_list = list(grouped_entries.values())

            # This is looping through every working day grouped by date,
            # we also need to grab the reported expense element for that data from the result["ReportedDailyExpenses"]
            reported_expenses_copy = reported_expenses

            for timesheet_entry_array_grouped_by_date in grouped_entries_list: # this is a 2D array, the elements are 1D arrays
                
                # Grab the reported expense for the day if it exists.
                reported_expense = None
                for exp in reported_expenses:
                    if exp["ExpenseDate"] == timesheet_entry_array_grouped_by_date[0]["Date"]:
                        reported_expense = exp
                        reported_expenses_copy.remove(exp)  # Remove the element from the copy, we'll loop through later
                        # this exists because sometimes they will have the day off but still get lodging and stuff.
                        break
                
                if reported_expense is None:
                    continue

                indexes_with_travel = [
                    index for index, entry in enumerate(timesheet_entry_array_grouped_by_date)
                    if "TRAVEL" in entry['TaskName'].upper()
                ]

                if timesheet_entries[0]["FirstName"] == 'Jefferson':
                    pass

                # Check if any entry contains "TRAVEL"
                contains_travel = bool(indexes_with_travel)
                if contains_travel:
                    hours = 0
                    for index in indexes_with_travel:
                        hours += timesheet_entry_array_grouped_by_date[index]['ReportedHours']

                        # if all travel hours are calculated for the day
                        if indexes_with_travel[-1] == index:
                            ## Get the allowed expenses, and compare hours
                            if 'ARRI' in timesheet_entry_array_grouped_by_date[index]['TaskName'].upper():                        
                                if expense_array['x_arrival_expense']:
                                    total_expenses_allowed += float(add_expenses(expense_array['x_arrival_expense'][0]))
                                    if hours > expense_array['x_arrival_expense'][0]['x_travel_time']:
                                        if not "Arrival travel hours are greater than the allowed travel hours.":
                                            # add error
                                            result["Notes"] = "Arrival travel hours are greater than the allowed travel hours.\n"

                                    # Add reported Expense breakdown
                                    result["DailyExpenseBreakdown"].append({
                                        "FirstName": first_name,
                                        "LastName": last_name,
                                        "Date": timesheet_entry_array_grouped_by_date[0]["Date"],
                                        "ReportedExpenses": reported_expense,
                                        "AllowedExpenses": expense_array['x_departure_expense'],
                                        "Type": "Arrive",
                                        "TravelHours": hours
                                    })
                            else:
                                if expense_array['x_departure_expense']:
                                    total_expenses_allowed += float(add_expenses(expense_array['x_departure_expense'][0]))
                                    if hours > expense_array['x_departure_expense'][0]['x_travel_time']:
                                        if not "Departure travel hours are greater than the allowed travel hours.":
                                            # add error
                                            result["Notes"] = "Departure travel hours are greater than the allowed travel hours.\n"

                                    result["DailyExpenseBreakdown"].append({
                                        "FirstName": first_name,
                                        "LastName": last_name,
                                        "Date": timesheet_entry_array_grouped_by_date[0]["Date"],
                                        "ReportedExpenses": reported_expense,
                                        "AllowedExpenses": expense_array['x_departure_expense'],
                                        "Type": "Depart",
                                        "TravelHours": hours
                                    })
                else:
                    if 'x_working_expense' in expense_array and expense_array['x_working_expense']:
                        total_expenses_allowed += float(add_expenses(expense_array['x_working_expense'][0]))
                        result["DailyExpenseBreakdown"].append({
                            "FirstName": first_name,
                            "LastName": last_name,
                            "Date": timesheet_entry_array_grouped_by_date[0]["Date"],
                            "ReportedExpenses": reported_expense,
                            "AllowedExpenses": expense_array['x_working_expense'],
                            "Type": "Working",
                            "TravelHours": 0
                        })
                    
            if len(reported_expenses_copy) > 0:
                for expense in reported_expenses_copy:

                    # first check if it's just an empty entry from their timesheet. No expenses reported for that day. If not empty, then add it
                    if not is_empty_expense(expense) and 'x_working_expense' in expense_array:
                        total_expenses_allowed += float(add_expenses(expense_array['x_working_expense'][0]))
                        result["DailyExpenseBreakdown"].append({
                            "FirstName": first_name,
                            "LastName": last_name,
                            "Date": expense["ExpenseDate"],
                            "ReportedExpenses": expense,
                            "AllowedExpenses": expense_array['x_working_expense'],
                            "Type": "Working",
                            "TravelHours": 0
                        })
        
        result['TotalAllowedExpenses'] = format_float(total_expenses_allowed)
        if total_expenses_allowed < float(result['TotalExpenses']):
            result["Notes"] = "Total reported expenses is greater than the total allowed expenses.\n"

    database_service.disconnect()

    return jsonify(results)


def is_empty_expense(expense):
    keys_to_check = ['Lodging', 'RentalCar', 'PerDiem', 'Phone', 'Misc', 'MileageTotal', 'Airfare']

    for key in keys_to_check:
        value = expense.get(key)
        if value is not None and not (isinstance(value, Decimal) and value == Decimal('0.00')):
            return False
    return True


def format_float(value):
    if value == int(value):
        # If the value is a whole number, format with two decimal places
        return f"{value:.2f}"
    else:
        # Otherwise, return the value as is
        return str(value)


def add_expenses(expense):
    numerical_keys = [
        'x_lodging', 
        'x_perdiem',        
        'x_misc', 
        'x_airfare', 
        'x_vehicle'
    ]
    miles_allowed = float(expense['x_mileage'])
    mileage_rate = float(expense['x_mileage_rate'])

    total_miles_expenses_allowed = miles_allowed * mileage_rate

    total = sum(expense[key] for key in numerical_keys if key in expense)

    expense['total_allowed_mileage_expense'] = total_miles_expenses_allowed
    
    total += total_miles_expenses_allowed
    
    return total


@airtanker_app.route('/releases', methods=['GET'])
@requires_authentication
def get_release_information():
    try:
        with open('releases.md', 'r') as file:
            content = file.read()
    except FileNotFoundError:
        content = "Releases file not found."

    # Convert Markdown to HTML
    html_content = markdown.markdown(content)
    
    # Render the HTML content within the template
    return render_template('endpoints/releases.html', content=html_content)
