from flask_wtf import <PERSON>laskF<PERSON>
from wtforms import <PERSON>Field, TextAreaField, SelectField, HiddenField
from wtforms.validators import DataRequired, Length, ValidationError
import json


class TemplateForm(FlaskForm):
    """Form for creating and editing templates."""
    
    # Hidden field for template ID (used for updates)
    template_id = HiddenField()
    
    # Template basic information
    name = StringField(
        'Template Name',
        validators=[
            DataRequired(message="Template name is required"),
            Length(min=1, max=255, message="Template name must be between 1 and 255 characters")
        ],
        render_kw={
            "class": "form-control",
            "placeholder": "Enter template name (e.g., 'Customer Timesheet Parser')"
        }
    )
    
    type = SelectField(
        'Template Type',
        choices=[
            ('', 'Select a type...'),
            ('customer_timesheets', 'Customer Timesheets'),
            ('internal_timesheets', 'Internal Timesheets'), # not tested
            ('expense_reports', 'Expense Reports'), # not tested
            # ('general', 'General Purpose'),
            # ('custom', 'Custom')
        ],
        render_kw={"class": "form-select"}
    )
    
    identifiers = StringField(
        'Identifiers',
        validators=[Length(max=1000, message="Identifiers must be less than 1000 characters")],
        render_kw={
            "class": "form-control",
            "placeholder": "Enter comma-separated identifiers (e.g., 'timesheet, hours, employee')"
        }
    )
    
    prompt = TextAreaField(
        'AI Prompt',
        validators=[
            DataRequired(message="Prompt is required"),
            Length(min=10, max=5000, message="Prompt must be between 10 and 5000 characters")
        ],
        render_kw={
            "class": "form-control",
            "rows": 8,
            "placeholder": "Enter the AI prompt that will be used to process data..."
        }
    )
    
    response_schema = TextAreaField(
        'Response Schema (JSON)',
        validators=[
            DataRequired(message="Response schema is required")
        ],
        render_kw={
            "class": "form-control",
            "rows": 15,
            "placeholder": "Enter the JSON schema that defines the expected response structure..."
        }
    )
    
    def validate_response_schema(self, field):
        """Validate that the response schema is valid JSON."""
        try:
            schema = json.loads(field.data)
            
            # Basic JSON schema validation
            if not isinstance(schema, dict):
                raise ValidationError("Response schema must be a JSON object")
            
            # Check for basic JSON schema structure
            if 'type' not in schema:
                raise ValidationError("Response schema must include a 'type' field")
            
        except json.JSONDecodeError as e:
            raise ValidationError(f"Invalid JSON format: {str(e)}")
        except Exception as e:
            raise ValidationError(f"Schema validation error: {str(e)}")
    
    def populate_from_template(self, template):
        """Populate form fields from a Template object."""
        self.template_id.data = template.id
        self.name.data = template.name
        self.type.data = template.type or ''
        self.identifiers.data = template.get_identifiers_as_string()
        self.prompt.data = template.prompt
        self.response_schema.data = template.get_response_schema_as_json_string()
    
    def get_template_data(self):
        """Get template data from form fields."""
        return {
            'id': int(self.template_id.data) if self.template_id.data else None,
            'name': self.name.data.strip(),
            'type': self.type.data if self.type.data else None,
            'identifiers': [id.strip() for id in self.identifiers.data.split(',') if id.strip()] if self.identifiers.data else None,
            'prompt': self.prompt.data.strip(),
            'response_schema': json.loads(self.response_schema.data)
        }


class TemplateDeleteForm(FlaskForm):
    """Simple form for template deletion confirmation."""
    
    template_id = HiddenField(validators=[DataRequired()])
    confirm_delete = HiddenField(default="true")
