def is_two_digit_or_float(s):
    try:
        # Attempt to convert the string to a float
        value = float(s)
        
        # For integers and floats, check the integral part's length
        integral_part = str(s).split('.')[0]  # Extract integral part before the decimal point
        if len(integral_part) > 2:
            return False  # More than two digits in the integral part
        
        # If it's a valid float or integer with at most 2 digits in the integral part, return True
        return True, s
    except:
        # If conversion to float fails, it's not a number
        return False, s