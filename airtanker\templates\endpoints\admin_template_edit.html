{% extends "base.html" %}

{% block styles %}
  <link rel="stylesheet" href="{{ url_for('static', filename='css/extra.css') }}" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" />
  <style>
    .template-form-card {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      padding: 30px;
      margin-bottom: 20px;
    }
    
    .form-section {
      margin-bottom: 25px;
    }
    
    .form-section:last-child {
      margin-bottom: 0;
    }
    
    .form-label {
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
      display: block;
    }
    
    .form-help {
      font-size: 0.875rem;
      color: #6c757d;
      margin-top: 5px;
    }
    
    .json-editor {
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 0.875rem;
      line-height: 1.4;
    }
    
    .btn-group-actions {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
    }
    
    .template-header {
      border-bottom: 2px solid #f0f0f0;
      margin-bottom: 30px;
      padding-bottom: 15px;
    }
    
    .error-message {
      color: #dc3545;
      font-size: 0.875rem;
      margin-top: 5px;
    }
    
    .json-preview {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      padding: 15px;
      margin-top: 10px;
      max-height: 200px;
      overflow-y: auto;
    }
    
    .json-preview pre {
      margin: 0;
      font-size: 0.875rem;
      color: #333;
    }

    /* Schema Editor Styles */
    .schema-editor-tabs {
      border-bottom: 1px solid #dee2e6;
      margin-bottom: 20px;
    }

    .schema-editor-tabs .nav-link {
      border: none;
      border-bottom: 2px solid transparent;
      color: #6c757d;
      font-weight: 500;
    }

    .schema-editor-tabs .nav-link.active {
      color: #0d6efd;
      border-bottom-color: #0d6efd;
      background: none;
    }

    .property-item {
      border: 1px solid #dee2e6;
      border-radius: 6px;
      padding: 15px;
      margin-bottom: 10px;
      background: #f8f9fa;
    }

    .property-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 10px;
    }

    .property-name {
      flex: 1;
      font-weight: 600;
    }

    .property-controls {
      display: flex;
      gap: 10px;
      align-items: center;
    }

    .property-details {
      display: grid;
      grid-template-columns: 1fr 1fr 2fr;
      gap: 15px;
      align-items: start;
    }

    .enum-items {
      margin-top: 10px;
    }

    .enum-item {
      display: flex;
      gap: 5px;
      margin-bottom: 5px;
    }

    .enum-item input {
      flex: 1;
    }

    .btn-sm {
      padding: 0.25rem 0.5rem;
      font-size: 0.875rem;
    }

    .add-property-section {
      border: 2px dashed #dee2e6;
      border-radius: 6px;
      padding: 20px;
      text-align: center;
      margin-top: 15px;
      background: #fafafa;
    }

    .add-property-section:hover {
      border-color: #0d6efd;
      background: #f8f9ff;
    }

    .schema-preview {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      padding: 15px;
      margin-top: 15px;
      max-height: 300px;
      overflow-y: auto;
    }
  </style>
{% endblock %}

{% block scripts %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // JSON validation and preview
      const responseSchemaField = document.getElementById('response_schema');
      const jsonPreview = document.getElementById('json-preview');

      // Schema editor state
      let schemaProperties = [];

      // Initialize with existing schema if present
      function initializeSchemaEditor() {
        try {
          const existingSchema = responseSchemaField.value.trim();
          if (existingSchema) {
            const parsed = JSON.parse(existingSchema);
            if (parsed.properties && parsed.properties.timeentries &&
                parsed.properties.timeentries.items &&
                parsed.properties.timeentries.items.properties) {
              const props = parsed.properties.timeentries.items.properties;
              const required = parsed.properties.timeentries.items.required || [];

              schemaProperties = Object.keys(props).map(key => {
                const propType = props[key].type || 'string';
                const hasEnum = props[key].enum && props[key].enum.length > 0;
                return {
                  name: key,
                  type: propType,
                  displayType: (propType === 'string' && hasEnum) ? 'enum' : propType,
                  description: props[key].description || '',
                  required: required.includes(key),
                  enum: props[key].enum || []
                };
              });
            }
          }
        } catch (e) {
          console.log('Could not parse existing schema for editor');
        }
        renderSchemaEditor();
      }

      function validateAndPreviewJSON() {
        try {
          const jsonValue = responseSchemaField.value.trim();
          if (jsonValue) {
            const parsed = JSON.parse(jsonValue);
            const formatted = JSON.stringify(parsed, null, 2);
            jsonPreview.innerHTML = '<pre><code class="language-json">' + formatted + '</code></pre>';
            Prism.highlightElement(jsonPreview.querySelector('code'));
            jsonPreview.style.display = 'block';
          } else {
            jsonPreview.style.display = 'none';
          }
        } catch (e) {
          jsonPreview.innerHTML = '<div class="error-message">Invalid JSON: ' + e.message + '</div>';
          jsonPreview.style.display = 'block';
        }
      }

      if (responseSchemaField) {
        responseSchemaField.addEventListener('input', validateAndPreviewJSON);
        validateAndPreviewJSON(); // Initial validation
      }

      // Form validation
      const form = document.querySelector('form');
      if (form) {
        form.addEventListener('submit', function(e) {
          const responseSchema = document.getElementById('response_schema').value.trim();
          if (responseSchema) {
            try {
              JSON.parse(responseSchema);
            } catch (error) {
              e.preventDefault();
              alert('Please fix the JSON syntax in the Response Schema field before submitting.');
              return false;
            }
          }
        });
      }

      // Schema Editor Functions
      function renderSchemaEditor() {
        const container = document.getElementById('schema-properties-container');
        if (!container) return;

        container.innerHTML = '';

        if (schemaProperties.length === 0) {
          container.innerHTML = `
            <div class="text-center text-muted py-4">
              <i class="ri-file-list-3-line" style="font-size: 2rem; opacity: 0.5;"></i>
              <p class="mb-0 mt-2">No properties defined yet</p>
              <small>Add properties below to define the timeentry structure</small>
            </div>
          `;
          updateSchemaPreview();
          return;
        }

        schemaProperties.forEach((prop, index) => {
          const propertyDiv = document.createElement('div');
          propertyDiv.className = 'property-item';
          propertyDiv.innerHTML = `
            <div class="property-header">
              <div class="property-name">${prop.name}</div>
              <div class="property-controls">
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeProperty(${index})">
                  <i class="ri-delete-bin-line"></i>
                </button>
              </div>
            </div>
            <div class="property-details">
              <div>
                <label class="form-label">Type</label>
                <select class="form-select form-select-sm" onchange="updateProperty(${index}, 'type', this.value)">
                  <option value="string" ${prop.displayType === 'string' ? 'selected' : ''}>String</option>
                  <option value="number" ${prop.displayType === 'number' ? 'selected' : ''}>Number</option>
                  <option value="boolean" ${prop.displayType === 'boolean' ? 'selected' : ''}>Boolean</option>
                  <option value="enum" ${prop.displayType === 'enum' ? 'selected' : ''}>Enum (String)</option>
                  <option value="array" ${prop.displayType === 'array' ? 'selected' : ''}>Array</option>
                  <option value="object" ${prop.displayType === 'object' ? 'selected' : ''}>Object</option>
                </select>
              </div>
              <div>
                <label class="form-label">Required</label>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" ${prop.required ? 'checked' : ''}
                         onchange="updateProperty(${index}, 'required', this.checked)">
                  <label class="form-check-label">Required field</label>
                </div>
              </div>
              <div>
                <label class="form-label">Description</label>
                <textarea class="form-control form-control-sm" rows="2"
                          onchange="updateProperty(${index}, 'description', this.value)"
                          placeholder="Field description...">${prop.description}</textarea>
              </div>
            </div>
            ${prop.displayType === 'enum' ? `
              <div class="enum-section" style="margin-top: 15px;">
                <label class="form-label">Enum Values</label>
                <div class="enum-items" id="enum-items-${index}">
                  ${prop.enum.map((enumValue, enumIndex) => `
                    <div class="enum-item">
                      <input type="text" class="form-control form-control-sm" value="${enumValue}"
                             onchange="updateEnumValue(${index}, ${enumIndex}, this.value)">
                      <button type="button" class="btn btn-sm btn-outline-danger"
                              onclick="removeEnumValue(${index}, ${enumIndex})">
                        <i class="ri-close-line"></i>
                      </button>
                    </div>
                  `).join('')}
                </div>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="addEnumValue(${index})">
                  <i class="ri-add-line"></i> Add Enum Value
                </button>
              </div>
            ` : ''}
          `;
          container.appendChild(propertyDiv);
        });

        updateSchemaPreview();
      }

      function addProperty() {
        const name = document.getElementById('new-property-name').value.trim();
        if (!name) {
          alert('Please enter a property name');
          return;
        }

        if (schemaProperties.some(p => p.name === name)) {
          alert('Property name already exists');
          return;
        }

        schemaProperties.push({
          name: name,
          type: 'string',
          displayType: 'string',
          description: '',
          required: false,
          enum: []
        });

        document.getElementById('new-property-name').value = '';
        renderSchemaEditor();
      }

      function removeProperty(index) {
        schemaProperties.splice(index, 1);
        renderSchemaEditor();
      }

      function updateProperty(index, field, value) {
        if (field === 'type') {
          if (value === 'enum') {
            // Convert to enum type (string with enum values)
            schemaProperties[index].type = 'string';
            schemaProperties[index].displayType = 'enum';
            if (schemaProperties[index].enum.length === 0) {
              schemaProperties[index].enum = [''];
            }
          } else {
            // Regular type
            schemaProperties[index].type = value;
            schemaProperties[index].displayType = value;
            if (value !== 'string') {
              schemaProperties[index].enum = [];
            }
          }
        } else {
          schemaProperties[index][field] = value;
        }
        renderSchemaEditor();
      }

      function addEnumValue(propertyIndex) {
        schemaProperties[propertyIndex].enum.push('');
        renderSchemaEditor();
      }

      function removeEnumValue(propertyIndex, enumIndex) {
        schemaProperties[propertyIndex].enum.splice(enumIndex, 1);
        renderSchemaEditor();
      }

      function updateEnumValue(propertyIndex, enumIndex, value) {
        schemaProperties[propertyIndex].enum[enumIndex] = value;
        updateSchemaPreview();
      }

      function updateSchemaPreview() {
        const schema = generateSchema();
        const preview = document.getElementById('schema-preview');
        if (preview) {
          preview.innerHTML = '<pre><code class="language-json">' + JSON.stringify(schema, null, 2) + '</code></pre>';
          Prism.highlightElement(preview.querySelector('code'));
        }
      }

      function generateSchema() {
        const properties = {};
        const required = [];

        schemaProperties.forEach(prop => {
          const propDef = {
            type: prop.type
          };

          if (prop.description) {
            propDef.description = prop.description;
          }

          if (prop.type === 'string' && prop.enum.length > 0) {
            propDef.enum = prop.enum.filter(e => e.trim() !== '');
          }

          properties[prop.name] = propDef;

          if (prop.required) {
            required.push(prop.name);
          }
        });

        return {
          type: "object",
          properties: {
            timeentries: {
              type: "array",
              items: {
                type: "object",
                properties: properties,
                required: required
              }
            }
          },
          required: ["timeentries"]
        };
      }

      function applySchemaToJSON() {
        const schema = generateSchema();
        responseSchemaField.value = JSON.stringify(schema, null, 2);
        validateAndPreviewJSON();
      }

      // Tab switching handlers
      document.getElementById('visual-tab').addEventListener('click', function() {
        // When switching to visual editor, parse current JSON and update visual editor
        initializeSchemaEditor();
      });

      document.getElementById('json-tab').addEventListener('click', function() {
        // When switching to JSON editor, ensure preview is updated
        validateAndPreviewJSON();
      });

      // Make functions global
      window.addProperty = addProperty;
      window.removeProperty = removeProperty;
      window.updateProperty = updateProperty;
      window.addEnumValue = addEnumValue;
      window.removeEnumValue = removeEnumValue;
      window.updateEnumValue = updateEnumValue;
      window.applySchemaToJSON = applySchemaToJSON;

      // Initialize schema editor
      initializeSchemaEditor();
    });
  </script>
{% endblock %}

{% block content %}
<div class="container mt-4">
  <div class="row">
    <div class="col-12">
      <div class="template-header">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h1>
              <i class="ri-file-code-line"></i> 
              {{ action }} Template
            </h1>
            <p class="text-muted">{{ action }} AI prompt template for data processing</p>
          </div>
          <div>
            <a href="{{ url_for('admin_templates') }}" class="btn btn-outline-secondary">
              <i class="ri-arrow-left-line"></i> Back to Templates
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Flash Messages -->
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
          {{ message }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}

  <div class="row">
    <div class="col-12">
      <div class="template-form-card">
        <form method="POST" novalidate>
          {{ form.hidden_tag() }}
          
          <div class="form-section">
            {{ form.name.label(class="form-label") }}
            {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
            {% if form.name.errors %}
              <div class="error-message">
                {% for error in form.name.errors %}{{ error }}{% endfor %}
              </div>
            {% endif %}
            <div class="form-help">A descriptive name for this template (e.g., "Customer Timesheet Parser")</div>
          </div>

          <div class="form-section">
            {{ form.type.label(class="form-label") }}
            {{ form.type(class="form-select" + (" is-invalid" if form.type.errors else "")) }}
            {% if form.type.errors %}
              <div class="error-message">
                {% for error in form.type.errors %}{{ error }}{% endfor %}
              </div>
            {% endif %}
            <div class="form-help">Category or type of data this template processes</div>
          </div>

          <div class="form-section">
            {{ form.identifiers.label(class="form-label") }}
            {{ form.identifiers(class="form-control" + (" is-invalid" if form.identifiers.errors else "")) }}
            {% if form.identifiers.errors %}
              <div class="error-message">
                {% for error in form.identifiers.errors %}{{ error }}{% endfor %}
              </div>
            {% endif %}
            <div class="form-help">Comma-separated keywords that help identify when to use this template</div>
          </div>

          <div class="form-section">
            {{ form.prompt.label(class="form-label") }}
            {{ form.prompt(class="form-control" + (" is-invalid" if form.prompt.errors else "")) }}
            {% if form.prompt.errors %}
              <div class="error-message">
                {% for error in form.prompt.errors %}{{ error }}{% endfor %}
              </div>
            {% endif %}
            <div class="form-help">The AI prompt that will be used to process data with this template</div>
          </div>

          <div class="form-section">
            {{ form.response_schema.label(class="form-label") }}

            <!-- Schema Editor Tabs -->
            <ul class="nav nav-tabs schema-editor-tabs" id="schema-tabs" role="tablist">
              <li class="nav-item" role="presentation">
                <button class="nav-link active" id="visual-tab" data-bs-toggle="tab" data-bs-target="#visual-editor"
                        type="button" role="tab" aria-controls="visual-editor" aria-selected="true">
                  <i class="ri-edit-box-line"></i> Visual Editor
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="json-tab" data-bs-toggle="tab" data-bs-target="#json-editor"
                        type="button" role="tab" aria-controls="json-editor" aria-selected="false">
                  <i class="ri-code-line"></i> JSON Editor
                </button>
              </li>
            </ul>

            <div class="tab-content" id="schema-tab-content">
              <!-- Visual Editor Tab -->
              <div class="tab-pane fade show active" id="visual-editor" role="tabpanel" aria-labelledby="visual-tab">
                <div class="mt-3">
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                      <h6 class="mb-0">Timeentry Properties</h6>
                      <small class="text-muted">Define the structure of individual timeentry objects</small>
                    </div>
                    <button type="button" class="btn btn-sm btn-primary" onclick="applySchemaToJSON()">
                      <i class="ri-refresh-line"></i> Apply to JSON
                    </button>
                  </div>

                  <div id="schema-properties-container">
                    <!-- Properties will be rendered here -->
                  </div>

                  <div class="add-property-section">
                    <div class="row align-items-end">
                      <div class="col-md-8">
                        <label class="form-label">Add New Property</label>
                        <input type="text" class="form-control" id="new-property-name"
                               placeholder="Property name (e.g., Date, Hours, Name)">
                      </div>
                      <div class="col-md-4">
                        <button type="button" class="btn btn-outline-primary w-100" onclick="addProperty()">
                          <i class="ri-add-line"></i> Add Property
                        </button>
                      </div>
                    </div>
                  </div>

                  <div class="schema-preview">
                    <h6>Schema Preview</h6>
                    <div id="schema-preview"></div>
                  </div>
                </div>
              </div>

              <!-- JSON Editor Tab -->
              <div class="tab-pane fade" id="json-editor" role="tabpanel" aria-labelledby="json-tab">
                <div class="mt-3">
                  {{ form.response_schema(class="form-control json-editor" + (" is-invalid" if form.response_schema.errors else ""), id="response_schema") }}
                  {% if form.response_schema.errors %}
                    <div class="error-message">
                      {% for error in form.response_schema.errors %}{{ error }}{% endfor %}
                    </div>
                  {% endif %}
                  <div id="json-preview" class="json-preview" style="display: none;"></div>
                </div>
              </div>
            </div>

            <div class="form-help">JSON schema defining the expected structure of the AI response</div>
          </div>

          <div class="btn-group-actions">
            <a href="{{ url_for('admin_templates') }}" class="btn btn-secondary">Cancel</a>
            <button type="submit" class="btn btn-primary">
              <i class="ri-save-line"></i> {{ action }} Template
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %}
