import xmlrpc.client
from datetime import datetime, timedelta
import pprint
import os
from dotenv import load_dotenv
from openpyxl import load_workbook
from fuzzywuzzy import process


class OdooAPI:
    def __init__(self):
        load_dotenv()
        self.url = os.getenv('ODOO_URL')
        self.db = os.getenv('ODOO_DB')

        self.username = os.getenv("ODOO_USER")
        self.password = os.getenv("ODOO_PASS")

        common = xmlrpc.client.ServerProxy('{}/xmlrpc/2/common'.format(self.url))
        self.uid = common.authenticate(self.db, self.username, self.password, {})


    def import_contractor_ids(self):
        wb = load_workbook(filename='C:\\Users\\<USER>\\Documents\\Atom Tech contractor numbers.xlsx')
        sheet = wb.active
        employees = self.get_all_employees()
        employee_names = [employee['name'] for employee in employees]
        error_logs = []

        for row in sheet.iter_rows(min_row=2, values_only=True):
            first_name = row[3]
            last_name = row[4]
            full_name = last_name + ', ' + first_name
            contractor_number = row[2]

            best_matches = find_best_match(full_name, employee_names)
            best_match_name, score = best_matches[0]
            best_match_employee = next(emp for emp in employees if emp['name'] == best_match_name)

            if score >= 90:
                selected_id = best_match_employee['id']
                employee_names.remove(best_match_name)
            else:
                print("Remainging Employees: " + str(len(employee_names)))
                print(f"Low confidence match for {full_name}. Top 5 matches:")
                for i, (name, score) in enumerate(best_matches, start=1):
                    print(f"{i}. {name} (score: {score})")
                
                choice = int(input("Select the correct match (1-5): "))
                if choice < 1 or choice > 5:
                    best_matches_error = []
                    for i, (name, score) in enumerate(best_matches, start=1):
                        best_matches_error.append(f"{name} (score: {score})")
                    error_logs.append(f"{full_name}. Best matches: " + str(best_matches_error))
                    continue
                selected_name = best_matches[choice - 1][0]

                employee_names.remove(selected_name)

                selected_employee = next(emp for emp in employees if emp['name'] == selected_name)
                selected_id = selected_employee['id']

            models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
            models.execute_kw(self.db, self.uid, self.password, 'hr.employee', 'write', [[selected_id], {'x_studio_customer_id': contractor_number}])
        print("-------------------------------")
        print("Errors: " + str(error_logs))


    def get_all_employees(self):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        employee_ids = models.execute_kw(self.db, self.uid, self.password, 'hr.employee', 'search', [[]])
        employees = models.execute_kw(self.db, self.uid, self.password, 'hr.employee', 'read', [employee_ids, ['id', 'name']])

        return employees
    
def find_best_match(full_name, employee_names):
    best_matches = process.extract(full_name, employee_names, limit=5)
    return best_matches

odoo_service = OdooAPI()
odoo_service.import_contractor_ids()
