from flask import Flask, request, jsonify, render_template, session
import os
from main import airtanker_app
from services.OdooService import OdooService
from services.DatabaseService import DatabaseService
from services.DateParser import check_holiday

from forms.WorkOrderForm import *
from auth.global_ldap_authentication import *
from threading import Thread
from enum import Enum
from dotenv import load_dotenv
from decimal import Decimal


class OT_Rules(Enum):
    Over40Wk = 1,
    Over40Wk_or_Saturday = 2

class DT_Rules(Enum):
    Over40Wk_and_Sunday = 1
    AnySunday = 2


def get_approved_hours(approved_hours, ot_rule, dt_rule):

    standard_hours = 0
    travel_hours = 0
    ot_hours = 0
    dt_hours = 0
    holiday_hours = 0

    for reported_hours in approved_hours:
        # check the task
        task_name = reported_hours['TaskName']
        date = reported_hours['Date']
        hours = reported_hours['Hours']            
            
        if task_name and "travel" in task_name.lower():
            # add travel time to bill
            travel_hours += hours
        else:
                    
            date_day = date.weekday()
            is_holiday = check_holiday(date)

            # TODO Check if it's a holiday or if the time is on a Friday or Monday then we need to check if there's a holiday on the weekend
            # because the Monday/Friday will become a holidays pay.
            if is_holiday:
                holiday_hours += hours

            elif date_day < 5: # Weekday

                # TODO check overtime rules

                # add the hours accordingly
                standard_hours += hours

                if standard_hours > 40 and (ot_rule == OT_Rules.Over40Wk or ot_rule == OT_Rules.Over40Wk_or_Saturday):
                    hours_over = standard_hours - 40 # get the hours over 40
                    standard_hours -= hours_over # subtract hours over from the 40+
                    ot_hours += hours_over # add the hours over 40 to the OT
                else:
                    # There might be another rule which is anything over 8 hours for the day - not just over 40 for the 
                    # TODO other rules
                    pass
            elif date_day == 5: # Saturday
                # check overtime rules
                standard_hours += hours
                if standard_hours > 40 and ot_rule == OT_Rules.Over40Wk:
                    hours_over = standard_hours - 40 # get the hours over 40
                    standard_hours -= hours_over # subtract hours over from the 40+
                    ot_hours += hours_over # add the hours over 40 to the OT
                elif ot_rule == OT_Rules.Over40Wk_or_Saturday:
                    ot_hours += hours
                    standard_hours -= hours # undo hour add pre-if statement
            elif date_day == 6: # Sunday
                # TODO check overtime rules
                standard_hours += hours # add the hours to standard to see if it'll get over 40 with the addition
                if standard_hours > 40 and dt_rule == DT_Rules.Over40Wk_and_Sunday:
                    hours_over = standard_hours - 40 # get the hours over 40
                    standard_hours -= hours_over # subtract hours over from the 40+
                    dt_hours += hours_over # add the hours over 40 to the DT                           
                elif dt_rule == DT_Rules.AnySunday:
                    standard_hours -= hours
                    dt_hours += hours
    return standard_hours, travel_hours, ot_hours, dt_hours, holiday_hours


@airtanker_app.route('/get_customer_invoices', methods=['POST', 'GET'])
def get_customer_invoices():
    form = WorkOrderForm()
    if request.method in ('POST', 'GET'):
        data = {}
        errors = []

        saleOrders_array = {}

        load_dotenv()

        selected_week_ending = '2024-06-16'
        session['selected_week_ending'] = selected_week_ending

        # Use the database service and the Odoo service to get the information.
        odoo_service = OdooService("test") # type: OdooService
        database_service = DatabaseService()
        database_service.connect()

        # get work order ID's and also the approved hours total so we can use it for fixed rate
        query_return_data = database_service.get_employee_details_weekly_work_ids(selected_week_ending,
                                                                                  isBill=False,
                                                                                  isInvoice=False,#os.getenv('PRODUCTION', 'True').lower() == 'true')
                                                                                  include_expenses=True)

        if not query_return_data:
            return jsonify({"Error": "error"})
        
        work_order_ids = [x["WorkOrderID"] for x in query_return_data]
        
        current_work_order_info = odoo_service.get_work_orders_for_talent_desk_by_ids(work_order_ids)
        # get the work_order information which contains rate and type
        bill_counter = 0
        for work_order in current_work_order_info:
            bill_counter += 1
            rate_type = work_order['x_sow_rate_type'] # type: str

            contractor_name = work_order['x_studio_assigned_to'][1]
            work_order_id = work_order['id']
            work_order_name = work_order["name"]

            time_sheet_entry = list(filter(lambda x: x['WorkOrderID'] == work_order_id, query_return_data))[0]                    
            approved_hours = database_service.get_employee_reported_hours(time_sheet_entry)

            # add this to dictionary at the end - need projectID:billID
            projectID = work_order['project_id'][0]
            project_name = work_order['project_id'][1]

            ot_rule = get_rule_from_value(work_order['x_inv_overtime_rule'], OT_Rules)
            dt_rule = get_rule_from_value(work_order['x_inv_double_time_rule'], DT_Rules)

            # Let's read the first task to get some important info.
            customerID = work_order['partner_id'][0]
            customer_name = work_order['partner_id'][1]

            accountID = work_order['project_analytic_account_id']
            saleOrderID = work_order['project_sale_order_id']
            if saleOrderID:
                saleOrderID = saleOrderID[0]
            else:
                errors.append({
                    "WorkOrderID": work_order_id,
                    "WorkOrderName": work_order_name,
                    "message": "Sale Order ID is missing for work order ID: {}, name: {}".format(work_order_id, work_order_name)
                })
                continue

            # Get Sale Order information used in the Invoice
            saleOrders = None
            if saleOrderID not in saleOrders_array:
                saleOrders = odoo_service.get_sale_order_by_id(saleOrderID)
                saleOrders_array[saleOrderID] = saleOrders
            else:
                saleOrders = saleOrders_array[saleOrderID]

            customerPO = saleOrders[0]['x_studio_message_on_document']

            st_rate = work_order['x_inv_rate']
            ot_rate = work_order['x_inv_ot_rate']
            dt_rate = work_order['x_inv_dt_rate']

            if approved_hours:
                standard_hours, travel_hours, ot_hours, dt_hours, holiday_hours = get_approved_hours(approved_hours=approved_hours,
                                                                                                        ot_rule=ot_rule,
                                                                                                        dt_rule=dt_rule)
                dt_hours += holiday_hours
                standard_hours += travel_hours

                if rate_type:
                    if 'fix' in rate_type.lower():
                        standard_hours = standard_hours + travel_hours + ot_hours + dt_hours + holiday_hours
                        travel_hours = 0
                        ot_hours = 0
                        dt_hours = 0
                        holiday_hours = 0
                else:
                    if st_rate == ot_rate == dt_rate:
                        rate_type = "fixed rate"
                        standard_hours = standard_hours + travel_hours + ot_hours + dt_hours + holiday_hours
                        travel_hours = 0
                        ot_hours = 0
                        dt_hours = 0
                        holiday_hours = 0
                    else:
                        rate_type = "Base Rate w/ Multipliers"

            contractor_id = work_order[os.getenv("EMP_INFO_FIELD")][0]

            employee = odoo_service.get_employee(contractor_id)
            contractor_number = ""
            if employee[0].get('x_studio_customer_id'):
                contractor_number = " - " + employee[0].get('x_studio_customer_id')


            st_total = standard_hours * Decimal(st_rate)
            ot_total = ot_hours * Decimal(ot_rate)
            dt_total = dt_hours * Decimal(dt_rate)

            data_object = {
                "TimesheetID": time_sheet_entry['TimeSheetID'],
                "WeekEnding": selected_week_ending.replace("-",""),

                "ContractorName": contractor_name,
                "ContractorNumber": contractor_number,
                "ContractorID": contractor_id,

                "ProjectID": projectID,
                "ProjectName": project_name,

                "WorkOrderName": work_order_name,
                
                "CustomerID": customerID,
                "CustomerName": customer_name,
                "CustomerPO": customerPO,

                "AccountID": accountID,
                "AccountName": accountID[1],

                "RateType": rate_type,
                
                "ApprovedData": {
                    "Standard": { "Hours": format_number(standard_hours), "Rate": format_decimal(st_rate), "Total": format_number(st_total) },
                    "OT": { "Hours": format_number(ot_hours), "Rate": format_decimal(ot_rate), "Total": format_number(ot_total) },
                    "DT": { "Hours": format_number(dt_hours), "Rate": format_decimal(dt_rate), "Total": format_number(dt_total) }
                }
            }

            if projectID not in data:
                data[projectID] = []

            # Add the data to the array
            data[projectID].append(data_object)

        database_service.disconnect()
        if bill_counter > 0:
            return jsonify(data)
            return jsonify({
                "status": "success",
                "data": data,
                "errors": errors
            })


def format_number(num):
    # Ensure the number is a float and format it with commas and two decimal places
    formatted_number = "{:,.2f}".format(float(num))
    return formatted_number

        
def format_decimal(value, decimal_places=3):
        return Decimal(value).quantize(Decimal('1.' + '0' * decimal_places))


def get_rule_from_value(value, enum):
    if value:
        try:
            # Try to return the corresponding enum member
            return enum(value)
        except ValueError:
            # If value is not a valid enum, return the default enum (first one)
            return list(enum)[0]
    else:
        # If value is falsey (e.g., None, False), return the default enum
        return list(enum)[0]
    


@airtanker_app.route('/export_invoices')
def index():
    return render_template('endpoints/export_invoice.html')

if __name__ == '__main__':
    airtanker_app.run(host='0.0.0.0', port=5000, debug=True)
