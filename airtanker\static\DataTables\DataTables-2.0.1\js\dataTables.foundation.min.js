/*! DataTables Foundation integration
 * ©2011-2015 SpryMedia Ltd - datatables.net/license
 */
!function(t){var a,i;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(e){return t(e,window,document)}):"object"==typeof exports?(a=require("jquery"),i=function(e,n){n.fn.dataTable||require("datatables.net")(e,n)},"undefined"==typeof window?module.exports=function(e,n){return e=e||window,n=n||a(e),i(e,n),t(n,0,e.document)}:(i(window,a),module.exports=t(a,window,window.document))):t(jQuery,window,document)}(function(r,e,n){"use strict";var t=r.fn.dataTable;return r.extend(!0,t.ext.classes,{container:"dt-container dt-foundation",processing:{container:"dt-processing panel callout"}}),r.extend(!0,t.defaults,{renderer:"foundation"}),t.ext.renderer.pagingButton.foundation=function(e,n,t,a,i){var o,d=[];return"ellipsis"===n?{display:o=r("<li>",{class:"ellipsis"}),clicker:o}:a||i?{display:o=r("<li>",{class:a?"current":"disabled "+d.join(" ")}).html(t),clicker:o}:{display:o=r("<li>").addClass(d.join(" ")),clicker:r("<a>",{href:"#"}).html(t).appendTo(o)}},t.ext.renderer.pagingContainer.foundation=function(e,n){return r("<ul/>").addClass("pagination").append(n)},t.ext.renderer.layout.foundation=function(e,n,t){var i=r("<div/>",{class:"grid-x"}).appendTo(n);r.each(t,function(e,n){var t="",a={};n.table?t+="cell small-12":"start"===e?t+="cell auto":"end"===e?(t+="cell shrink",a.marginLeft="auto"):"full"===e&&(t+="cell",a.marginLeft="auto",a.marginRight="auto"),r("<div/>",{id:n.id||null,class:t+" "+(n.className||"")}).css(a).append(n.contents).appendTo(i)})},t});