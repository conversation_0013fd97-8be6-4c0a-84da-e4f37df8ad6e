{% extends 'base.html' %}

{% block content %}
<head>
    <!-- Required meta tags -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Bootstrap CSS -->
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" rel="stylesheet">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.7.1/css/buttons.dataTables.min.css">

    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/export_summary.css') }}"  />
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/styles_odoo.css') }}"  />

    <style>
        .progress-container {
            width: 100%;
            background-color: #ddd;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .progress-bar {
            height: 30px;
            background-color: #4CAF50; /* Green background */
            background-image: linear-gradient(to right, #4caf50, #81c784); /* Gradient effect */
            border-radius: 8px;
            width: 0%; /* Initial width */
            position: relative; /* Relative position for inner text placement */
            overflow: hidden;
            display: flex;
            align-items: center; /* Center the label vertically */
        }

        .progress-bar span {
            color: white;
            margin-left: 10px; /* Give some space for the text from the start */
            font-weight: bold;
            z-index: 1; /* Make sure the text is above the background */
        }
        .progress-bar {
            transition: width 0.4s ease;
        }
        div#loading {
            width: 500px;
            height: 500px;
            display: none;
            background: url(/static/assets/loadingOdoo.gif) no-repeat center center;
            background-size: contain;
            cursor: wait;
            z-index: 1000;
            position: relative;
            top: 25%;
            left: 50%;
            transform: translate(-50%, -50%);
            
            box-shadow: none; /* Ensure no shadow is applied */
            filter: none; /* Remove any filters that might create a shadow effect */
        }
        .table-container {
            position: relative;
            height: 500px; /* Adjust height as needed */
        }
        .table-wrapper {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: none;
            z-index: 0;
        }
        .table-wrapper.active {
            display: block;
            z-index: 1;
        }
        .pagination-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
            margin-top: 20px;
        }
        .pagination-controls button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            cursor: pointer;
            border-radius: 5px;
        }
        .pagination-controls button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .pagination-controls span {
            font-size: 16px;
            margin: 0 10px;
        }
    </style>
</head>


<body class="o_web_client">

    <h2 style="display: none; font-size: 21px;" id="waiting_message">Thank you for being patient. This process is expected to take a few minutes.</h2>

    <h2 style="display: none;" id="invoice_header">Sending Invoices to Odoo</h2>
    <div id="progress-container" style="display: none; width: 90%; margin-top: 50px;"><!-- Container to control visibility -->
        <div id="progress-bar" class="progress-bar">
            <span id="progress-label">0%</span>
        </div>
    </div>

    <br>
    <br>
    <br>

    <h2 style="display: none;" id="pdf_header">PDF Generation</h2>
    <div id="progress-container-pdf" style="display: none; width: 90%; margin-top: 50px;"><!-- Container to control visibility -->
        <div id="progress-bar-pdf" class="progress-bar">
            <span id="progress-label">0%</span>
        </div>
    </div>


    <div id="paging_tabs" style="display: none;">
        <div class="pagination-controls">
            <button id="prev-page">Previous</button>
            <span id="page-info">1 / 1</span>
            <button id="next-page">Next</button>
        </div>
        <div class="pagination-controls">
            <button id="export_button">Looks good!</button>
        </div>
    </div>


    <div id="loading"></div>
    <div id="tables-container" class="table-container"></div>
    <style>
    .o_main_navbar {background-color: #e48108; border-bottom: 0px;}
    .o_main_navbar .show .dropdown-toggle {background-color: #e48108;}
    .o_main_navbar > ul > li > a:hover, .o_main_navbar 
    > ul > li > label:hover {background-color: #f79e30;}
    .o_main_navbar > a:hover, .o_main_navbar > a:focus, .o_main_navbar 
    > button:hover, .o_main_navbar 
    > button:focus {background-color: #f79e30; color: inherit;}
    </style>
    <script>
        
        let currentPage = 1;
        const tablesPerPage = 1;
        let totalPages = 0;
        let response_data = null;

        document.addEventListener('DOMContentLoaded', () => {
            console.log("getting data...");
            $("#loading").show();
            fetch('/get_customer_invoices')
            .then(response => response.json())
            .then(data => {
                if (data.status === "error") {
                    alert("Error: No timesheets available for selected week ending");
                    window.location.href = "/exports";
                }
                $("#loading").hide()
                showTablePopupInvoices(data.data, data.errors)
                document.getElementById("paging_tabs").style.display = 'block';
            })
            .catch(error => console.error('Error fetching data:', error));
            
        });

        let pdf_progress_done = false;
        let invoice_progress_done = false;

        function startProgress(url) {
            const evtSource = new EventSource(url);
            const progressBar = document.getElementById("progress-bar");
            const progress_container = document.getElementById("progress-container");
            progress_container.style.display = "block";

            evtSource.onmessage = function(event) {
                const data = event.data;
                try {
                    if (data === "Completed" || data === "Failed") {

                        if (data === "Failed"){
                            alert("Error occurred during export process");
                        }
                        animateProgress(data === "Completed" ? 100 : 0); // Animate to 100% or reset to 0%
                        progressBar.textContent = data;
                        evtSource.close();
                        invoice_progress_done = true;

                        if (pdf_progress_done == true){
                            setTimeout(() => {
                                window.location.href = "/exports";
                            }, 1000);  // Wait 1 second before refreshing
                        }
                    } else {
                        const newProgress = Math.round(parseFloat(data));  // Convert to float and round
                        if (isNaN(newProgress)) {
                            throw new Error(`Invalid progress value: ${data}`);
                        }
                        animateProgress(newProgress);  // Animate progress update
                    }
                } catch (error) {
                    console.error("Error processing SSE data:", error);
                    progressBar.textContent = "Error!";
                    evtSource.close();  // Close the event source on errors
                }
            };

            evtSource.onerror = function(event) {
                console.error("SSE failed:", event);
                progressBar.textContent = "Error!";
                evtSource.close();  // Close the event source on errors
            };

            let queuedProgress = null;

            function animateProgress(targetPercentage) {
                if (queuedProgress !== null) {
                    clearInterval(queuedProgress.animation);
                    queuedProgress = null;
                }

                const currentPercentage = parseFloat(progressBar.style.width) || 0;
                if (currentPercentage === targetPercentage) return;  // No change needed

                const diff = Math.abs(targetPercentage - currentPercentage);
                const increment = diff > 10 ? 0.5 : 0.1;

                const animation = setInterval(() => {
                    let currentWidth = parseFloat(progressBar.style.width) || 0;
                    if ((targetPercentage > currentWidth && currentWidth + increment >= targetPercentage) ||
                        (targetPercentage < currentWidth && currentWidth - increment <= targetPercentage)) {
                        progressBar.style.width = targetPercentage + '%';
                        progressBar.textContent = targetPercentage + '%';
                        clearInterval(animation);
                        queuedProgress = null;
                    } else {
                        currentWidth += (targetPercentage > currentWidth ? increment : -increment);
                        progressBar.style.width = currentWidth + '%';
                        progressBar.textContent = Math.round(currentWidth) + '%';
                    }
                }, 10);

                queuedProgress = { target: targetPercentage, animation: animation };
            }
        }


        function startProgress_pdf(url, pdf_task_id) {
            const evtSource = new EventSource(url);
            const progressBar = document.getElementById("progress-bar-pdf");
            const progress_container = document.getElementById("progress-container-pdf");
            progress_container.style.display = "block";
            console.log("PDF Task ID: ", pdf_task_id);

            evtSource.onmessage = function(event) {
                const data = event.data;
                try {
                    if (data === "Completed" || data === "Failed") {
                        if (data === "Failed") {
                            alert("Error occurred during export process");
                        }
                        animateProgress(data === "Completed" ? 100 : 0); // Animate to 100% or reset to 0%
                        progressBar.textContent = data;
                        pdf_progress_done = true;
                        
                        if (data === "Completed") {
                            console.log("Fetching PDF paths... Completed progress status is verified.");
                            console.log(`/download_pdfs/${pdf_task_id}`);
                            
                            // Fetch the PDF paths and initiate download
                            get_pdfs(pdf_task_id).then(() => {
                                console.log("All PDFs downloaded.");

                                // Navigate to the exports page after a short delay
                                if (invoice_progress_done === true) {
                                    setTimeout(() => {
                                        window.location.href = "/exports";
                                    }, 1000);  // 5 seconds delay
                                }
                            });
                        }

                        evtSource.close();

                    } else {
                        const newProgress = Math.round(parseFloat(data));  // Convert to float and round
                        if (isNaN(newProgress)) {
                            throw new Error(`Invalid progress value: ${data}`);
                        }
                        animateProgress(newProgress);  // Animate progress update
                    }
                } catch (error) {
                    console.error("Error processing SSE data:", error);
                    progressBar.textContent = "Error!";
                    evtSource.close();  // Close the event source on errors
                }
            };

            evtSource.onerror = function(event) {
                console.error("SSE failed:", event);
                progressBar.textContent = "Error!";
                evtSource.close();  // Close the event source on errors
            };

            let queuedProgress = null;

            function animateProgress(targetPercentage) {
                if (queuedProgress !== null) {
                    clearInterval(queuedProgress.animation);
                    queuedProgress = null;
                }

                const currentPercentage = parseFloat(progressBar.style.width) || 0;
                if (currentPercentage === targetPercentage) return;  // No change needed

                const diff = Math.abs(targetPercentage - currentPercentage);
                const increment = diff > 10 ? 0.5 : 0.1;

                const animation = setInterval(() => {
                    let currentWidth = parseFloat(progressBar.style.width) || 0;
                    if ((targetPercentage > currentWidth && currentWidth + increment >= targetPercentage) ||
                        (targetPercentage < currentWidth && currentWidth - increment <= targetPercentage)) {
                        progressBar.style.width = targetPercentage + '%';
                        progressBar.textContent = targetPercentage + '%';
                        clearInterval(animation);
                        queuedProgress = null;
                    } else {
                        currentWidth += (targetPercentage > currentWidth ? increment : -increment);
                        progressBar.style.width = currentWidth + '%';
                        progressBar.textContent = Math.round(currentWidth) + '%';
                    }
                }, 10);

                queuedProgress = { target: targetPercentage, animation: animation };
            }
        }


        function get_pdfs(pdf_task_id) {
            console.log("get_pdfs function called with task_id:", pdf_task_id);
            
            return fetch(`/download_pdfs/${pdf_task_id}`)
                .then(response => {
                    console.log("Response status:", response.status); // Log response status
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                }) // Parse the JSON response from the server
                .then(pdfData => {
                    console.log("PDF paths:", pdfData);

                    // Function to download a single file
                    const downloadFile = (url) => {
                        return new Promise((resolve) => {
                            // Create a temporary anchor element
                            const a = document.createElement('a');
                            a.style.display = 'none'; // Hide the anchor element
                            a.href = url; // Set the href attribute to the PDF URL
                            a.download = url.split('/').pop(); // Set the download attribute to the filename

                            // Append the anchor element to the document body
                            document.body.appendChild(a);

                            // Programmatically click the anchor element to trigger the download
                            a.click();

                            // Remove the anchor element from the document body
                            document.body.removeChild(a);

                            // Resolve the promise after a short delay
                            setTimeout(resolve, 500); // Slight delay to ensure the file download starts properly
                        });
                    };

                    // Sequentially download each PDF file
                    const downloadSequentially = async () => {
                        for (const pdf of pdfData) {
                            const url = pdf.url;
                            console.log(`Downloading ${url}`);
                            await downloadFile(url); // Wait for the download to complete
                        }
                    };

                    // Start the download process
                    return downloadSequentially();
                })
                .catch(err => {
                    console.error('Error fetching PDF paths:', err); // Log any errors that occur during the fetch process
                });
        }

        
    
        function export_to_odoo(event, data) {

            document.getElementById('waiting_message').style.display = 'block';
            document.getElementById('pdf_header').style.display = 'block';
            document.getElementById('invoice_header').style.display = 'block';

            document.getElementById('tables-container').style.display = 'none';
            document.getElementById("paging_tabs").style.display = 'none';

            let actionUrl = '/post_customer_invoices';
            
            fetch(actionUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(response_data),
                credentials: 'same-origin'
            })        
            .then(response => {
                if (!response.ok) {
                    // If the server response is not ok (e.g., 500 Internal Server Error), throw an error
                    throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                // Check for invoice task
                if (data.task_id){
                    startProgress(`/progress/${data.task_id}`);
                }
                else{
                    console.log("No invoice task_id");
                }


                // Check for PDF task
                if (data.pdf_task_id){
                    startProgress_pdf(`/progress/${data.pdf_task_id}`, data.pdf_task_id);
                }
                else{
                    console.log("No pdf_task_id");
                }
            })
            .catch(error => {
                // Handle any errors that occurred during the fetch or due to a server error
                console.error('Error:', error);
            })
        };    


        document.getElementById('export_button').addEventListener('click', function(event) {
            export_to_odoo(event, response_data);
        });


        function showTablePopupInvoices(data, errors = null) {
            response_data = data;
            if (errors && errors.length > 0) {
                let errorHtml = `
                <div style="max-height: 300px; z-index: 1500; max-width: 100%; > <!-- Container with scrollbars -->
                    <ul class="list-group" style="white-space: nowrap;">`; // Ensures the text stays on one line
                
                errors.forEach((error) => {
                    errorHtml += `<li class="list-group-item list-group-item-danger">${error.message}</li>`;
                });

                errorHtml += `
                    </ul>
                </div>`; // Close the container div

                Swal.fire({
                    title: 'Errors Detected',
                    html: errorHtml,
                    icon: 'error',
                    width: '800px', // Adjust the width as needed
                    confirmButtonText: 'Acknowledge',
                    allowOutsideClick: false,
                    footer: '<p>Skipping these work orders for export.</p>',
                    preConfirm: () => {
                        // Additional pre-confirm logic
                    },
                    didOpen: () => {
                        Swal.getContainer().style.zIndex = 1500; // Ensure the z-index is set properly
                        document.body.classList.remove('swal2-height-auto'); // Remove the unwanted class
                    }
                });
            } 
            
            createTables(data);
            updatePagination(); // Ensure pagination is updated after tables are created
        };


        function createTables(data) {
            const tablesContainer = document.getElementById('tables-container');
            totalPages = Math.ceil(Object.keys(data).length / tablesPerPage);
            
            for (const projectID in data) {
                if (data.hasOwnProperty(projectID)) {
                    createTable(tablesContainer, projectID, data[projectID]);                    
                }
            }

        }


        function createTable(container, projectID, entries) {
            const tableWrapper = document.createElement('div');
            tableWrapper.classList.add('table-wrapper', 'o_form_sheet_bg');
            tableWrapper.id = `table-${projectID}`;

            let plant_expenses = {};

            const accountName = entries[0].AccountName;
            let expense_header = `[Expenses] All - Project # ${projectID}\nPer Diem, Fuel, Hotel, Misc. items.\n`;
            let expense_string = "\n";
            let total_expenses = 0;

            let tableHTML = `
                
            <!-- Add the account name and checkboxes here -->
                <!-- <div class="table-header">
                    <h2>${accountName}</h2>
                    <br>
                    <label>
                        <input type="checkbox" id="breakdown-expenses-${projectID}"> Breakdown Expenses
                    </label>
                    <br>
                    <label>
                        <input type="checkbox" id="separate-expenses-${projectID}"> Separate Expenses from Hours
                    </label>
                </div> -->

                <!-- Existing table structure --> 
                <div class="o_action o_view_controller o_form_view o_xxl_form_view h-100">
                    <div class="o_form_view_container">
                        <div class="o_content">
                            <div class="o_form_editable d-flex o_form_saved flex-nowrap h-100">
                            <div class="o_form_sheet_bg">
                                <div class="o_form_sheet position-relative clearfix">
                                    <div class="o-form-buttonbox oe_button_box position-relative text-end o_not_full"></div>

                                    <!-- TITLE -->

                                    <div class="oe_title">
                                        <span class="o_form_label">
                                        <div name="move_type" class="o_field_widget o_readonly_modifier o_required_modifier o_field_selection"><span raw-value="out_invoice">Customer Invoice</span></div>
                                        </span>
                                        <h1>
                                        <div name="name" class="o_field_widget o_readonly_modifier o_field_char"><span>Draft</span></div>
                                        </h1>
                                    </div>

                                    <!-- BILL INFORMATION -->

                                    <div class="o_group row align-items-start">
                                        <div class="o_inner_group grid col-lg-6">
                                        <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                            <div class="o_cell flex-grow-1 flex-sm-grow-0 o_wrap_label w-100 text-break text-900" style=""><label class="o_form_label o_form_label_readonly" for="partner_id">Customer</label></div>
                                            <div class="o_cell flex-grow-1 flex-sm-grow-0" style="width: 100%;">
                                                <div name="partner_id" class="o_field_widget o_readonly_modifier o_field_res_partner_many2one"><a class="o_form_uri"><span>${entries[0].CustomerName}</span><br><span></span><br><span></span><br><span></span></a></div>
                                            </div>
                                        </div>
                                        <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                            <div class="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900"><label class="o_form_label o_form_label_readonly" for="partner_shipping_id">Delivery Address<sup class="text-info p-1" data-tooltip-template="web.FieldTooltip" data-tooltip-info="{&quot;field&quot;:{&quot;help&quot;:&quot;Delivery address for current invoice.&quot;}}" data-tooltip-touch-tap-to-show="true">?</sup></label></div>
                                            <div class="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style="width: 100%;">
                                                <div name="partner_shipping_id" class="o_field_widget o_readonly_modifier o_field_many2one"><a class="o_form_uri"><span>${entries[0].CustomerName}</span></a></div>
                                            </div>
                                        </div>
                                        <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                            <div class="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900"><label class="o_form_label" for="x_studio_service_date">Service Date</label></div>
                                            <div class="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style="width: 100%;">
                                                <div name="x_studio_service_date" class="o_field_widget o_field_date"><span>${formatDate(entries[0].WeekEnding)}</span>
                                                </div>
                                            </div>
                                        </div>
                                        </div>
                                        <div class="o_inner_group grid col-lg-6">
                                        <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                            <div class="o_cell flex-grow-1 flex-sm-grow-0 o_wrap_label w-100 text-break text-900" style=""><label class="o_form_label o_form_label_readonly" for="invoice_date">Invoice Date</label></div>
                                            <div class="o_cell flex-grow-1 flex-sm-grow-0" style="width: 100%;">
                                                <div name="invoice_date" class="o_field_widget o_readonly_modifier o_field_date"><span>${getFormattedDate()}</span></div>
                                            </div>
                                        </div>
                                        <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                            <div class="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900"><label class="o_form_label" for="payment_reference">Payment Reference</label></div>
                                            <div class="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style="width: 100%;">
                                            </div>
                                        </div>
                                        <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                            <div class="o_cell flex-grow-1 flex-sm-grow-0 o_wrap_label w-100 text-break text-900" style="">
                                                <div class="o_td_label"><label class="o_form_label o_form_label_readonly" for="invoice_payment_term_id">Payment terms</label></div>
                                            </div>
                                            <div class="o_cell flex-grow-1 flex-sm-grow-0" style="width: 100%;">
                                                <div class="d-flex">
                                                    <div name="invoice_payment_term_id" class="o_field_widget o_readonly_modifier o_field_many2one"><a class="o_form_uri"><span>30 Days</span></a></div>
                                                </div>
                                            </div>
                                        </div>


                                        <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0"><div class="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900"><label class="o_form_label" for="x_studio_message_on_invoice_${projectID}">Message on Invoice</label></div><div class="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style="width: 100%;"><div name="x_studio_message_on_invoice_${projectID}" class="o_field_widget o_field_text"><div style="height: 62px;"><textarea class="o_input" id="x_studio_message_on_invoice_${projectID}" rows="2" style="height: 62px; border-top-width: 0px; border-bottom-width: 1px; padding: 1px 0px;" readonly></textarea></div></div></div></div>

                                        <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                            <div class="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900"><label class="o_form_label o_form_label_readonly" for="currency_id_1">Currency</label></div>
                                            <div class="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style="width: 100%;">
                                                <div name="currency_id" class="o_field_widget o_readonly_modifier o_required_modifier o_field_many2one"><a class="o_form_uri"><span>USD</span></a></div>
                                            </div>
                                        </div>
                                        </div>
                                    </div>
                                    <div class="o_notebook d-flex w-100 horizontal flex-column">
                                        <div class="o_notebook_headers">
                                        <ul class="nav nav-tabs flex-row flex-nowrap">
                                            <li class="nav-item flex-nowrap cursor-pointer"><a class="nav-link active undefined" role="tab" tabindex="0" name="invoice_tab">Invoice Lines</a></li>
                                        </ul>
                                        </div>
                                        <div class="o_notebook_content tab-content">
                                        <div class="tab-pane active">
                                            <div name="invoice_line_ids" class="o_field_widget o_readonly_modifier o_field_section_and_note_one2many o_field_one2many">
                                                <div class="o_field_x2many o_field_x2many_list">
                                                    <div class="o_x2m_control_panel">
                                                    <div class="o_cp_pager" role="search">
                                                    </div>
                                                    </div>
                                                    <div class="o_list_renderer o_renderer table-responsive" tabindex="-1">
                                                    <table class="o_section_and_note_list_view o_list_table table table-sm table-hover position-relative mb-0 o_list_table_ungrouped table-striped" style="table-layout: fixed;">
                                                        <thead>
                                                            <tr>
                                                                <th data-tooltip-delay="1000" tabindex="-1" data-name="sequence" class="align-middle o_column_sortable position-relative cursor-pointer o_list_number_th o_handle_cell opacity-trigger-hover" style="min-width: 33px; width: 33px;"></th>
                                                                <th data-tooltip-delay="1000" tabindex="-1" data-name="product_id" class="align-middle o_column_sortable position-relative cursor-pointer o_many2one_barcode_cell opacity-trigger-hover" style="width: 234px;">
                                                                <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">Product</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                                </th>
                                                                <th data-tooltip-delay="1000" tabindex="-1" data-name="name" class="align-middle o_column_sortable position-relative cursor-pointer o_section_and_note_text_cell opacity-trigger-hover" style="width: 129px;">
                                                                <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">Label</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                                </th>
                                                                <th data-tooltip-delay="1000" tabindex="-1" data-name="analytic_distribution" class="align-middle o_column_sortable position-relative cursor-pointer o_analytic_distribution_cell opacity-trigger-hover" style="width: 213px;">
                                                                <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">Analytic</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                                </th>
                                                                <th data-tooltip-delay="1000" tabindex="-1" data-name="quantity" class="align-middle o_column_sortable position-relative cursor-pointer o_list_number_th opacity-trigger-hover" style="min-width: 92px; width: 92px;">
                                                                <div class="d-flex flex-row-reverse"><span class="d-block min-w-0 text-truncate flex-grow-1 o_list_number_th">Quantity</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                                </th>
                                                                <th data-tooltip-delay="1000" tabindex="-1" data-name="product_uom_id" class="align-middle o_column_sortable position-relative cursor-pointer opacity-trigger-hover" style="width: 60px;">
                                                                <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">UoM</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                                </th>
                                                                <th data-tooltip-delay="1000" tabindex="-1" data-name="price_unit" class="align-middle o_column_sortable position-relative cursor-pointer o_list_number_th opacity-trigger-hover" style="min-width: 92px; width: 92px;">
                                                                <div class="d-flex flex-row-reverse"><span class="d-block min-w-0 text-truncate flex-grow-1 o_list_number_th">Price</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                                </th>
                                                                <th data-tooltip-delay="1000" tabindex="-1" data-name="tax_ids" class="align-middle cursor-default o_many2many_tags_cell opacity-trigger-hover" style="width: 39px;">
                                                                <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">Taxes</span><i class="d-none fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                                </th>
                                                                <th data-tooltip-delay="1000" tabindex="-1" data-name="price_subtotal" class="align-middle o_column_sortable position-relative cursor-pointer o_list_number_th opacity-trigger-hover" style="min-width: 104px; width: 104px;">
                                                                <div class="d-flex flex-row-reverse"><span class="d-block min-w-0 text-truncate flex-grow-1 o_list_number_th">Subtotal</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                                </th>
                                                                <th class="o_list_controller o_list_actions_header position-static" style="width: 32px; min-width: 32px">
                                                                <div class="o-dropdown dropdown o_optional_columns_dropdown border-top-0 text-center o-dropdown--no-caret"><button class="dropdown-toggle btn p-0" tabindex="-1" aria-expanded="false"><i class="o_optional_columns_dropdown_toggle oi oi-fw oi-settings-adjust"></i></button></div>
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody class="ui-sortable">`

            // add all the rows dynamically now. 
            entries.forEach(entry => {

                tableHTML += `
                    <tr class="o_data_row o_is_line_section" data-id="datapoint_304">
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_handle_cell" data-tooltip-delay="1000" tabindex="-1" name="sequence">
                        <div name="sequence" class="o_field_widget o_field_handle"><span class="o_row_handle fa fa-sort ui-sortable-handle"></span></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_char o_section_and_note_text_cell" data-tooltip-delay="1000" tabindex="-1" name="name" colspan="8" data-tooltip="${entry.ContractorName}${entry.ContractorNumber}">
                        <div name="name" class="o_field_widget o_field_section_and_note_text o_field_text"><span>${entry.ContractorName}${entry.ContractorNumber}</span></div>
                        </td>
                        <td tabindex="-1"></td>
                    </tr>


                    <tr class="o_data_row o_is_product" data-id="datapoint_306">
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_handle_cell" data-tooltip-delay="1000" tabindex="-1" name="sequence">
                        <div name="sequence" class="o_field_widget o_field_handle"><span class="o_row_handle fa fa-sort ui-sortable-handle"></span></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one o_many2one_barcode_cell" data-tooltip-delay="1000" tabindex="-1" name="product_id" data-tooltip="[Engineering Services] Contract Services">
                        <div name="product_id" class="o_field_widget o_field_many2one_barcode"><a class="o_form_uri"><span>[Engineering Services] Contract Services</span></a></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_char o_section_and_note_text_cell" data-tooltip-delay="1000" tabindex="-1" name="name" data-tooltip="${entry.ContractorName} - ST W.E. ${entry.WeekEnding}">
                        <div name="name" class="o_field_widget o_field_section_and_note_text o_field_text"><span>${entry.ContractorName} - ST W.E. ${entry.WeekEnding}</span>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_analytic_distribution_cell" data-tooltip-delay="1000" tabindex="-1" name="analytic_distribution">
                        <div name="analytic_distribution" class="o_field_widget o_field_analytic_distribution">
                            <div class="o_field_tags d-inline-flex flex-wrap mw-100">
                                <span tabindex="-1" class="o_tag_color_4 badge rounded-pill o_tag d-inline-flex align-items-center mw-100" data-color="4" title="${entry.WorkOrderName}">
                                    <div class="o_tag_badge_text">${entry.AccountName}</div>
                                </span>
                            </div>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="quantity">${entry.ApprovedData.Standard.Hours}</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one" data-tooltip-delay="1000" tabindex="-1" name="product_uom_id" data-tooltip="Hours">Hours</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="price_unit">${entry.ApprovedData.Standard.Rate}</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_many2many_tags_cell" data-tooltip-delay="1000" tabindex="-1" name="tax_ids" data-tooltip="No records">
                        <div name="tax_ids" class="o_field_widget o_field_many2many_tags">
                            <div class="o_field_tags d-inline-flex flex-wrap"></div>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_readonly_modifier" data-tooltip-delay="1000" tabindex="-1" name="price_subtotal">$&nbsp;${entry.ApprovedData.Standard.Total}</td>
                        <td tabindex="-1"></td>
                    </tr>`
                
                if (entry.RateType != false && !entry.RateType.toLowerCase().includes('fix')) {
                    
                    tableHTML += `
                        <tr class="o_data_row o_is_product" data-id="datapoint_308">
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_handle_cell" data-tooltip-delay="1000" tabindex="-1" name="sequence">
                            <div name="sequence" class="o_field_widget o_field_handle"><span class="o_row_handle fa fa-sort ui-sortable-handle"></span></div>
                            </td>
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one o_many2one_barcode_cell" data-tooltip-delay="1000" tabindex="-1" name="product_id" data-tooltip="[Engineering Services] Contract Services">
                            <div name="product_id" class="o_field_widget o_field_many2one_barcode"><a class="o_form_uri"><span>[Engineering Services] Contract Services</span></a></div>
                            </td>
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_char o_section_and_note_text_cell" data-tooltip-delay="1000" tabindex="-1" name="name" data-tooltip="${entry.ContractorName} - OT W.E. ${entry.WeekEnding}">
                            <div name="name" class="o_field_widget o_field_section_and_note_text o_field_text"><span>${entry.ContractorName} - OT W.E. ${entry.WeekEnding}</span>
                            </div>
                            </td>
                            <td class="o_data_cell cursor-pointer o_field_cell o_analytic_distribution_cell" data-tooltip-delay="1000" tabindex="-1" name="analytic_distribution">
                            <div name="analytic_distribution" class="o_field_widget o_field_analytic_distribution">
                                <div class="o_field_tags d-inline-flex flex-wrap mw-100">
                                    <span tabindex="-1" class="o_tag_color_4 badge rounded-pill o_tag d-inline-flex align-items-center mw-100" data-color="4" title="${entry.WorkOrderName}">
                                        <div class="o_tag_badge_text">${entry.AccountName}</div>
                                    </span>
                                </div>
                            </div>
                            </td>
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="quantity">${entry.ApprovedData.OT.Hours}</td>
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one" data-tooltip-delay="1000" tabindex="-1" name="product_uom_id" data-tooltip="Hours">Hours</td>
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="price_unit">${entry.ApprovedData.OT.Rate}</td>
                            <td class="o_data_cell cursor-pointer o_field_cell o_many2many_tags_cell" data-tooltip-delay="1000" tabindex="-1" name="tax_ids" data-tooltip="No records">
                            <div name="tax_ids" class="o_field_widget o_field_many2many_tags">
                                <div class="o_field_tags d-inline-flex flex-wrap"></div>
                            </div>
                            </td>
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_readonly_modifier" data-tooltip-delay="1000" tabindex="-1" name="price_subtotal">$&nbsp;${entry.ApprovedData.OT.Total}</td>
                            <td tabindex="-1"></td>
                        </tr>


                        <tr class="o_data_row o_is_product" data-id="datapoint_310">
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_handle_cell" data-tooltip-delay="1000" tabindex="-1" name="sequence">
                            <div name="sequence" class="o_field_widget o_field_handle"><span class="o_row_handle fa fa-sort ui-sortable-handle"></span></div>
                            </td>
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one o_many2one_barcode_cell" data-tooltip-delay="1000" tabindex="-1" name="product_id" data-tooltip="[Engineering Services] Contract Services">
                            <div name="product_id" class="o_field_widget o_field_many2one_barcode"><a class="o_form_uri"><span>[Engineering Services] Contract Services</span></a></div>
                            </td>
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_char o_section_and_note_text_cell" data-tooltip-delay="1000" tabindex="-1" name="name" data-tooltip="${entry.ContractorName} - DT W.E. ${entry.WeekEnding}">
                            <div name="name" class="o_field_widget o_field_section_and_note_text o_field_text"><span>${entry.ContractorName} - DT W.E. ${entry.WeekEnding}</span>
                            </div>
                            </td>
                            <td class="o_data_cell cursor-pointer o_field_cell o_analytic_distribution_cell" data-tooltip-delay="1000" tabindex="-1" name="analytic_distribution">
                            <div name="analytic_distribution" class="o_field_widget o_field_analytic_distribution">
                                <div class="o_field_tags d-inline-flex flex-wrap mw-100">
                                    <span tabindex="-1" class="o_tag_color_4 badge rounded-pill o_tag d-inline-flex align-items-center mw-100" data-color="4" title="${entry.WorkOrderName}">
                                        <div class="o_tag_badge_text">${entry.AccountName}</div>
                                    </span>
                                </div>
                            </div>
                            </td>
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="quantity">${entry.ApprovedData.DT.Hours}</td>
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one" data-tooltip-delay="1000" tabindex="-1" name="product_uom_id" data-tooltip="Hours">Hours</td>
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="price_unit">${entry.ApprovedData.DT.Rate}</td>
                            <td class="o_data_cell cursor-pointer o_field_cell o_many2many_tags_cell" data-tooltip-delay="1000" tabindex="-1" name="tax_ids" data-tooltip="No records">
                            <div name="tax_ids" class="o_field_widget o_field_many2many_tags">
                                <div class="o_field_tags d-inline-flex flex-wrap"></div>
                            </div>
                            </td>
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_readonly_modifier" data-tooltip-delay="1000" tabindex="-1" name="price_subtotal">$&nbsp;${entry.ApprovedData.DT.Total}</td>
                            <td tabindex="-1"></td>
                        </tr>`
                    }

                    if (entry.CustomerName.toUpperCase().includes("ATS") && entry.Expenses?.ApprovedTotalExpenses > 0) {
                        if (!plant_expenses[entry.PlantName]) {
                            // Initialize the plant entry if it does not exist
                            plant_expenses[entry.PlantName] = {
                                TotalExpenses: 0,
                                ExpenseString: '\n'
                            };
                        }
                        plant_expenses[entry.PlantName]["TotalExpenses"] += parseFloat(entry.Expenses.ApprovedTotalExpenses);
                        plant_expenses[entry.PlantName]["ExpenseString"]  += `${entry.ContractorName} - $${entry.Expenses.ApprovedTotalExpenses}\n`;
                    }
                    else if (entry.Expenses?.ApprovedTotalExpenses > 0) {
                        expense_string += `${entry.ContractorName} - $${entry.Expenses.ApprovedTotalExpenses}\n`;
                        total_expenses += parseFloat(entry.Expenses.ApprovedTotalExpenses);
                    }
                });

            if (expense_string.length > 1) {

                tableHTML += `
                    <tr class="o_data_row o_is_line_section" data-id="datapoint_304">
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_handle_cell" data-tooltip-delay="1000" tabindex="-1" name="sequence">
                        <div name="sequence" class="o_field_widget o_field_handle"><span class="o_row_handle fa fa-sort ui-sortable-handle"></span></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_char o_section_and_note_text_cell" data-tooltip-delay="1000" tabindex="-1" name="name" colspan="8" data-tooltip="EE ${entries[0].ProjectName}">
                        <div name="name" class="o_field_widget o_field_section_and_note_text o_field_text"><span>EE ${entries[0].ProjectName}</span></div>
                        </td>
                        <td tabindex="-1"></td>
                    </tr>
                    <tr class="o_data_row o_is_product" data-id="datapoint_308">
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_handle_cell" data-tooltip-delay="1000" tabindex="-1" name="sequence">
                        <div name="sequence" class="o_field_widget o_field_handle"><span class="o_row_handle fa fa-sort ui-sortable-handle"></span></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one o_many2one_barcode_cell" data-tooltip-delay="1000" tabindex="-1" name="product_id" data-tooltip="[Expenses] All">
                        <div name="product_id" class="o_field_widget o_field_many2one_barcode"><a class="o_form_uri"><span>[Expenses] All</span></a></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_char o_section_and_note_text_cell" data-tooltip-delay="1000" tabindex="-1" name="name" data-tooltip="${expense_header}${expense_string}">
                        <div name="name" class="o_field_widget o_field_section_and_note_text o_field_text"><span>${expense_header}${expense_string}</span>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_analytic_distribution_cell" data-tooltip-delay="1000" tabindex="-1" name="analytic_distribution">
                        <div name="analytic_distribution" class="o_field_widget o_field_analytic_distribution">
                            <div class="o_field_tags d-inline-flex flex-wrap mw-100">
                                <span tabindex="-1">
                                    <div></div>
                                </span>
                            </div>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="quantity">1.00</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one" data-tooltip-delay="1000" tabindex="-1" name="product_uom_id" data-tooltip="Unit">Unit</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="price_unit">${total_expenses.toLocaleString('en-US', { minimumFractionDigits: 3, maximumFractionDigits: 3 })}</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_many2many_tags_cell" data-tooltip-delay="1000" tabindex="-1" name="tax_ids" data-tooltip="No records">
                        <div name="tax_ids" class="o_field_widget o_field_many2many_tags">
                            <div class="o_field_tags d-inline-flex flex-wrap"></div>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_readonly_modifier" data-tooltip-delay="1000" tabindex="-1" name="price_subtotal">$&nbsp;${total_expenses.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
                        <td tabindex="-1"></td>
                    </tr>
                `;
            }

            tableHTML += `
                                                                        
                                                        </tbody>
                                                        <tfoot class="o_list_footer cursor-default">
                                                            <tr>
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                                <td></td>
                                                            </tr>
                                                        </tfoot>
                                                    </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            `;
            
            tableWrapper.innerHTML = tableHTML;

            container.appendChild(tableWrapper);

            const textarea = document.getElementById(`x_studio_message_on_invoice_${projectID}`);
            const firstEntry = entries[0];  // Assuming all entries have the same PO and date range
            const poSection = createCustomerPOSection(firstEntry);
            textarea.value = poSection;

            /// Create new invoice for each plant in 
            if (Object.keys(plant_expenses).length > 0) {
                Object.keys(plant_expenses).forEach(plantName => {
                    totalPages += 1;

                    let tableWrapper_expense = document.createElement('div');
                    tableWrapper_expense.classList.add('table-wrapper', 'o_form_sheet_bg');
                    tableWrapper_expense.id = `table-${plantName}`;

                    let expenses = plant_expenses[plantName];
                    // Run your function for each key
                    let expense_tableHTML = `
                        
                    <!-- Add the account name and checkboxes here -->
                        <!-- <div class="table-header">
                            <h2>${accountName}</h2>
                            <br>
                            <label>
                                <input type="checkbox" id="breakdown-expenses-${projectID}"> Breakdown Expenses
                            </label>
                            <br>
                            <label>
                                <input type="checkbox" id="separate-expenses-${projectID}"> Separate Expenses from Hours
                            </label>
                        </div> -->

                        <!-- Existing table structure --> 
                        <div class="o_action o_view_controller o_form_view o_xxl_form_view h-100">
                            <div class="o_form_view_container">
                                <div class="o_content">
                                    <div class="o_form_editable d-flex o_form_saved flex-nowrap h-100">
                                    <div class="o_form_sheet_bg">
                                        <div class="o_form_sheet position-relative clearfix">
                                            <div class="o-form-buttonbox oe_button_box position-relative text-end o_not_full"></div>
                                            <div class="oe_title">
                                                <span class="o_form_label">
                                                <div name="move_type" class="o_field_widget o_readonly_modifier o_required_modifier o_field_selection"><span raw-value="out_invoice">Customer Invoice</span></div>
                                                </span>
                                                <h1>
                                                <div name="name" class="o_field_widget o_readonly_modifier o_field_char"><span>Draft</span></div>
                                                </h1>
                                            </div>
                                            <div class="o_group row align-items-start">
                                                <div class="o_inner_group grid col-lg-6">
                                                <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                                    <div class="o_cell flex-grow-1 flex-sm-grow-0 o_wrap_label w-100 text-break text-900" style=""><label class="o_form_label o_form_label_readonly" for="partner_id">Customer</label></div>
                                                    <div class="o_cell flex-grow-1 flex-sm-grow-0" style="width: 100%;">
                                                        <div name="partner_id" class="o_field_widget o_readonly_modifier o_field_res_partner_many2one"><a class="o_form_uri"><span>${entries[0].CustomerName}</span><br><span></span><br><span></span><br><span></span></a></div>
                                                    </div>
                                                </div>
                                                <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                                    <div class="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900"><label class="o_form_label o_form_label_readonly" for="partner_shipping_id">Delivery Address<sup class="text-info p-1" data-tooltip-template="web.FieldTooltip" data-tooltip-info="{&quot;field&quot;:{&quot;help&quot;:&quot;Delivery address for current invoice.&quot;}}" data-tooltip-touch-tap-to-show="true">?</sup></label></div>
                                                    <div class="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style="width: 100%;">
                                                        <div name="partner_shipping_id" class="o_field_widget o_readonly_modifier o_field_many2one"><a class="o_form_uri"><span>${entries[0].CustomerName}</span></a></div>
                                                    </div>
                                                </div>
                                                <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                                    <div class="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900"><label class="o_form_label" for="x_studio_service_date">Service Date</label></div>
                                                    <div class="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style="width: 100%;">
                                                        <div name="x_studio_service_date" class="o_field_widget o_field_date"><span>${formatDate(entries[0].WeekEnding)}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                </div>
                                                <div class="o_inner_group grid col-lg-6">
                                                <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                                    <div class="o_cell flex-grow-1 flex-sm-grow-0 o_wrap_label w-100 text-break text-900" style=""><label class="o_form_label o_form_label_readonly" for="invoice_date">Invoice Date</label></div>
                                                    <div class="o_cell flex-grow-1 flex-sm-grow-0" style="width: 100%;">
                                                        <div name="invoice_date" class="o_field_widget o_readonly_modifier o_field_date"><span>${getFormattedDate()}</span></div>
                                                    </div>
                                                </div>
                                                <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                                    <div class="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900"><label class="o_form_label" for="payment_reference">Payment Reference</label></div>
                                                    <div class="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style="width: 100%;">
                                                    </div>
                                                </div>
                                                <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                                    <div class="o_cell flex-grow-1 flex-sm-grow-0 o_wrap_label w-100 text-break text-900" style="">
                                                        <div class="o_td_label"><label class="o_form_label o_form_label_readonly" for="invoice_payment_term_id">Payment terms</label></div>
                                                    </div>
                                                    <div class="o_cell flex-grow-1 flex-sm-grow-0" style="width: 100%;">
                                                        <div class="d-flex">
                                                            <div name="invoice_payment_term_id" class="o_field_widget o_readonly_modifier o_field_many2one"><a class="o_form_uri"><span>30 Days</span></a></div>
                                                        </div>
                                                    </div>
                                                </div>


                                                <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0"><div class="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900"><label class="o_form_label" for="x_studio_message_on_invoice_${plantName}">Message on Invoice</label></div><div class="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style="width: 100%;"><div name="x_studio_message_on_invoice_${plantName}" class="o_field_widget o_field_text"><div style="height: 62px;"><textarea class="o_input" id="x_studio_message_on_invoice_${plantName}" rows="2" style="height: 62px; border-top-width: 0px; border-bottom-width: 1px; padding: 1px 0px;" readonly></textarea></div></div></div></div>

                                                <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                                    <div class="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900"><label class="o_form_label o_form_label_readonly" for="currency_id_1">Currency</label></div>
                                                    <div class="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style="width: 100%;">
                                                        <div name="currency_id" class="o_field_widget o_readonly_modifier o_required_modifier o_field_many2one"><a class="o_form_uri"><span>USD</span></a></div>
                                                    </div>
                                                </div>
                                                </div>
                                            </div>
                                            <div class="o_notebook d-flex w-100 horizontal flex-column">
                                                <div class="o_notebook_headers">
                                                <ul class="nav nav-tabs flex-row flex-nowrap">
                                                    <li class="nav-item flex-nowrap cursor-pointer"><a class="nav-link active undefined" role="tab" tabindex="0" name="invoice_tab">Invoice Lines</a></li>
                                                </ul>
                                                </div>
                                                <div class="o_notebook_content tab-content">
                                                <div class="tab-pane active">
                                                    <div name="invoice_line_ids" class="o_field_widget o_readonly_modifier o_field_section_and_note_one2many o_field_one2many">
                                                        <div class="o_field_x2many o_field_x2many_list">
                                                            <div class="o_x2m_control_panel">
                                                            <div class="o_cp_pager" role="search">
                                                            </div>
                                                            </div>
                                                            <div class="o_list_renderer o_renderer table-responsive" tabindex="-1">
                                                            <table class="o_section_and_note_list_view o_list_table table table-sm table-hover position-relative mb-0 o_list_table_ungrouped table-striped" style="table-layout: fixed;">
                                                                <thead>
                                                                    <tr>
                                                                        <th data-tooltip-delay="1000" tabindex="-1" data-name="sequence" class="align-middle o_column_sortable position-relative cursor-pointer o_list_number_th o_handle_cell opacity-trigger-hover" style="min-width: 33px; width: 33px;"></th>
                                                                        <th data-tooltip-delay="1000" tabindex="-1" data-name="product_id" class="align-middle o_column_sortable position-relative cursor-pointer o_many2one_barcode_cell opacity-trigger-hover" style="width: 234px;">
                                                                        <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">Product</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                                        </th>
                                                                        <th data-tooltip-delay="1000" tabindex="-1" data-name="name" class="align-middle o_column_sortable position-relative cursor-pointer o_section_and_note_text_cell opacity-trigger-hover" style="width: 129px;">
                                                                        <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">Label</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                                        </th>
                                                                        <th data-tooltip-delay="1000" tabindex="-1" data-name="analytic_distribution" class="align-middle o_column_sortable position-relative cursor-pointer o_analytic_distribution_cell opacity-trigger-hover" style="width: 213px;">
                                                                        <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">Analytic</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                                        </th>
                                                                        <th data-tooltip-delay="1000" tabindex="-1" data-name="quantity" class="align-middle o_column_sortable position-relative cursor-pointer o_list_number_th opacity-trigger-hover" style="min-width: 92px; width: 92px;">
                                                                        <div class="d-flex flex-row-reverse"><span class="d-block min-w-0 text-truncate flex-grow-1 o_list_number_th">Quantity</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                                        </th>
                                                                        <th data-tooltip-delay="1000" tabindex="-1" data-name="product_uom_id" class="align-middle o_column_sortable position-relative cursor-pointer opacity-trigger-hover" style="width: 60px;">
                                                                        <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">UoM</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                                        </th>
                                                                        <th data-tooltip-delay="1000" tabindex="-1" data-name="price_unit" class="align-middle o_column_sortable position-relative cursor-pointer o_list_number_th opacity-trigger-hover" style="min-width: 92px; width: 92px;">
                                                                        <div class="d-flex flex-row-reverse"><span class="d-block min-w-0 text-truncate flex-grow-1 o_list_number_th">Price</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                                        </th>
                                                                        <th data-tooltip-delay="1000" tabindex="-1" data-name="tax_ids" class="align-middle cursor-default o_many2many_tags_cell opacity-trigger-hover" style="width: 39px;">
                                                                        <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">Taxes</span><i class="d-none fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                                        </th>
                                                                        <th data-tooltip-delay="1000" tabindex="-1" data-name="price_subtotal" class="align-middle o_column_sortable position-relative cursor-pointer o_list_number_th opacity-trigger-hover" style="min-width: 104px; width: 104px;">
                                                                        <div class="d-flex flex-row-reverse"><span class="d-block min-w-0 text-truncate flex-grow-1 o_list_number_th">Subtotal</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                                        </th>
                                                                        <th class="o_list_controller o_list_actions_header position-static" style="width: 32px; min-width: 32px">
                                                                        <div class="o-dropdown dropdown o_optional_columns_dropdown border-top-0 text-center o-dropdown--no-caret"><button class="dropdown-toggle btn p-0" tabindex="-1" aria-expanded="false"><i class="o_optional_columns_dropdown_toggle oi oi-fw oi-settings-adjust"></i></button></div>
                                                                        </th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody class="ui-sortable">`



                    expense_tableHTML += create_ATS_invoice_table(plantName, expenses);

                    expense_tableHTML += `
                                                                        
                                                                </tbody>
                                                                <tfoot class="o_list_footer cursor-default">
                                                                    <tr>
                                                                        <td></td>
                                                                        <td></td>
                                                                        <td></td>
                                                                        <td></td>
                                                                        <td></td>
                                                                        <td></td>
                                                                        <td></td>
                                                                        <td></td>
                                                                        <td></td>
                                                                        <td></td>
                                                                    </tr>
                                                                </tfoot>
                                                            </table>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>
                    `;
                    
                    tableWrapper_expense.innerHTML = expense_tableHTML;

                    container.appendChild(tableWrapper_expense);

                    const textarea = document.getElementById(`x_studio_message_on_invoice_${plantName}`);
                    const firstEntry = entries[0];  // Assuming all entries have the same PO and date range
                    const poSection = createCustomerPOSection(firstEntry);
                    textarea.value = poSection;

                });
            }

        };


        function create_ATS_invoice_table(plantName, expenses){
            let expense_header = `[Expenses] All - Project # ${'PROJECT # HERE'}\nPer Diem, Fuel, Hotel, Misc. items.\n`;

            expense_table = `
                    <tr class="o_data_row o_is_line_section" data-id="datapoint_304">
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_handle_cell" data-tooltip-delay="1000" tabindex="-1" name="sequence">
                        <div name="sequence" class="o_field_widget o_field_handle"><span class="o_row_handle fa fa-sort ui-sortable-handle"></span></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_char o_section_and_note_text_cell" data-tooltip-delay="1000" tabindex="-1" name="name" colspan="8" data-tooltip="Expenses ${plantName}">
                        <div name="name" class="o_field_widget o_field_section_and_note_text o_field_text"><span>Expenses ${plantName}</span></div>
                        </td>
                        <td tabindex="-1"></td>
                    </tr>
                    <tr class="o_data_row o_is_product" data-id="datapoint_308">
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_handle_cell" data-tooltip-delay="1000" tabindex="-1" name="sequence">
                        <div name="sequence" class="o_field_widget o_field_handle"><span class="o_row_handle fa fa-sort ui-sortable-handle"></span></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one o_many2one_barcode_cell" data-tooltip-delay="1000" tabindex="-1" name="product_id" data-tooltip="[Expenses] All">
                        <div name="product_id" class="o_field_widget o_field_many2one_barcode"><a class="o_form_uri"><span>[Expenses] All</span></a></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_char o_section_and_note_text_cell" data-tooltip-delay="1000" tabindex="-1" name="name" data-tooltip="${expense_header}${expenses.ExpenseString}">
                        <div name="name" class="o_field_widget o_field_section_and_note_text o_field_text"><span>${expense_header}${expenses.ExpenseString}</span>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_analytic_distribution_cell" data-tooltip-delay="1000" tabindex="-1" name="analytic_distribution">
                        <div name="analytic_distribution" class="o_field_widget o_field_analytic_distribution">
                            <div class="o_field_tags d-inline-flex flex-wrap mw-100">
                                <span tabindex="-1">
                                    <div></div>
                                </span>
                            </div>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="quantity">1.00</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one" data-tooltip-delay="1000" tabindex="-1" name="product_uom_id" data-tooltip="Unit">Unit</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="price_unit">${expenses.TotalExpenses.toLocaleString('en-US', { minimumFractionDigits: 3, maximumFractionDigits: 3 })}</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_many2many_tags_cell" data-tooltip-delay="1000" tabindex="-1" name="tax_ids" data-tooltip="No records">
                        <div name="tax_ids" class="o_field_widget o_field_many2many_tags">
                            <div class="o_field_tags d-inline-flex flex-wrap"></div>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_readonly_modifier" data-tooltip-delay="1000" tabindex="-1" name="price_subtotal">$&nbsp;${expenses.TotalExpenses.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
                        <td tabindex="-1"></td>
                    </tr>
                `;
            return expense_table;
            
        };


        function updatePagination() {
            const tablesContainer = document.getElementById('tables-container');
            const tableWrappers = tablesContainer.querySelectorAll('.table-wrapper');

            tableWrappers.forEach((wrapper, index) => {
                if (index >= (currentPage - 1) * tablesPerPage && index < currentPage * tablesPerPage) {
                    wrapper.classList.add('active');
                } else {
                    wrapper.classList.remove('active');
                }
            });

            document.getElementById('page-info').textContent = `${currentPage} / ${totalPages}`;
            document.getElementById('prev-page').disabled = currentPage === 1;
            document.getElementById('next-page').disabled = currentPage === totalPages;
        }


        document.getElementById('prev-page').addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                updatePagination();
            }
        });


        document.getElementById('next-page').addEventListener('click', () => {
            if (currentPage < totalPages) {
                currentPage++;
                updatePagination();
            }
        });


        function getStartDate(endDateString) {
            const endDate = new Date(endDateString.substring(0, 4), endDateString.substring(4, 6) - 1, endDateString.substring(6, 8));
            const startDate = new Date(endDate);
            startDate.setDate(endDate.getDate() - 6);
            const year = startDate.getFullYear();
            const month = ('0' + (startDate.getMonth() + 1)).slice(-2);
            const day = ('0' + startDate.getDate()).slice(-2);
            return `${month}/${day}/${year}`;
        }


        function formatDate(dateString) {
            const year = dateString.substring(0, 4);
            const month = dateString.substring(4, 6);
            const day = dateString.substring(6, 8);
            return `${month}/${day}/${year}`;
        }


        function createCustomerPOSection(entry) {
            const customerPO = entry.CustomerPO;
            const endDate = formatDate(entry.WeekEnding);
            const startDate = getStartDate(entry.WeekEnding);

            return `${customerPO}\nStart Date: ${startDate}\nEnd Date: ${endDate}`;
        }


        function getFormattedDate() {
            const today = new Date();
            const month = String(today.getMonth() + 1).padStart(2, '0'); // Add 1 because months are zero-indexed
            const day = String(today.getDate()).padStart(2, '0');
            const year = today.getFullYear();
            return `${month}/${day}/${year}`;
        }

    </script>
    </body>
</html>

<!-- Scripts -->
<script src="https://code.jquery.com/jquery-3.5.1.js"></script>
<script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.3/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.3/dist/umd/popper.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>     

<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/1.7.1/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/1.7.1/js/buttons.html5.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/1.7.1/js/buttons.print.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

{% endblock %}