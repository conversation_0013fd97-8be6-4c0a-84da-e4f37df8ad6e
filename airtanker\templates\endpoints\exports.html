{% extends 'base.html' %}

{% block content %}
<head>
    <!-- Required meta tags -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Bootstrap CSS -->
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" rel="stylesheet">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.7.1/css/buttons.dataTables.min.css">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/styles_odoo.css') }}"  />

    <style>
        .file-upload-wrapper {
            border: 2px dashed #91b0b3;
            border-radius: 10px;
            padding: 10px;
            text-align: center;
            position: relative;
            cursor: pointer;
        }
        .flash-message {
            position: absolute;
            z-index: 1000; /* Ensures it's above other content */
            display: none; /* Initially hidden */
            opacity: 0; /* Start fully transparent */
            transition: opacity 2s; /* Smooth transition for fading */
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }        
        div#loading {
            width: 500px;
            height: 500px;
            display: none;
            background: url(/static/assets/loadingOdoo.gif) no-repeat center center;
            background-size: contain;
            cursor: wait;
            z-index: 1000;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            
            box-shadow: none; /* Ensure no shadow is applied */
            filter: none; /* Remove any filters that might create a shadow effect */
        }
        #detailsModal .modal-dialog {
            max-width: 90%;
        }
        /* Add a horizontal scrollbar to the modal body */
        #detailsModal .modal-body {
            overflow-x: auto;
        }

        .progress-container {
            width: 100%;
            background-color: #ddd;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .progress-bar {
            height: 30px;
            background-color: #4CAF50; /* Green background */
            background-image: linear-gradient(to right, #4caf50, #81c784); /* Gradient effect */
            border-radius: 8px;
            width: 0%; /* Initial width */
            position: relative; /* Relative position for inner text placement */
            overflow: hidden;
            display: flex;
            align-items: center; /* Center the label vertically */
        }

        .progress-bar span {
            color: white;
            margin-left: 10px; /* Give some space for the text from the start */
            font-weight: bold;
            z-index: 1; /* Make sure the text is above the background */
        }
        .progress-bar {
            transition: width 0.4s ease;
        }



    </style>
    <title>Exports</title>
</head>


<br>


<body class="bg-gradient-white">

    <!-- Modal for the spinner overlay -->
    <div class="modal" id="spinnerModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary mt-4" role="status">
                    </div>
                    <p class="mt-2">Getting approvals...</p>
                </div>
            </div>
        </div>
    </div>

    <h2 style="display: none;" id="waiting_message">Thank you for being patient. This process is expected to take a few minutes.</h2>
    <!-- <div id="loading"></div> -->
    <div id="progress-container" style="display: none; width: 90%; margin-top: 50px;"><!-- Container to control visibility -->
        <div id="progress-bar" class="progress-bar">
            <span id="progress-label">0%</span>
        </div>
    </div>
    
    <div class="wrapper fadeInDown" id="content">
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-message">
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}" style="margin-top: -230px;">{{ message }}</div>
            {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
        <div id="formContent">
            <img class="fadeIn first" src="{{ url_for('static', filename='assets/Odoo_logo_rgb.svg') }}" width="180" ondblclick="downloadAllPdfs()" alt="">
            <h1 class="h4 text-gray-900 mb-4 fadeIn first">                                            
                Export Approvals to Odoo
            </h1>
            <form id="form_content">
                <div>
                    {{ form.sundayPicker.label }}<br>
                    {{ form.sundayPicker(size=20, class_="date-input", onchange="checkSunday(this)", style="margin-bottom:20px; text-align:center;") }}
                    {% if form.sundayPicker.errors %}
                        <ul class="errors">
                            {% for error in form.sundayPicker.errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                </div>
            
                <!-- Buttons for different actions -->
            </form>
            <input type="submit" style="margin-bottom: 15px;" class="underlineHover fadeIn second" onclick="go_to_timesheets();"value="Export Timesheets"></input>
            <input type="submit" style="margin-bottom: 15px; margin-top: 15px;" class="underlineHover fadeIn third" onclick="go_to_bills();" value="Export Vendor Bills"></input>
            <input type="submit" style="margin-top: 15px;" class="underlineHover fadeIn fourth" onclick="go_to_invoices();" value="Export Customer Invoice"></input>
        </div>
    </div>
  </body>
</html>

<!-- Scripts -->
<script src="https://code.jquery.com/jquery-3.5.1.js"></script>
<script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.3/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.3/dist/umd/popper.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>     

<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/1.7.1/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/1.7.1/js/buttons.html5.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/1.7.1/js/buttons.print.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        
<style>
    .table-custom {
        width: 100%;  /* Full-width */
        background-color: #fff;  /* White background for clarity */
    }

    .preformatted-text {
        white-space: pre-wrap; /* Maintains whitespace and wraps text */
    }

    .table-custom th, .table-custom td {
        padding: 12px 10px;  /* Increase padding to make rows taller */
        border-bottom: 2px solid #f8f8f8;  /* Light gray border for subtle separation */
    }

    .table-custom tbody tr:hover {
        background-color: #f4f4f4;  /* Light gray background on row hover */
    }

    /* Optional: Add a subtle shadow to headers to lift them off the page */
    .table-custom thead th {
        background-color: #e9ecef;  /* Slightly different background for header */
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);  /* Subtle shadow for depth */
        position: sticky;  /* Make headers sticky if needed */
        top: 0;  /* Stick to the top of the table or viewport */
        z-index: 1020;  /* Ensures header stays on top of other content */
    }
</style>
<script>

    async function downloadAllPdfs() {
        try {
            const response = await fetch('/download_all_pdfs');

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = 'all_pdfs.zip'; // The name of the downloaded file
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('There was a problem with the fetch operation:', error);
        }
    }

    function go_to_invoices() {
        let selected_week_ending = document.getElementById('sundayPicker').value;
        window.location.href = "/export_invoices?week_ending=" + encodeURIComponent(selected_week_ending);
    }

    function go_to_bills() {
        let selected_week_ending = document.getElementById('sundayPicker').value;
        window.location.href = "/export_bills?week_ending=" + encodeURIComponent(selected_week_ending);
    }

    function go_to_timesheets() {
        let selected_week_ending = document.getElementById('sundayPicker').value;
        window.location.href = "/export_timesheets?week_ending=" + encodeURIComponent(selected_week_ending);
    }

    function showTablePopupBills(data) {
        let tableHtml = `
        <div style="max-height: 700px; max-width: 100%; overflow: auto;"> <!-- Container with scrollbars -->
            <table id="additionalDetailsTableDaily" class="table table-custom" style="min-width: 1500px;"> <!-- Table with min-width -->
                <thead>
                    <tr>
                        <th rowspan="2" style="max-width: 800px;"><input type="checkbox" id="select-all" checked></th>
                        <th rowspan="2"></th> <!-- Add this column for the expand/collapse button -->
                        <th rowspan="2">Bill Name</th>

                        <th rowspan="2">Contractor Name</th>

                        <th rowspan="2">SOW Contact Name</th>
                        <th style="display: none;">SOW Contact ID</th>
                        <th style="display: none;">WorkOrderName</th>
                        <th style="display: none;">ProjectID</th>

                        
                        <th style="display: none;">LineItemWeekEnding</th>

                        <th colspan="5">Hours</th> <!-- Merged "Hours" column header -->

                        <th style="display: none;">ST Hours</th>
                        <th style="display: none;">TT Hours</th>
                        <th style="display: none;">OT Hours</th>
                        <th style="display: none;">DT Hours</th>
                        <th style="display: none;">Holiday Hours</th>

                        <th style="display: none;">Timesheet ID</th>

                        <th style="display: none;">WorkOrderID</th>
                        
                        <th style="display: none;">Contractor ID</th>
                        <th style="display: none;">RateType</th>
                        
                        <!-- Add more table headers based on your data structure -->
                    </tr>
                    <tr>
                        <th style="width: 100px;">ST</th>
                        <th style="width: 100px;">TT</th>
                        <th style="width: 100px;">OT</th>
                        <th style="width: 100px;">DT</th>
                        <th style="width: 100px;">HT</th>
                    </tr>
                </thead>
                <tbody>`;

        data.forEach((item, index) => {
            if (item.Contacts) {
                // Unique identifier for the dropdown
                const dropdownId = `contact-select-${index}`;
                const billNameCellId = `bill-name-${index}`;
                
                contactCellHtml = `<td style="text-align: center;" id="${billNameCellId}">N/A</td>`;
                contactCellHtml += `<td style="text-align: center;">${item.ContractorName}</td>`;

                contactCellHtml += `<td style="text-align: center;">
                    <select class="form-control contact-dropdown" id="${dropdownId}" data-bill-name-cell="#${billNameCellId}">
                        <option value="">Select a contact</option>`;

                item.Contacts.forEach(contact => {
                    contactCellHtml += `<option value='[${contact.id}, ${contact.parent_id[0] !== undefined ? contact.parent_id[0] : false},${item.LineItemWeekEnding}]'>${contact.display_name}</option>`;
                });

                contactCellHtml += `</select></td>`;
            } else {
                // Display the default content
                // Start building the HTML content
                contactCellHtml = '';  // Initialize as empty string to start fresh accumulation

                // Append each piece of HTML content without overwriting the previous
                contactCellHtml += `<td style="text-align: center;">${item.Bill_Info[1]}</td>`; // Bill name
                contactCellHtml += `<td style="text-align: center;">${item.ContractorName}</td>`; // Contractor name
                contactCellHtml += `<td style="text-align: center;">${item.Bill_Info[2]}</td>`; // Contact name
                contactCellHtml += `<td style="display: none; text-align: center;">${item.Bill_Info[3]}</td>`; // Parent ID, hidden
            }
            tableHtml += `
                <tr data-index="${index}">
                    <td><input type="checkbox" class="row-checkbox" id="checkbox-${index}" checked></td>
                    <td><button class="btn btn-sm btn-info" data-toggle="child-row" data-index="${index}">-</button></td>
                    ${contactCellHtml}
                    <td style="display: none;">${item.WorkOrderName}</td>
                    <td style="display: none;">${item.ProjectID}</td>
                    <td style="display: none;">${item.LineItemWeekEnding}</td>
                    <td style="text-align: center;">${item.ApprovedData.Standard.Hours}</td>
                    <td style="text-align: center;">${item.ApprovedData.Travel.Hours}</td>
                    <td style="text-align: center;">${item.ApprovedData.OT.Hours}</td>
                    <td style="text-align: center;">${item.ApprovedData.DT.Hours}</td>
                    <td style="text-align: center;">${item.ApprovedData.Holiday.Hours}</td>

                    <td style="display: none;">${item.ApprovedData.Standard.Rate}</td>
                    <td style="display: none;">${item.ApprovedData.Travel.Rate}</td>
                    <td style="display: none;">${item.ApprovedData.OT.Rate}</td>
                    <td style="display: none;">${item.ApprovedData.DT.Rate}</td>
                    <td style="display: none;">${item.ApprovedData.Holiday.Rate}</td>

                    <td style="display: none;">${item.TimesheetID}</td>
                    <td style="display: none;">${item.WorkOrderID}</td>
                    <td style="display: none;">${item.ContractorID}</td>
                    <td style="display: none;">${item.RateType}</td>

                    <!-- Add more table data based on your data structure -->
                </tr>`;

            // Add expense row only if Expenses is not 0 or None
            if (item.Expenses?.ApprovedTotalExpenses) {
                tableHtml += `
                    <tr id="child-row-${index}">
                    <td colspan="10">
                        <div>
                            <strong>Approved Total Expenses:</strong> $${item.Expenses.ApprovedTotalExpenses ?? '0.00'}
                        </div>
                    </td>
                </tr>`;
            }
        });

        tableHtml += `
                </tbody>
            </table>
        </div>`; // Close the container div
            

        Swal.fire({
            title: 'Select Bills To Create',
            html: tableHtml,
            width: '1700px',
            showCancelButton: true, // This line ensures the cancel button is shown
            cancelButtonText: 'Cancel',
            confirmButtonText: 'Submit',
            allowOutsideClick: true, // Disable outside click
            didOpen: () => {
                // Event listener for the "Select All" checkbox
                const selectAllCheckbox = document.getElementById('select-all');
                selectAllCheckbox.addEventListener('click', (e) => {
                    const isChecked = e.target.checked;
                    document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                        checkbox.checked = isChecked;
                    });
                    validateSowContacts();
                });

                // Event listener for individual row checkboxes
                document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                    checkbox.addEventListener('change', () => {
                        const allCheckboxes = document.querySelectorAll('.row-checkbox');
                        const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
                        selectAllCheckbox.checked = allChecked;
                        validateSowContacts();
                    });
                });

                document.body.classList.remove('swal2-height-auto');

                // Process each dropdown
                document.querySelectorAll('.contact-dropdown').forEach(dropdown => {
                    if (dropdown) { // Ensure dropdown is not null
                        dropdown.addEventListener('change', async (event) => {
                            const selectedOption = event.target.options[event.target.selectedIndex];
                            const parsedValue = JSON.parse(selectedOption.value); // Ensure this is a JSON string
                            const contact_id = parsedValue[0];
                            const parent_id = parsedValue[1]; // Adjust according to the actual data structure
                            const week_ending = parsedValue[2];

                            try {
                                const response = await fetch('/get_parent_code', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    body: JSON.stringify({ parent_id: parent_id, contact_id: contact_id })
                                });

                                if (!response.ok) {
                                    throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                                }

                                const data = await response.json();
                                const parent_code = data.parent_code;

                                // Get the associated "Bill Name" cell and update it
                                const billNameCell = document.querySelector(event.target.dataset.billNameCell);
                                billNameCell.textContent = week_ending + parent_code;

                            } catch (error) {
                                console.error('Error fetching parent code:', error);
                            }
                        });

                        // Attach validation listener if the dropdown exists
                        dropdown.addEventListener('change', validateSowContacts);
                    }
                });

                // Toggle child rows on button click
                document.querySelectorAll('button[data-toggle="child-row"]').forEach(button => {
                    button.addEventListener('click', (event) => {
                        const button = event.target;
                        const index = button.getAttribute('data-index');
                        const parentRow = button.closest('tr');
                        const childRowId = `child-row-${index}`;
                        // Toggle child row visibility
                        let childRow = document.getElementById(childRowId);
                        if (childRow) {
                            // If child row exists, toggle its visibility
                            if (childRow.style.display === 'none') {
                                childRow.style.display = 'table-row';
                                button.textContent = '-';
                            } else {
                                childRow.style.display = 'none';
                                button.textContent = '+';
                            }
                        } else {
                            // If child row doesn't exist, create it
                            const item = data[index];
                            const childHtml = `
                                <tr id="${childRowId}">
                                    <td colspan="9">
                                        <div>
                                            <strong>Approved Total Expenses:</strong> $${item.Expenses?.ApprovedTotalExpenses ?? '0.00'}
                                        </div>
                                    </td>
                                </tr>`;
                            parentRow.insertAdjacentHTML('afterend', childHtml);
                            button.textContent = '-';
                        }
                    });
                });

                // Perform initial validation
                validateSowContacts();
            },

            preConfirm: () => {
                let selectedRows = [];
                data.forEach((item, index) => {
                    const checkbox = document.getElementById(`checkbox-${index}`);
                    if (checkbox.checked) {
                        // Check if the dropdown exists
                        const dropdown = document.getElementById(`contact-select-${index}`);
                        if (dropdown && dropdown.value) {  // Ensures dropdown exists and has a selection
                            const selectedOption = dropdown.options[dropdown.selectedIndex];
                            const contactName = selectedOption.text; // Getting the contact's name from the dropdown text

                            // Ensure the values are split correctly. Trim any extraneous whitespace.
                            const cleanValue = selectedOption.value.replace('[', '').replace(']', '').split(',').map(item => item.trim());
                            // Correctly parse the contactId as an integer, and leave parentId as a string.
                            const contactId = parseInt(cleanValue[0], 10); // Convert contactId to a number
                            const parent_id = parseInt(cleanValue[1], 10); // Convert contactId to a number

                            // Find the billName from the corresponding cell
                            const billNameCell = document.getElementById(`bill-name-${index}`);
                            const billName = billNameCell.textContent;

                            // Create or update sow_contact
                            let sow_contact = [contactId, billName, contactName, parent_id];


                            // Clone item to avoid mutating original data array directly
                            let updatedItem = {...item};

                            // Update or create Bill_Info array
                            if (updatedItem.Bill_Info) {
                                updatedItem.Bill_Info[0] = sow_contact;
                            } else {
                                updatedItem.Bill_Info = sow_contact;
                            }

                            // Add the updated item to selectedRows
                            selectedRows.push(updatedItem);
                        } else {
                            // Push the item as it is if no dropdown exists
                            selectedRows.push(item);
                        }
                    }
                });
                return selectedRows;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // result.value contains the selected rows
                updateSelectedRows(result.value, "Bills");
            }
            else if (result.dismiss === Swal.DismissReason.cancel) {
                // The user clicked on "Cancel" button
                console.log("Update cancelled by user.");
                // Here, you don't need to do anything, but you can add additional logic if needed
            }
        });
    }


    function showTablePopupTimesheets(data) {
        let tableHtml = `
        <div style="max-height: 700px; max-width: 100%; overflow: auto;"> <!-- Container with scrollbars -->
            <table class="table table-custom" style="min-width: 1500px;"> <!-- Table with min-width -->
                <thead>
                    <tr>
                        <th style="max-width: 800px;"><input type="checkbox" id="select-all-timesheet" checked></th>
                        <th style="width: 150px; vertical-align: middle; text-align: left;">Date</th>
                        <th style="min-width: 100px; vertical-align: middle; text-align: left;">Employee</th>                        
                        <th style="min-width: 100px; vertical-align: middle; text-align: left;">Project</th>
                        <th style="width: 200px; vertical-align: middle; text-align: left;">Task</th>
                        <th style="min-width: 100px; vertical-align: middle; text-align: left;">Description</th>
                        <th style="min-width: 100px; vertical-align: middle; text-align: left;">Hours</th>

                        <th style="display: none;">ProjectID</th>
                        <th style="display: none;">ContractorID</th>
                        <th style="display: none;">TaskID</th>
                        <th style="display: none;">TimesheetID</th>
                        <th style="display: none;">InternalID</th>

                    </tr>                    
                </thead>
                <tbody>`;

        data.forEach((item, index) => {            
            tableHtml += `
                <tr>
                    <td><input type="checkbox" class="row-checkbox" id="checkbox-${index}" checked></td>
                    <td style="text-align: left;">${item.Date}</td>
                    <td style="text-align: left;">${item.ContractorName}</td>
                    <td style="text-align: left;">${item.ProjectName}</td>
                    <td style="text-align: left;">${item.TaskName}</td>
                    <td style="text-align: left;">${item.Description}</td>
                    <td style="text-align: left;">${item.Hours}</td>

                    <td style="display: none;">${item.ProjectID}</td>
                    <td style="display: none;">${item.ContractorID}</td>                    
                    <td style="display: none;">${item.TaskID}</td>

                    <td style="display: none;">${item.TimesheetID}</td>
                    <td style="display: none;">${item.InternalID}</td>

                </tr>`;
        });

        tableHtml += `
                </tbody>
            </table>
        </div>`; // Close the container div

        Swal.fire({
            title: 'Select Timesheets To Export',
            html: tableHtml,
            width: '1700px',            
            showCancelButton: true, // This line ensures the cancel button is shown
            cancelButtonText: 'Cancel',
            confirmButtonText: 'Submit',
            allowOutsideClick: false, // Disable outside click
            didOpen: () => {
                // Event listener for the "Select All" checkbox
                document.getElementById('select-all-timesheet').addEventListener('click', (e) => {
                    const isChecked = e.target.checked;
                    document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                        checkbox.checked = isChecked;
                    });

                    validateSowContacts(true);
                });
                
                document.body.classList.remove('swal2-height-auto');

                // Attach validation listeners to checkboxes
                document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                    checkbox.addEventListener('change', () => validateSowContacts(true));
                });

                // Perform initial validation
                validateSowContacts(true);
            },

            preConfirm: () => {
                setTimeout(() => {
                    
                }, 2000);
                let selectedRows = [];
                data.forEach((item, index) => {
                    const checkbox = document.getElementById(`checkbox-${index}`);
                    if (checkbox.checked) {
                        selectedRows.push(item);
                    }
                });
                return selectedRows;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // result.value contains the selected rows
                updateSelectedRows(result.value, "Timesheets");
            }
            else if (result.dismiss === Swal.DismissReason.cancel) {
                // The user clicked on "Cancel" button
                console.log("Update cancelled by user.");
                // Here, you don't need to do anything, but you can add additional logic if needed
            }
        });
    }


    function validateForm() {
        let allValid = true;
        document.querySelectorAll('select[id^="project-"]').forEach((projectSelect, index) => {
            const taskSelect = document.getElementById(`task-${index}`);
            if (!projectSelect.value || !taskSelect.value) {
                allValid = false;
            }
        });
        Swal.getConfirmButton().disabled = !allValid;
    }


    async function handle_timesheet_errors(invalidEntries, projects) {
        let tableHtml = `
            <style>
                .table-custom th, .table-custom td {
                    padding: 8px;
                    text-align: left;
                }
                .contractor-column {
                    min-width: 150px;
                    max-width: 220px; /* Set a maximum width */
                    overflow: hidden;
                }
                .date-column {
                    min-width: 110px;
                }
                .project-column, .task-column, .description-column {
                    min-width: 200px;
                }
                .hours-column {
                    min-width: 80px;
                    max-width: 100px;
                }
                select {
                    min-width: 350px; /* Set a minimum width */
                    max-width: 350px; /* Set a maximum width */
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            </style>
            <div style="max-height: 700px; max-width: 100%; overflow: auto;"> <!-- Container with scrollbars -->
                <table class="table table-custom" style="min-width: 1500px;"> <!-- Table with min-width -->
                    <thead>
                        <tr>
                            <th class="contractor-column">Contractor</th>
                            <th class="date-column">Date</th>
                            <th class="project-column">Project</th>
                            <th class="task-column">Task</th>
                            <th class="hours-column">Orig. Project</th>
                            <th class="description-column">Task Desc.</th>
                            <th class="hours-column">Hours</th>
                        </tr>                    
                    </thead>
                    <tbody>`;

        for (let index = 0; index < invalidEntries.length; index++) {
            const entry = invalidEntries[index];
            const formattedDate = formatDate(entry.Date); // Format the date
            let tasks = [];

            if (entry.ProjectID) {
                // Fetch tasks for the pre-populated ProjectID
                tasks = await fetchTasks(entry.ProjectID);
            }

            tableHtml += `
                <tr>
                    <td class="contractor-column">${entry.EmployeeName}</td>
                    <td class="date-column">${formattedDate}</td>
                    <td class="project-column">
                        <select id="project-${index}" onchange="loadTasks(${index})" ${entry.ProjectID ? 'disabled' : ''}>
                            ${projects.map(project => `<option value="${project.id}" ${project.id === entry.ProjectID ? 'selected' : ''}>${project.name}</option>`).join('')}
                        </select>
                    </td>
                    <td class="task-column">
                        <select id="task-${index}" oninput="adjustWidth(this)">
                            ${tasks.map(task => `<option value="${task.id}" ${task.id === entry.TaskID ? 'selected' : ''}>${task.name.trim()}</option>`).join('')}
                        </select>
                    </td>
                    <td class="project-column">${entry.ProjectName}</td>
                    <td class="description-column">${entry.Description}</td>
                    <td class="hours-column">${entry.Hours}</td>
                </tr>`;
        }

        tableHtml += `
                </tbody>
            </table>
        </div>`; // Close the container div

        Swal.fire({
            title: 'Update Tasks On Timesheets',
            html: tableHtml,
            width: '1700px',
            showCancelButton: true, // This line ensures the cancel button is shown
            cancelButtonText: 'Cancel',
            confirmButtonText: 'Submit',
            allowOutsideClick: false, // Disable outside click
            didOpen: () => {
                document.querySelectorAll('select').forEach(select => {
                    adjustWidth(select);
                    select.addEventListener('change', validateForm); // Add change event listener for validation
                });
                validateForm(); // Initial validation check
                document.body.classList.remove('swal2-height-auto');

            },
            preConfirm: () => {
                let updatedEntries = invalidEntries.map((entry, index) => {
                    return {
                        ...entry,
                        ProjectID: document.getElementById(`project-${index}`).value,
                        TaskID: document.getElementById(`task-${index}`).value
                    };
                });
                return updatedEntries;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                $('#spinnerModal').modal({
                    backdrop: 'static', // Prevent clicking outside the modal to close
                    keyboard: false // Prevent closing the modal with keyboard ESC key
                }).appendTo('body');
                // result.value contains the updated entries
                fetch('/update_timesheet_entries', {
                    method: 'POST',
                    body: JSON.stringify(result.value),
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    // Handle the response after updating entries
                    // Refresh the page or show a success message
                    confirmExport('timesheets');
                })
                .catch(error => console.error('Error updating entries:', error))
            } else if (result.dismiss === Swal.DismissReason.cancel) {
                // The user clicked on "Cancel" button
                console.log("Update cancelled by user.");
                // Here, you don't need to do anything, but you can add additional logic if needed
            }
        });
    }


    async function fetchTasks(projectId) {
        try {
            const response = await fetch(`/get_tasks/${projectId}`);
            const tasks = await response.json();
            return tasks;
        } catch (error) {
            console.error('Error fetching tasks:', error);
            return [];
        }
    }


    // Function to load tasks for the selected project
    async function loadTasks(index) {
        const projectId = document.getElementById(`project-${index}`).value;
        const taskSelect = document.getElementById(`task-${index}`);

        // Clear existing options
        taskSelect.innerHTML = '';

        // Fetch tasks for the selected project
        try {
            const response = await fetch(`/get_tasks/${projectId}`);
            const tasks = await response.json();

            // Populate the task dropdown
            tasks.forEach(task => {
                const option = document.createElement('option');
                option.value = task.id;
                option.text = task.name.trim(); // Trim whitespace from task names
                taskSelect.appendChild(option);
            });

            // Adjust width after populating options
            adjustWidth(taskSelect);
            validateForm();
        } catch (error) {
            console.error('Error fetching tasks:', error);
        }
    }

    // Function to adjust the width of the select element based on the selected option
    function adjustWidth(selectElement) {
        const tempSelect = document.createElement('select');
        tempSelect.style.visibility = 'hidden';
        tempSelect.style.position = 'absolute';

        selectElement.parentNode.appendChild(tempSelect);
        tempSelect.innerHTML = selectElement.innerHTML;
        tempSelect.selectedIndex = selectElement.selectedIndex;

        const tempSelectWidth = tempSelect.offsetWidth;
        selectElement.style.width = tempSelectWidth + 'px';

        selectElement.parentNode.removeChild(tempSelect);
    }


    function showTablePopupInvoices(data, errors = null) {
        if (errors && errors.length > 0) {
            let errorHtml = `
            <div style="max-height: 300px; max-width: 100%; overflow: auto;"> <!-- Container with scrollbars -->
                <ul class="list-group" style="white-space: nowrap;">`; // Ensures the text stays on one line
            
            errors.forEach((error) => {
                errorHtml += `<li class="list-group-item list-group-item-danger">${error.message}</li>`;
            });

            errorHtml += `
                </ul>
            </div>`; // Close the container div

            Swal.fire({
                title: 'Errors Detected',
                html: errorHtml,
                icon: 'error',
                width: '800px', // Adjust the width as needed
                confirmButtonText: 'Acknowledge',
                allowOutsideClick: false,
                preConfirm: () => {
                    // Acknowledge and proceed to show data popup
                    showDataPopup();
                }
            });
        } else {
            showDataPopup();
        }

        function showDataPopup() {
            let tableHtml = `
            <div style="max-height: 700px; max-width: 100%; overflow: auto;"> <!-- Container with scrollbars -->
                <table class="table table-custom"> <!-- Table with min-width -->
                    <thead>
                        <tr>
                            <th rowspan="2" style="width: 80px;"><input type="checkbox" id="select-all-invoice" checked></th>
                            <th rowspan="2" style="width: 200px;">Invoice Name</th>
                            <th rowspan="2" style="width: 200px;">Contractor Name</th>
                            <th colspan="3" style="width: 300px;">Hours</th> <!-- Merged "Hours" column header -->
                            <th style="display: none;">ST Rate</th>
                            <th style="display: none;">OT Rate</th>
                            <th style="display: none;">DT Rate</th>
                            <th style="display: none;">Timesheet ID</th>
                            <th style="display: none;">WorkOrderID</th>
                            <th style="display: none;">Contractor ID</th>
                            <th style="display: none;">WeekEnding</th>
                            <th style="display: none;">Project ID</th>
                            <th style="display: none;">AccountID</th>
                            <th style="display: none;">CustomerPO</th>
                            <th style="display: none;">RateType</th>
                        </tr>
                        <tr>
                            <th style="width: 100px;">ST</th>
                            <th style="width: 100px;">OT</th>
                            <th style="width: 100px;">DT</th>
                        </tr>
                    </thead>
                    <tbody>`;

            data.forEach((item, index) => {
                tableHtml += `
                    <tr>
                        <td><input type="checkbox" class="row-checkbox" id="checkbox-${index}" checked></td>
                        <td style="text-align: left;" class="preformatted-text">${formatCustomerDates(item.CustomerPO, item.WeekEnding)}</td>
                        <td style="text-align: center;">${item.ContractorName}</td>
                        <td style="text-align: center;">${item.ApprovedData.Standard.Hours}</td>
                        <td style="text-align: center;">${item.ApprovedData.OT.Hours}</td>
                        <td style="text-align: center;">${item.ApprovedData.DT.Hours}</td>
                        <td style="display: none;">${item.ApprovedData.Standard.Rate}</td>
                        <td style="display: none;">${item.ApprovedData.OT.Rate}</td>
                        <td style="display: none;">${item.ApprovedData.DT.Rate}</td>
                        <td style="display: none;">${item.TimesheetID}</td>
                        <td style="display: none;">${item.WorkOrderID}</td>
                        <td style="display: none;">${item.ContractorID}</td>
                        <td style="display: none;">${item.WeekEnding}</td>
                        <td style="display: none;">${item.ProjectID}</td>
                        <td style="display: none;">${item.AccountID}</td>
                        <td style="display: none;">${item.CustomerPO}</td>
                        <td style="display: none;">${item.RateType}</td>
                    </tr>`;
            });

            tableHtml += `
                    </tbody>
                </table>
            </div>`; // Close the container div

            Swal.fire({
                title: 'Select Invoices To Create',
                html: tableHtml,
                width: '1200px',
                showCancelButton: true, // This line ensures the cancel button is shown
                cancelButtonText: 'Cancel',
                confirmButtonText: 'Submit',
                allowOutsideClick: false, // Disable outside click
                didOpen: () => {
                    // Event listener for the "Select All" checkbox
                    document.getElementById('select-all-invoice').addEventListener('click', (e) => {
                        const isChecked = e.target.checked;
                        document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                            checkbox.checked = isChecked;
                        });

                        validateSowContacts(true);
                    });
                    document.body.classList.remove('swal2-height-auto');

                    // Attach validation listeners to checkboxes
                    document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                        checkbox.addEventListener('change', () => validateSowContacts(true));
                    });

                    // Perform initial validation
                    validateSowContacts(true);
                },

                preConfirm: () => {
                    setTimeout(() => {

                    }, 2000);
                    let selectedRows = [];
                    data.forEach((item, index) => {
                        const checkbox = document.getElementById(`checkbox-${index}`);
                        if (checkbox.checked) {
                            selectedRows.push(item);
                        }
                    });
                    return selectedRows;
                }
            }).then((result) => {
                setTimeout(() => {

                }, 2000);
                if (result.isConfirmed) {
                    // result.value contains the selected rows
                    updateSelectedRows(result.value, "Invoices");
                } else if (result.dismiss === Swal.DismissReason.cancel) {
                    // The user clicked on "Cancel" button
                    console.log("Update cancelled by user.");
                    // Here, you don't need to do anything, but you can add additional logic if needed
                }
            });
        }
    }


    function formatCustomerDates(customerPO, weekEnding) {
        // Create date objects and set the time to noon (12:00) to avoid timezone issues
        console.log("Weekending: ", weekEnding);
        const week_ending = new Date(weekEnding);
        week_ending.setHours(12, 0, 0, 0);
        
        const week_starting = new Date(week_ending);
        week_starting.setDate(week_ending.getDate() - 6);

        function formatDate(date) {
            const day = (date.getDate() + 1).toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            return `${month}/${day}/${year}`;
        }

        const formattedStartDate = formatDate(week_starting);
        const formattedEndDate = formatDate(week_ending);
        return `${customerPO}\nStart Date: ${formattedStartDate}\nEnd Date: ${formattedEndDate}`;
    }


    function startProgress(url) {
        const evtSource = new EventSource(url);
        const progressBar = document.getElementById("progress-bar");
        const progress_container = document.getElementById("progress-container");
        progress_container.style.display = "block";

        evtSource.onmessage = function(event) {
            const data = event.data;
            try {
                if (data === "Completed" || data === "Failed") {
                    animateProgress(data === "Completed" ? 100 : 0); // Animate to 100% or reset to 0%
                    progressBar.textContent = data;
                    evtSource.close();
                    setTimeout(() => {
                        window.location.reload();  // Reload the page or redirect
                    }, 1000);  // Wait 1 second before refreshing
                } else {
                    const newProgress = Math.round(parseFloat(data));  // Convert to float and round
                    if (isNaN(newProgress)) {
                        throw new Error(`Invalid progress value: ${data}`);
                    }
                    animateProgress(newProgress);  // Animate progress update
                }
            } catch (error) {
                console.error("Error processing SSE data:", error);
                progressBar.textContent = "Error!";
                evtSource.close();  // Close the event source on errors
            }
        };

        evtSource.onerror = function(event) {
            console.error("SSE failed:", event);
            progressBar.textContent = "Error!";
            evtSource.close();  // Close the event source on errors
        };

        let queuedProgress = null;

        function animateProgress(targetPercentage) {
            if (queuedProgress !== null) {
                clearInterval(queuedProgress.animation);
                queuedProgress = null;
            }

            const currentPercentage = parseFloat(progressBar.style.width) || 0;
            if (currentPercentage === targetPercentage) return;  // No change needed

            const diff = Math.abs(targetPercentage - currentPercentage);
            const increment = diff > 10 ? 0.5 : 0.1;

            const animation = setInterval(() => {
                let currentWidth = parseFloat(progressBar.style.width) || 0;
                if ((targetPercentage > currentWidth && currentWidth + increment >= targetPercentage) ||
                    (targetPercentage < currentWidth && currentWidth - increment <= targetPercentage)) {
                    progressBar.style.width = targetPercentage + '%';
                    progressBar.textContent = targetPercentage + '%';
                    clearInterval(animation);
                    queuedProgress = null;
                } else {
                    currentWidth += (targetPercentage > currentWidth ? increment : -increment);
                    progressBar.style.width = currentWidth + '%';
                    progressBar.textContent = Math.round(currentWidth) + '%';
                }
            }, 10);

            queuedProgress = { target: targetPercentage, animation: animation };
        }
    }


    function validateSowContacts(isInvoice = false) {
        let allValid = true;
        let atLeastOneSelected = false;  // To check if at least one item is validly checked
        const rows = document.querySelectorAll('tbody tr');  // Get all table rows
        console.log(rows);
        rows.forEach(row => {
            const checkbox = row.querySelector('.row-checkbox');
            const dropdown = row.querySelector('.contact-dropdown');

            if (checkbox && checkbox.checked) {
                atLeastOneSelected = true;  // Mark that at least one checkbox is selected

                // Ensure that if a dropdown exists, it must have a valid selection
                if (dropdown && (dropdown.value === '' || dropdown.value === '[]')) {
                    allValid = false;
                }
            }
        });

        // Enable the OK button only if all conditions are valid and at least one item is selected
        const confirmButton = Swal.getConfirmButton();
        if (confirmButton) {
            confirmButton.disabled = !allValid || !atLeastOneSelected;
        }

        // Update the "Select All" checkbox state
        const allCheckboxes = document.querySelectorAll('.row-checkbox');
        const selectAllCheckbox = document.getElementById(isInvoice ? 'select-all-invoice' : 'select-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = Array.from(allCheckboxes).every(checkbox => checkbox.checked);
        }
    }


    function updateSelectedRows(selectedRows, type) {
        // Process the selected rows for updating
        //(selectedRows);
        if (type == "Bills") {
            export_to_odoo("vendor_bills", selectedRows);
        }
        else if (type == "Invoices") {
            export_to_odoo("customer_invoices", selectedRows);
        }
        else if (type == "Timesheets") {
            export_to_odoo("timesheets", selectedRows);
        }
    }


    // Launch popups
    async function confirmExport(actionType) {

        $('#spinnerModal').modal({
            backdrop: 'static', // Prevent clicking outside the modal to close
            keyboard: false // Prevent closing the modal with keyboard ESC key
        }).appendTo('body');

        let actionUrl = '/get_vendor_bills'; // Default action
        if (actionType === 'vendor_bills') {
            actionUrl = '/get_vendor_bills';
        } else if (actionType === 'customer_invoices') {
            actionUrl = '/export_invoices';
        } else if (actionType === 'timesheets'){
            actionUrl = '/get_timesheets';
        }

        const form = document.getElementById('form_content');
        const formData = new FormData(form);


        try {
            const response = await fetch(actionUrl, {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            });

            if (!response.ok) {
                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();  // Parse the JSON in the response

            if (data.redirect_url) {
                // If a redirect URL is provided in the response, redirect the browser
                window.location.href = data.redirect_url;
            } else if (data.status === 'error' && data.invalid_entries) {
                await handle_timesheet_errors(data.invalid_entries, data.projects);
            } else {
                if (actionType === 'vendor_bills') {
                    showTablePopupBills(data.data);
                } else if (actionType === 'customer_invoices') {                    
                    showTablePopupInvoices(data.data, data.errors);
                } else if (actionType === 'timesheets'){
                    showTablePopupTimesheets(data.data);
                }
            }
        } catch (error) {
            console.error('Error:', error);
        } finally {
            $('#spinnerModal').modal('hide');
        }
    }
    
    
    // Compare button click
    function export_to_odoo(actionType, selectedRows) {
        event.preventDefault();
        document.getElementById('waiting_message').style.display = 'block';

        //$("#loading").show();
        $("#content").hide();

        let actionUrl = '/post_vendor_bills'; // Default action
        if (actionType === 'vendor_bills') {
            actionUrl = '/post_vendor_bills';
        } else if (actionType === 'customer_invoices') {
            actionUrl = '/post_customer_invoices';
        } else if (actionType === 'timesheets'){
            actionUrl = '/post_timesheets';
        }

        fetch(actionUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(selectedRows),
            credentials: 'same-origin'
        })        
        .then(response => {
            if (!response.ok) {
                // If the server response is not ok (e.g., 500 Internal Server Error), throw an error
                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.task_id){
                startProgress(`/progress/${data.task_id}`);
            }
            else{
                console.log("No task_id");
            }
        })
        .catch(error => {
            // Handle any errors that occurred during the fetch or due to a server error
            console.error('Error:', error);
            alert('An error occurred while exporting to odoo. Please try again.');
        })
    };    


    document.addEventListener('DOMContentLoaded', function () {
        setMostRecentSunday();
    });


    function setMostRecentSunday() {
        const selectedWeekEnding = '{{ selected_week_ending }}';
        let formattedDate;

        if (selectedWeekEnding != 'False') {
            formattedDate = selectedWeekEnding;
        } else {
            const today = new Date();
            const dayOfWeek = today.getDay(); // Sunday - 0, Monday - 1, ..., Saturday - 6
            const difference = dayOfWeek % 7; // Calculate difference to get back to the previous Sunday
            const mostRecentSunday = new Date(today.setDate(today.getDate() - difference));
            
            // Format the date as YYYY-MM-DD
            formattedDate = mostRecentSunday.toISOString().split('T')[0];
        }
        
        document.getElementById('sundayPicker').value = formattedDate;
    }


    function checkSunday(input) {
        const selectedDate = new Date(input.value);
        if (selectedDate.getDay() !== 6) {
            alert('Please select a Sunday.');
            setMostRecentSunday(); // Reset to the most recent Sunday if the check fails
        }
    }


    document.addEventListener('DOMContentLoaded', (event) => {
        const flashMessage = document.querySelector('.flash-message');
        if (flashMessage) {
            flashMessage.style.display = 'block'; // Make it visible
            flashMessage.style.opacity = 1; // Fade in
            setTimeout(() => {
                flashMessage.style.opacity = 0; // Fade out after 5 seconds
                setTimeout(() => flashMessage.remove(), 2000); // Remove from DOM after it's invisible
            }, 4000);
        }
    });
    
    
    function formatDate(dateString) {
        const date = new Date(dateString);
        
        // Extract the components
        const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-based
        const day = String(date.getDate()).padStart(2, '0');
        const year = date.getFullYear();

        // Construct the formatted date string
        return `${month}/${day}/${year}`;
    }
    </script>
                                               
</body>
{% endblock %}
