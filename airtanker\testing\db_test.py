import os
from dotenv import load_dotenv
import pyodbc

# Load environment variables from .env file
load_dotenv()

# Retrieve database connection info from environment variables
DB_SERVER = os.getenv('DB_SERVER')
DB_DATABASE = os.getenv('DB_DATABASE')
DB_USERNAME = os.getenv('DB_USERNAME')
DB_PASSWORD = os.getenv('DB_PASSWORD')

def test_database_connection():
    try:
        # Construct the connection string
        # Adjust the DRIVER part as needed based on your database and environment
        conn_str = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={DB_SERVER};"
            f"DATABASE={DB_DATABASE};"
            f"UID={DB_USERNAME};"
            f"PWD={DB_PASSWORD}"
        )
        
        # Connect to the database
        conn = pyodbc.connect(conn_str)
        
        # Open a cursor to perform database operations
        with conn.cursor() as cursor:
            # Execute a query
            cursor.execute("SELECT 1;")
            # Fetch result
            result = cursor.fetchone()
            print("Database connection test successful:", result[0])

        # Close the connection
        conn.close()

    except Exception as e:
        print("Failed to connect to the database:", e)

if __name__ == "__main__":
    test_database_connection()

