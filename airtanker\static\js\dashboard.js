const spinnerModal = new bootstrap.Modal('#spinnerModal', {
    backdrop: 'static',
    keyboard: false 
});

var totalInvoiced = 0;
var totalBilled = 0;

$(document).ready(function() {
    fetchData();
})

function GetWeekEnding() {
    return $('#weekEndingPicker').val();
}

function fetchData() {
    $( '#workOrderTable' ).DataTable().destroy();
    
    spinnerModal.show();
    $( '#financeContainer' ).hide();

    let args = new URLSearchParams({weekEnding: GetWeekEnding()});
    fetch(`/api/get_workorder_dashboard?${args}`)
    .then(response => response.json())
    .then(data => {
        populateWorkOrders(data.WorkOrders);

        $('#invoiceTable').DataTable().destroy();
        totalInvoiced = 0;
        
        $('#billsTable').DataTable().destroy();
        totalBilled = 0;

        if (data.Invoices.length > 0 || data.Bills.length > 0) {
            $( '#financeContainer' ).show();
            populateFinancialData(data.Invoices, data.Bills);
        }
    })
    .finally(() => {
        spinnerModal.hide();
    });
}

function populateWorkOrders(data) {
    var collapsedGroups = {};
    var groupCol = 4;    
    var table = $('#workOrderTable').DataTable({
        data: data,
        order: [[groupCol, "asc"]],
        columns: [
            { data: "StartDate" },
            { data: "WorkOrder" },
            { data: "Employee" },
            { data: "Site" },
            { data: "ProjectNumber" },
            { data: "Comments" },
            { data: "Invoice_Created" },
            { data: 'Bill_Created'},
            { data: "Name" }
        ],
        pageLength: 25,
        searching: false,
        rowGroup: {
            dataSrc: 'ProjectNumber',
            startRender: function ( rows, group ) {
                var collapsed = !!collapsedGroups[group];
                rows.nodes().each(function (r) {
                    r.style.display = collapsed ? 'none' : '';
                });
                return $('<tr/>')
                    .append('<td colspan="9">' + group + ' (' + rows.count() + ')</td>')
                    .attr('data-name', group)
                    .toggleClass('collapsed', collapsed);
                },
            },
            initComplete: function () {
                $('#workOrderTable tbody tr.dtrg-group').each(function() {
                    var name = $(this).data('name');
                    collapsedGroups[name] = !collapsedGroups[name];
                });
                this.api().draw()
            }
    });
    
    $('#workOrderTable tbody').on('click', 'tr.dtrg-group', function () {
        var name = $(this).data('name');
        collapsedGroups[name] = !collapsedGroups[name];
        table.draw(false);
    });
}

function populateFinancialData(invoices, bills) {
    if (invoices.length > 0) {
        $( '#invoiceTable' ).show();
        defineFinancialTable('#invoiceTable', invoices);
    } else {
        $( '#invoiceTitle' ).hide();
        $( '#invoiceTable' ).hide();
    }
    
    if (bills.length > 0) {
        $( '#billsTable' ).show();
        defineFinancialTable('#billsTable', bills);
    } else {
        $( '#billsTitle' ).hide();
        $( '#billsTable' ).hide();
    }

    $('#spanTotalInvoiced').text(DataTable.render
        .number(',','.',2,'$')
        .display(totalInvoiced));

    var formattedBilled = DataTable.render
        .number(',','.',2,'$')
        .display(totalBilled);
    if (totalBilled <= 0) {
        $('#spanTotalBilled').text(formattedBilled);
    } else {
        $( '#spanTotalBilled' ).addClass("text-danger").text('(' + formattedBilled + ')');
    }

    var net = totalInvoiced - totalBilled;
    $('#spanNetTotal').text(DataTable.render
                    .number(',','.',2,'$')
                    .display(net));

    var utilization = net / totalInvoiced;
    drawGauge(0,1, utilization);
    var utilizationFormatted = DataTable.render.number(null, null, 2, '','%').display(utilization * 100);
    $('#utilization').text(utilizationFormatted);
}

function defineFinancialTable(table, data) {
    var collapsedGroups = {};
    var groupCol = 1;    

    var tbody = table + ' tbody';
    var groupIdent = tbody + ' tr.dtrg-group';

    var table = $( table ).DataTable({
        data: data,
        order: [[groupCol, "desc"]],
        columns: [
            { data: 'name' },                
            { data: 'partner_id.1' },
            {
                data: 'amount_total',
                render: (data) => {
                    var currency = DataTable.render
                    .number(',','.',2,'$')
                    .display(data);
                
                    return currency;
                }
            }
        ],
        paging: false,
        searching: false,
        info: false,
        rowGroup: {
            dataSrc: 'partner_id.1',
            startRender: function ( rows, group ) {
                var collapsed = !!collapsedGroups[group];
                var total = 0;

                rows.nodes().each(function (r) {
                    r.style.display = collapsed ? 'none' : '';                        
                });

                rows.data().each(function (r) {
                    total += r.amount_total;
                })

                var currency = DataTable.render
                .number(',','.',2,'$')
                .display(total);

                return $('<tr/>')
                    .append('<td colspan="2">' + group + ' (' + rows.count() + ')</td>')
                    .append('<td class="text-end">' + currency + '</td>')
                    .attr('data-name', group)
                    .toggleClass('collapsed', collapsed);
                },
            },
        initComplete: function () {
            $(groupIdent).each(function() {
                var name = $(this).data('name');
                collapsedGroups[name] = !collapsedGroups[name];
            });
            this.api().draw()
        },
        footerCallback: function (row, data, start, end, display) {
            let api = this.api();
            
            // Remove the formatting to get integer data for summation
            let intVal = function (i) {
                return typeof i === 'string'
                    ? i.replace(/[\$,]/g, '') * 1
                    : typeof i === 'number'
                    ? i
                    : 0;
            };
            var total = api
                .column(2)
                .data()
                .reduce((a, b) => intVal(a) + intVal(b), 0);
            // Total over all pages
            var totalFormatted = DataTable.render
            .number(',','.',2,'$')
            .display(total);
            
            if (table == '#invoiceTable') {
                totalInvoiced = total;
            } else {
                totalBilled = total;
            }

            // Update footer
            api.column(2).footer().innerHTML = totalFormatted;               
        }
    });
    
    $(tbody).on('click', 'tr.dtrg-group', function () {
        var name = $(this).data('name');
        collapsedGroups[name] = !collapsedGroups[name];
        table.draw(false);
    });
}

function drawGauge(min, max, val) {
    var options = {
        angle: 0,        
    }
    var target = document.getElementById('gauge');
    var gauge = new Gauge(target).setOptions(options);
    gauge.maxValue = max;
    gauge.setMinValue(min);
    gauge.set(val)
}