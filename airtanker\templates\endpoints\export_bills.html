{% extends 'base.html' %}

{% block styles %}
<link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/export_summary.css') }}"  />
<style>
    .progress-container {
        width: 100%;
        background-color: #ddd;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .progress-bar {
        height: 30px;
        background-color: #4CAF50; /* Green background */
        background-image: linear-gradient(to right, #4caf50, #81c784); /* Gradient effect */
        border-radius: 8px;
        width: 0%; /* Initial width */
        position: relative; /* Relative position for inner text placement */
        overflow: hidden;
        display: flex;
        align-items: center; /* Center the label vertically */
    }

    .progress-bar span {
        color: white;
        margin-left: 10px; /* Give some space for the text from the start */
        font-weight: bold;
        z-index: 1; /* Make sure the text is above the background */
    }
    .progress-bar {
        transition: width 0.4s ease;
    }
    div#loading {
        width: 500px;
        height: 500px;
        display: none;
        background: url(/static/assets/loadingOdoo.gif) no-repeat center center;
        background-size: contain;
        cursor: wait;
        z-index: 1000;
        position: relative;
        top: 25%;
        left: 50%;
        transform: translate(-50%, -50%);
        
        box-shadow: none; /* Ensure no shadow is applied */
        filter: none; /* Remove any filters that might create a shadow effect */
    }
    .table-container {
        position: relative;
        height: 500px; /* Adjust height as needed */
    }
    .table-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: none;
        z-index: 0;
    }
    .table-wrapper.active {
        display: block;
        z-index: 1;
    }
    .pagination-controls {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
        margin-top: 20px;
    }
    .pagination-controls button {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        margin: 0 10px;
        cursor: pointer;
        border-radius: 5px;
    }
    .pagination-controls button:disabled {
        background-color: #cccccc;
        cursor: not-allowed;
    }
    .pagination-controls span {
        font-size: 16px;
        margin: 0 10px;
    }
</style>
<style>
    .o_main_navbar {background-color: #e48108; border-bottom: 0px;}
    .o_main_navbar .show .dropdown-toggle {background-color: #e48108;}
    .o_main_navbar > ul > li > a:hover, .o_main_navbar 
    > ul > li > label:hover {background-color: #f79e30;}
    .o_main_navbar > a:hover, .o_main_navbar > a:focus, .o_main_navbar 
    > button:hover, .o_main_navbar 
    > button:focus {background-color: #f79e30; color: inherit;}
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    let currentPage = 1;
    const tablesPerPage = 1;
    let totalPages = 0;
    let response_data = null;
    let response_errors = null;

    document.addEventListener('DOMContentLoaded', () => {
        console.log("getting data...");
        $("#loading").show();
        fetch('/get_vendor_bills')
        .then(response => response.json())
        .then(data => {
            if (data.status === "error") {
                alert("Error: No timesheets available for selected week ending");
                window.location.href = "/exports";
            }
            else{
                $("#loading").hide()
                showTablePopupBills(data.data, data.errors)
                document.getElementById("paging_tabs").style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error fetching data:', error);
            alert(`Error fetching data: ${error}`);
            window.location.href = "/exports";
        });
    });

    function startProgress(url) {
        const evtSource = new EventSource(url);
        const progressBar = document.getElementById("progress-bar");
        const progress_container = document.getElementById("progress-container");
        progress_container.style.display = "block";

        evtSource.onmessage = function(event) {
            const data = event.data;
            try {
                if (data === "Completed" || data === "Failed") {
                    if (data === "Failed"){
                        alert("Error occurred during export process");
                    }
                    animateProgress(data === "Completed" ? 100 : 0); // Animate to 100% or reset to 0%
                    progressBar.textContent = data;
                    evtSource.close();
                    setTimeout(() => {
                        window.location.href = "/exports";
                    }, 1000);  // Wait 1 second before refreshing
                } else {
                    const newProgress = Math.round(parseFloat(data));  // Convert to float and round
                    if (isNaN(newProgress)) {
                        throw new Error(`Invalid progress value: ${data}`);
                    }
                    animateProgress(newProgress);  // Animate progress update
                }
            } catch (error) {
                console.error("Error processing SSE data:", error);
                progressBar.textContent = "Error!";
                evtSource.close();  // Close the event source on errors
            }
        };

        evtSource.onerror = function(event) {
            console.error("SSE failed:", event);
            progressBar.textContent = "Error!";
            evtSource.close();  // Close the event source on errors
        };

        let queuedProgress = null;

        function animateProgress(targetPercentage) {
            if (queuedProgress !== null) {
                clearInterval(queuedProgress.animation);
                queuedProgress = null;
            }

            const currentPercentage = parseFloat(progressBar.style.width) || 0;
            if (currentPercentage === targetPercentage) return;  // No change needed

            const diff = Math.abs(targetPercentage - currentPercentage);
            const increment = diff > 10 ? 0.5 : 0.1;

            const animation = setInterval(() => {
                let currentWidth = parseFloat(progressBar.style.width) || 0;
                if ((targetPercentage > currentWidth && currentWidth + increment >= targetPercentage) ||
                    (targetPercentage < currentWidth && currentWidth - increment <= targetPercentage)) {
                    progressBar.style.width = targetPercentage + '%';
                    progressBar.textContent = targetPercentage + '%';
                    clearInterval(animation);
                    queuedProgress = null;
                } else {
                    currentWidth += (targetPercentage > currentWidth ? increment : -increment);
                    progressBar.style.width = currentWidth + '%';
                    progressBar.textContent = Math.round(currentWidth) + '%';
                }
            }, 10);

            queuedProgress = { target: targetPercentage, animation: animation };
        }
    }

    function export_to_odoo(event, data) {
        document.getElementById('waiting_message').style.display = 'block';
        document.getElementById('tables-container').style.display = 'none';
        document.getElementById("paging_tabs").style.display = 'none';

        let actionUrl = '/post_vendor_bills';
        fetch(actionUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(response_data),
            credentials: 'same-origin'
        })        
        .then(response => {
            if (!response.ok) {
                // If the server response is not ok (e.g., 500 Internal Server Error), throw an error
                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.task_id){
                startProgress(`/progress/${data.task_id}`);
            }
            else{
                console.log("No task_id");
            }
        })
        .catch(error => {
            // Handle any errors that occurred during the fetch or due to a server error
            console.error('Error:', error);
        })
    };    

    document.getElementById('export_button').addEventListener('click', function(event) {
        export_to_odoo(event, response_data);
    });

    function showTablePopupBills(data, errors = null) {
        if (errors && errors.length > 0) {

            response_errors = errors;                

            let errorHtml = `
            <div style="max-height: 300px; z-index: 1500; max-width: 100%;"> <!-- Container with scrollbars -->
                <ul class="list-group" style="white-space: nowrap;">`;

            response_errors.forEach((error, index) => {
                errorHtml += `<li class="list-group-item list-group-item-danger">
                    ${error.ContractorName}
                    <label for="contactDropdown_${index}"></label>
                    <select id="contactDropdown_${index}" class="form-control" onchange="updateContact(${index})">
                        <option value="">Select a contact</option>`;

                error.Contacts.forEach(contact => {
                    errorHtml += `<option value="${contact.id}" data-display_name="${contact.display_name}" data-parent_id="${contact.parent_id[0]}">${contact.display_name}</option>`;
                });

                errorHtml += `</select>
                </li>`;
            });

            errorHtml += `
                </ul>
            </div>`;

            Swal.fire({
                title: 'Select Correct SOW Contact',
                html: errorHtml,
                icon: 'error',
                width: '800px',
                confirmButtonText: 'Acknowledge',
                allowOutsideClick: false,
                preConfirm: () => {
                    let allSelected = true;
                    response_errors.forEach((error, index) => {
                        const dropdown = document.getElementById(`contactDropdown_${index}`);
                        const selectedContact = dropdown.value;
                        const selectedOption = dropdown.options[dropdown.selectedIndex];

                        if (selectedContact) {
                            error.SOW_Contact = [selectedContact, selectedOption.getAttribute('data-display_name'), selectedOption.getAttribute('data-parent_id')]
                            console.log("Contact Selected: ", error.SOW_Contact);
                        } else {
                            allSelected = false;
                        }
                    });

                    if (!allSelected) {
                        Swal.showValidationMessage(`Please select a contact for each error`);
                        return false;
                    } else {
                        response_errors.forEach((error) => {
                            const contactId = error.SOW_Contact[0];
                            if (!data[contactId]) {
                                data[contactId] = [];
                            }
                            data[contactId].push(error);
                        });
                        console.log("Data: ", data);
                    }
                },
                didOpen: () => {
                    Swal.getContainer().style.zIndex = 1500;
                    document.body.classList.remove('swal2-height-auto');
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    response_data = data;
                    createTables(data);
                    updatePagination();
                }
            });
        }
        else {

            response_data = data;
            createTables(data);
            updatePagination(); // Ensure pagination is updated after tables are created
        }
    }

    function updateContact(index) {
        const dropdown = document.getElementById(`contactDropdown_${index}`);
        const selectedContactID = dropdown.value;
        const parent_id = dropdown.options[dropdown.selectedIndex].getAttribute('data-parent_id');

        if (selectedContactID || parent_id) {
            try {
                fetch('/get_parent_code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ parent_id: parent_id, contact_id: selectedContactID })
                }).then(response => {
                    if (!response.ok) {
                        throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                    }
                    
                    return response.json();
                })
                .then(data => {
                    const parent_code = data.parent_code;

                    response_errors[index].ParentCode = parent_code;
                    response_errors[index].SOW_Company = [parent_id, ""]

                })
                .catch(error => console.log(error));

            } catch (error) {
                console.error('Error fetching parent code:', error);
            }
        }
    }

    function createTables(data) {
        const tablesContainer = document.getElementById('tables-container');
        totalPages = Math.ceil(Object.keys(data).length / tablesPerPage);
        console.log(data);
        for (const sow_contact_id in data) {

            if (data.hasOwnProperty(sow_contact_id)) {
                createTable(tablesContainer, sow_contact_id, data[sow_contact_id]);                    
            }
        }

    }

    function createTable(container, sow_contact_id, entries) {
        const tableWrapper = document.createElement('div');
        tableWrapper.classList.add('table-wrapper', 'o_form_sheet_bg');
        tableWrapper.id = `table-${sow_contact_id}`;

        let plant_expenses = {};

        const billInfo = `${entries[0].Bill_Info}${entries[0].ParentCode}`;
        const sow_contact_name = entries[0].SOW_Contact[1];

        let sow_company = entries[0].ContractorName;

        if (entries[0].SOW_Company) {
            sow_company = entries[0].SOW_Company[1];
        }


        const allEntriesInvalid = entries.every(entry => !entry.RateType || entry.RateType === false);

        if (allEntriesInvalid) {
            // Gather WorkOrderName and ContractorName for each entry
            const invalidEntriesDetails = entries.map(entry => `${entry.WorkOrderName} (${entry.ContractorName})`);
            
            // Construct the alert message
            const alertMessage = `All these work orders are missing a RATE TYPE under SOW Contact ${sow_contact_name}. Skipping these for now.\n\nEntries:\n${invalidEntriesDetails.join('\n')}`;
            
            // Alert the user
            alert(alertMessage);
            totalPages -= 1;
            return;
        }



        let expense_header = `[Expenses] All - Project # ${sow_contact_id}\nPer Diem, Fuel, Hotel, Misc. items.\n`;
        let expense_string = "\n";
        let total_expenses = 0;

        let tableHTML = `
            
            <!-- Add the account name and checkboxes here -->

            <!-- <div class="table-header">
                <h2>${billInfo}</h2>
                <br>
                <label>
                    <input type="checkbox" id="breakdown-expenses-${sow_contact_id}"> Breakdown Expenses
                </label>
                <br>
                <label>
                    <input type="checkbox" id="separate-expenses-${sow_contact_id}"> Separate Expenses from Hours
                </label>
            </div> -->

            <!-- Existing table structure --> 
            <div class="o_action o_view_controller o_form_view o_xxl_form_view h-100">
                <div class="o_form_view_container">
                    <div class="o_content">
                        <div class="o_form_editable d-flex o_form_saved flex-nowrap h-100">
                        <div class="o_form_sheet_bg">
                            <div class="o_form_sheet position-relative clearfix">
                                <div class="o-form-buttonbox oe_button_box position-relative text-end o_not_full"></div>

                                <!-- TITLE --> 

                                <div class="oe_title">
                                    <span class="o_form_label">
                                        <div name="move_type" class="o_field_widget o_readonly_modifier o_required_modifier o_field_selection"><span raw-value="in_invoice">Vendor Bill</span></div>
                                    </span>
                                    <h1>
                                        <div name="name" class="o_field_widget o_field_char"><span class="o_field_widget o_field_text o_input" id="name" style="border: none; padding: 0;">${billInfo}</span></div>
                                    </h1>
                                </div>

                                <!-- BILL INFORMATION -->

                                <div class="o_group row align-items-start">
                                <div class="o_inner_group grid col-lg-6">
                                    <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                        <div class="o_cell flex-grow-1 flex-sm-grow-0 o_wrap_label w-100 text-break text-900" style=""><label class="o_form_label" for="partner_id">Vendor</label></div>
                                        <div class="o_cell flex-grow-1 flex-sm-grow-0" style="width: 100%;">
                                            <div>${sow_contact_name}</div>
                                        </div>
                                    </div>
                                    <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                        <div class="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900"><label class="o_form_label" for="x_studio_service_date">Service Date</label></div>
                                        <div class="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style="width: 100%;">
                                            <div name="x_studio_service_date" class="o_field_widget o_field_date">
                                            <div class="o_datepicker" aria-atomic="true" data-target-input="#o_datepicker_28"><span type="hidden" id="o_datepicker_28">${formatDate(entries[0].LineItemWeekEnding)}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                        <div class="o_cell flex-grow-1 flex-sm-grow-0 o_wrap_label w-100 text-break text-900" style=""><label class="o_form_label" for="ref">Bill Reference</label></div>
                                        <div class="o_cell flex-grow-1 flex-sm-grow-0" style="width: 100%;">
                                        </div>
                                    </div>
                                    <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                        <div class="o_cell flex-grow-1 flex-sm-grow-0 o_wrap_label w-100 text-break text-900" style=""><label class="o_form_label oe_edit_only" for="invoice_vendor_bill_id">Auto-Complete<sup class="text-info p-1" data-tooltip-template="web.FieldTooltip" data-tooltip-info="{&quot;field&quot;:{&quot;help&quot;:&quot;Auto-complete from a past bill.&quot;}}" data-tooltip-touch-tap-to-show="true">?</sup></label></div>
                                        <div class="o_cell flex-grow-1 flex-sm-grow-0" style="width: 100%;">
                                            <div name="invoice_vendor_bill_id" class="o_field_widget o_field_many2one oe_edit_only">
                                            <div class="o_field_many2one_selection">
                                                <div>
                                                    <div class="o-autocomplete dropdown"><span type="text" class="o-autocomplete--input o_input" autocomplete="off" role="combobox" aria-autocomplete="list" aria-haspopup="listbox" id="invoice_vendor_bill_id" placeholder="Select an old vendor bill" aria-expanded="false"></div>
                                                    <a role="button" class="o_dropdown_button" draggable="false"></a>
                                                </div>
                                            </div>
                                            <div class="o_field_many2one_extra"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="o_inner_group grid col-lg-6">
                                    <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                        <div class="o_cell flex-grow-1 flex-sm-grow-0 o_wrap_label w-100 text-break text-900" style=""><label class="o_form_label" for="invoice_date">Bill Date</label></div>
                                        <div class="o_cell flex-grow-1 flex-sm-grow-0" style="width: 100%;">
                                            <div name="invoice_date" class="o_field_widget o_field_date">
                                            <div class="o_datepicker" aria-atomic="true" data-target-input="#o_datepicker_29"><span type="hidden" id="o_datepicker_29">${formatDate(entries[0].LineItemWeekEnding)}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                        <div class="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900"><label class="o_form_label" for="date_2">Accounting Date</label></div>
                                        <div class="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style="width: 100%;">
                                            <div name="date" class="o_field_widget o_required_modifier o_field_date">
                                            <div class="o_datepicker" aria-atomic="true" data-target-input="#o_datepicker_30"><span type="hidden" id="o_datepicker_30">${formatDate(entries[0].LineItemWeekEnding)}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                        <div class="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900"><label class="o_form_label" for="payment_reference">Payment Reference<sup class="text-info p-1" data-tooltip-template="web.FieldTooltip" data-tooltip-info="{&quot;field&quot;:{&quot;help&quot;:&quot;The payment reference to set on journal items.&quot;}}" data-tooltip-touch-tap-to-show="true">?</sup></label></div>
                                        <div class="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style="width: 100%;">
                                        </div>
                                    </div>
                                    <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                        <div class="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900"><label class="o_form_label" for="partner_bank_id">Recipient Bank<sup class="text-info p-1" data-tooltip-template="web.FieldTooltip" data-tooltip-info="{&quot;field&quot;:{&quot;help&quot;:&quot;Bank Account Number to which the invoice will be paid. A Company bank account if this is a Customer Invoice or Vendor Credit Note, otherwise a Partner bank account number.&quot;}}" data-tooltip-touch-tap-to-show="true">?</sup></label></div>
                                        <div class="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style="width: 100%;">
                                            <div name="partner_bank_id" class="o_field_widget o_field_many2one">
                                            <div class="o_field_many2one_selection">
                                                <div>
                                                    <div class="o-autocomplete dropdown"><span type="text" class="o-autocomplete--input o_input" autocomplete="off" role="combobox" aria-autocomplete="list" aria-haspopup="listbox" id="partner_bank_id" placeholder="" aria-expanded="false"></div>
                                                    <a role="button" class="o_dropdown_button" draggable="false"></a>
                                                </div>
                                            </div>
                                            <div class="o_field_many2one_extra"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                        <div class="o_cell flex-grow-1 flex-sm-grow-0 o_wrap_label w-100 text-break text-900" style="">
                                            <div class="o_td_label"><label class="o_form_label" for="invoice_payment_term_id">Payment terms</label></div>
                                        </div>
                                        <div class="o_cell flex-grow-1 flex-sm-grow-0" style="width: 100%;">
                                            <div class="d-flex">
                                                <div>30 Days</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                        <div class="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900"><label class="o_form_label" for="x_studio_message_on_invoice">Message on Invoice</label></div>
                                        <div class="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style="width: 100%;">
                                            <textarea class="o_input" id="x_studio_message_on_invoice_${sow_contact_id}" rows="2" style="resize: none; height: 70px; border-top-width: 0px; border-bottom-width: 1px; padding: 1px 0px;" readonly></textarea>
                                        </div>
                                    </div>
                                    <div class="o_wrap_field d-flex d-sm-contents flex-column mb-3 mb-sm-0">
                                        <div class="o_cell o_wrap_label flex-grow-1 flex-sm-grow-0 w-100 text-break text-900"><label class="o_form_label" for="currency_id_1">Currency</label></div>
                                        <div class="o_cell o_wrap_input flex-grow-1 flex-sm-grow-0 text-break" style="width: 100%;">
                                            <div>USD</div>
                                        </div>
                                    </div>
                                </div>
                                </div>


                                <!-- COLUMNS -->

                                <div class="o_notebook d-flex w-100 horizontal flex-column">
                                    <div class="o_notebook_headers">
                                    <ul class="nav nav-tabs flex-row flex-nowrap">
                                        <li class="nav-item flex-nowrap cursor-pointer"><a class="nav-link active undefined" role="tab" tabindex="0" name="invoice_tab">Invoice Lines</a></li>
                                    </ul>
                                    </div>
                                    <div class="o_notebook_content tab-content">
                                    <div class="tab-pane active">
                                        <div name="invoice_line_ids" class="o_field_widget o_readonly_modifier o_field_section_and_note_one2many o_field_one2many">
                                            <div class="o_field_x2many o_field_x2many_list">
                                                <div class="o_x2m_control_panel">
                                                <div class="o_cp_pager" role="search">
                                                </div>
                                                </div>
                                                <div class="o_list_renderer o_renderer table-responsive" tabindex="-1">
                                                <table class="o_section_and_note_list_view o_list_table table table-sm table-hover position-relative mb-0 o_list_table_ungrouped table-striped" style="table-layout: fixed;">
                                                    <thead>
                                                        <tr>
                                                            <th data-tooltip-delay="1000" tabindex="-1" data-name="sequence" class="align-middle o_column_sortable position-relative cursor-pointer o_list_number_th o_handle_cell opacity-trigger-hover" style="min-width: 33px; width: 33px;"></th>
                                                            <th data-tooltip-delay="1000" tabindex="-1" data-name="product_id" class="align-middle o_column_sortable position-relative cursor-pointer o_many2one_barcode_cell opacity-trigger-hover" style="width: 234px;">
                                                            <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">Product</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                            </th>
                                                            <th data-tooltip-delay="1000" tabindex="-1" data-name="name" class="align-middle o_column_sortable position-relative cursor-pointer o_section_and_note_text_cell opacity-trigger-hover" style="width: 129px;">
                                                            <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">Label</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                            </th>
                                                            <th data-tooltip-delay="1000" tabindex="-1" data-name="analytic_distribution" class="align-middle o_column_sortable position-relative cursor-pointer o_analytic_distribution_cell opacity-trigger-hover" style="width: 213px;">
                                                            <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">Analytic</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                            </th>
                                                            <th data-tooltip-delay="1000" tabindex="-1" data-name="quantity" class="align-middle o_column_sortable position-relative cursor-pointer o_list_number_th opacity-trigger-hover" style="min-width: 92px; width: 92px;">
                                                            <div class="d-flex flex-row-reverse"><span class="d-block min-w-0 text-truncate flex-grow-1 o_list_number_th">Quantity</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                            </th>
                                                            <th data-tooltip-delay="1000" tabindex="-1" data-name="product_uom_id" class="align-middle o_column_sortable position-relative cursor-pointer opacity-trigger-hover" style="width: 60px;">
                                                            <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">UoM</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                            </th>
                                                            <th data-tooltip-delay="1000" tabindex="-1" data-name="price_unit" class="align-middle o_column_sortable position-relative cursor-pointer o_list_number_th opacity-trigger-hover" style="min-width: 92px; width: 92px;">
                                                            <div class="d-flex flex-row-reverse"><span class="d-block min-w-0 text-truncate flex-grow-1 o_list_number_th">Price</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                            </th>
                                                            <th data-tooltip-delay="1000" tabindex="-1" data-name="tax_ids" class="align-middle cursor-default o_many2many_tags_cell opacity-trigger-hover" style="width: 39px;">
                                                            <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">Taxes</span><i class="d-none fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                            </th>
                                                            <th data-tooltip-delay="1000" tabindex="-1" data-name="price_subtotal" class="align-middle o_column_sortable position-relative cursor-pointer o_list_number_th opacity-trigger-hover" style="min-width: 104px; width: 104px;">
                                                            <div class="d-flex flex-row-reverse"><span class="d-block min-w-0 text-truncate flex-grow-1 o_list_number_th">Subtotal</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                                            </th>
                                                            <th class="o_list_controller o_list_actions_header position-static" style="width: 32px; min-width: 32px">
                                                            <div class="o-dropdown dropdown o_optional_columns_dropdown border-top-0 text-center o-dropdown--no-caret"><button class="dropdown-toggle btn p-0" tabindex="-1" aria-expanded="false"><i class="o_optional_columns_dropdown_toggle oi oi-fw oi-settings-adjust"></i></button></div>
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody class="ui-sortable">`

        // LOOP THROUGH THE DATA AND ADD ROWS TO THE GRID / TABLE. 
        entries.forEach(entry => {  

            if (!entry.RateType || entry.RateType === false) {
                alert(`The entry for ${entry.WorkOrderName} does not contain a RateType for ${entry.ContractorName}. Skipping this export for now.`);
                return; // Skip processing this entry
            }

            tableHTML += `
                <tr class="o_data_row o_is_line_section" data-id="datapoint_304">
                    <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_handle_cell" data-tooltip-delay="1000" tabindex="-1" name="sequence">
                    <div name="sequence" class="o_field_widget o_field_handle"><span class="o_row_handle fa fa-sort ui-sortable-handle"></span></div>
                    </td>
                    <td class="o_data_cell cursor-pointer o_field_cell o_list_char o_section_and_note_text_cell" data-tooltip-delay="1000" tabindex="-1" name="name" colspan="8" data-tooltip="${entry.ContractorName}${entry.ContractorNumber}">
                    <div name="name" class="o_field_widget o_field_section_and_note_text o_field_text"><span>${entry.ContractorName}</span></div>
                    </td>
                    <td tabindex="-1"></td>
                </tr>

                <!-- Standard Hours -->

                <tr class="o_data_row o_is_product" data-id="datapoint_306">
                    <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_handle_cell" data-tooltip-delay="1000" tabindex="-1" name="sequence">
                    <div name="sequence" class="o_field_widget o_field_handle"><span class="o_row_handle fa fa-sort ui-sortable-handle"></span></div>
                    </td>
                    <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one o_many2one_barcode_cell" data-tooltip-delay="1000" tabindex="-1" name="product_id" data-tooltip="[Engineering Services] Contract Services">
                    <div name="product_id" class="o_field_widget o_field_many2one_barcode"><a class="o_form_uri"><span>[Engineering Services] Contract Services</span></a></div>
                    </td>
                    <td class="o_data_cell cursor-pointer o_field_cell o_list_char o_section_and_note_text_cell" data-tooltip-delay="1000" tabindex="-1" name="name" data-tooltip="${entry.ContractorName} - ST W.E. ${entry.WeekEnding}">
                    <div name="name" class="o_field_widget o_field_section_and_note_text o_field_text"><span>W.E. ${entry.LineItemWeekEnding} - Standard</span>
                    </div>
                    </td>
                    <td class="o_data_cell cursor-pointer o_field_cell o_analytic_distribution_cell" data-tooltip-delay="1000" tabindex="-1" name="analytic_distribution">
                    
                    <div name="analytic_distribution" class="o_field_widget o_field_analytic_distribution">
                        <div class="o_field_tags d-inline-flex flex-wrap mw-100">
                            <span tabindex="-1" class="o_tag_color_4 badge rounded-pill o_tag d-inline-flex align-items-center mw-100" data-color="4" title="${entry.WorkOrderName}">
                                <div class="o_tag_badge_text">${entry.AccountName}</div>
                            </span>
                        </div>
                    </div>
                        
                    </td>
                    <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="quantity">${entry.ApprovedData.Standard.Hours}</td>
                    <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one" data-tooltip-delay="1000" tabindex="-1" name="product_uom_id" data-tooltip="Hours">Hours</td>
                    <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="price_unit">${entry.ApprovedData.Standard.Rate}</td>
                    <td class="o_data_cell cursor-pointer o_field_cell o_many2many_tags_cell" data-tooltip-delay="1000" tabindex="-1" name="tax_ids" data-tooltip="No records">
                    <div name="tax_ids" class="o_field_widget o_field_many2many_tags">
                        <div class="o_field_tags d-inline-flex flex-wrap"></div>
                    </div>
                    </td>
                    <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_readonly_modifier" data-tooltip-delay="1000" tabindex="-1" name="price_subtotal">$&nbsp;${entry.ApprovedData.Standard.Total}</td>
                    <td tabindex="-1"></td>
                </tr>`

            if (!entry.RateType.toLowerCase().includes('fix')) {
                
                tableHTML += `

                    <!-- Travel Hours -->

                    <tr class="o_data_row o_is_product" data-id="datapoint_308">
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_handle_cell" data-tooltip-delay="1000" tabindex="-1" name="sequence">
                        <div name="sequence" class="o_field_widget o_field_handle"><span class="o_row_handle fa fa-sort ui-sortable-handle"></span></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one o_many2one_barcode_cell" data-tooltip-delay="1000" tabindex="-1" name="product_id" data-tooltip="[Engineering Services] Contract Services">
                        <div name="product_id" class="o_field_widget o_field_many2one_barcode"><a class="o_form_uri"><span>[Engineering Services] Contract Services</span></a></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_char o_section_and_note_text_cell" data-tooltip-delay="1000" tabindex="-1" name="name" data-tooltip="W.E. ${entry.LineItemWeekEnding} - Travel">
                        <div name="name" class="o_field_widget o_field_section_and_note_text o_field_text"><span>W.E. ${entry.LineItemWeekEnding} - Travel</span>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_analytic_distribution_cell" data-tooltip-delay="1000" tabindex="-1" name="analytic_distribution">
                        
                        <div name="analytic_distribution" class="o_field_widget o_field_analytic_distribution">
                            <div class="o_field_tags d-inline-flex flex-wrap mw-100">
                                <span tabindex="-1" class="o_tag_color_4 badge rounded-pill o_tag d-inline-flex align-items-center mw-100" data-color="4" title="${entry.WorkOrderName}">
                                    <div class="o_tag_badge_text">${entry.AccountName}</div>
                                </span>
                            </div>
                        </div>
                        
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="quantity">${entry.ApprovedData.Travel.Hours}</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one" data-tooltip-delay="1000" tabindex="-1" name="product_uom_id" data-tooltip="Hours">Hours</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="price_unit">${entry.ApprovedData.Travel.Rate}</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_many2many_tags_cell" data-tooltip-delay="1000" tabindex="-1" name="tax_ids" data-tooltip="No records">
                        <div name="tax_ids" class="o_field_widget o_field_many2many_tags">
                            <div class="o_field_tags d-inline-flex flex-wrap"></div>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_readonly_modifier" data-tooltip-delay="1000" tabindex="-1" name="price_subtotal">$&nbsp;${entry.ApprovedData.Travel.Total}</td>
                        <td tabindex="-1"></td>
                    </tr>

                    <!-- OT Hours -->

                    <tr class="o_data_row o_is_product" data-id="datapoint_308">
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_handle_cell" data-tooltip-delay="1000" tabindex="-1" name="sequence">
                        <div name="sequence" class="o_field_widget o_field_handle"><span class="o_row_handle fa fa-sort ui-sortable-handle"></span></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one o_many2one_barcode_cell" data-tooltip-delay="1000" tabindex="-1" name="product_id" data-tooltip="[Engineering Services] Contract Services">
                        <div name="product_id" class="o_field_widget o_field_many2one_barcode"><a class="o_form_uri"><span>[Engineering Services] Contract Services</span></a></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_char o_section_and_note_text_cell" data-tooltip-delay="1000" tabindex="-1" name="name" data-tooltip="W.E. ${entry.LineItemWeekEnding} - Over Time">
                        <div name="name" class="o_field_widget o_field_section_and_note_text o_field_text"><span>W.E. ${entry.LineItemWeekEnding} - Over Time</span>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_analytic_distribution_cell" data-tooltip-delay="1000" tabindex="-1" name="analytic_distribution">
                        
                        <div name="analytic_distribution" class="o_field_widget o_field_analytic_distribution">
                            <div class="o_field_tags d-inline-flex flex-wrap mw-100">
                                <span tabindex="-1" class="o_tag_color_4 badge rounded-pill o_tag d-inline-flex align-items-center mw-100" data-color="4" title="${entry.WorkOrderName}">
                                    <div class="o_tag_badge_text">${entry.AccountName}</div>
                                </span>
                            </div>
                        </div>
                        
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="quantity">${entry.ApprovedData.OT.Hours}</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one" data-tooltip-delay="1000" tabindex="-1" name="product_uom_id" data-tooltip="Hours">Hours</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="price_unit">${entry.ApprovedData.OT.Rate}</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_many2many_tags_cell" data-tooltip-delay="1000" tabindex="-1" name="tax_ids" data-tooltip="No records">
                        <div name="tax_ids" class="o_field_widget o_field_many2many_tags">
                            <div class="o_field_tags d-inline-flex flex-wrap"></div>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_readonly_modifier" data-tooltip-delay="1000" tabindex="-1" name="price_subtotal">$&nbsp;${entry.ApprovedData.OT.Total}</td>
                        <td tabindex="-1"></td>
                    </tr>

                    <!-- DT Hours -->

                    <tr class="o_data_row o_is_product" data-id="datapoint_310">
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_handle_cell" data-tooltip-delay="1000" tabindex="-1" name="sequence">
                        <div name="sequence" class="o_field_widget o_field_handle"><span class="o_row_handle fa fa-sort ui-sortable-handle"></span></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one o_many2one_barcode_cell" data-tooltip-delay="1000" tabindex="-1" name="product_id" data-tooltip="[Engineering Services] Contract Services">
                        <div name="product_id" class="o_field_widget o_field_many2one_barcode"><a class="o_form_uri"><span>[Engineering Services] Contract Services</span></a></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_char o_section_and_note_text_cell" data-tooltip-delay="1000" tabindex="-1" name="name" data-tooltip="W.E. ${entry.LineItemWeekEnding} - Double Time">
                        <div name="name" class="o_field_widget o_field_section_and_note_text o_field_text"><span>W.E. ${entry.LineItemWeekEnding} - Double Time</span>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_analytic_distribution_cell" data-tooltip-delay="1000" tabindex="-1" name="analytic_distribution">
                        
                        <div name="analytic_distribution" class="o_field_widget o_field_analytic_distribution">
                            <div class="o_field_tags d-inline-flex flex-wrap mw-100">
                                <span tabindex="-1" class="o_tag_color_4 badge rounded-pill o_tag d-inline-flex align-items-center mw-100" data-color="4" title="${entry.WorkOrderName}">
                                    <div class="o_tag_badge_text">${entry.AccountName}</div>
                                </span>
                            </div>
                        </div>
                        
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="quantity">${entry.ApprovedData.DT.Hours}</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one" data-tooltip-delay="1000" tabindex="-1" name="product_uom_id" data-tooltip="Hours">Hours</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="price_unit">${entry.ApprovedData.DT.Rate}</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_many2many_tags_cell" data-tooltip-delay="1000" tabindex="-1" name="tax_ids" data-tooltip="No records">
                        <div name="tax_ids" class="o_field_widget o_field_many2many_tags">
                            <div class="o_field_tags d-inline-flex flex-wrap"></div>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_readonly_modifier" data-tooltip-delay="1000" tabindex="-1" name="price_subtotal">$&nbsp;${entry.ApprovedData.DT.Total}</td>
                        <td tabindex="-1"></td>
                    </tr>

                    <!-- Holiday Hours -->
                    
                    <tr class="o_data_row o_is_product" data-id="datapoint_308">
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_handle_cell" data-tooltip-delay="1000" tabindex="-1" name="sequence">
                        <div name="sequence" class="o_field_widget o_field_handle"><span class="o_row_handle fa fa-sort ui-sortable-handle"></span></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one o_many2one_barcode_cell" data-tooltip-delay="1000" tabindex="-1" name="product_id" data-tooltip="[Engineering Services] Contract Services">
                        <div name="product_id" class="o_field_widget o_field_many2one_barcode"><a class="o_form_uri"><span>[Engineering Services] Contract Services</span></a></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_char o_section_and_note_text_cell" data-tooltip-delay="1000" tabindex="-1" name="name" data-tooltip="W.E. ${entry.LineItemWeekEnding} - Holiday Time">
                        <div name="name" class="o_field_widget o_field_section_and_note_text o_field_text"><span>W.E. ${entry.LineItemWeekEnding} - Holiday Time</span>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_analytic_distribution_cell" data-tooltip-delay="1000" tabindex="-1" name="analytic_distribution">
                        
                        <div name="analytic_distribution" class="o_field_widget o_field_analytic_distribution">
                            <div class="o_field_tags d-inline-flex flex-wrap mw-100">
                                <span tabindex="-1" class="o_tag_color_4 badge rounded-pill o_tag d-inline-flex align-items-center mw-100" data-color="4" title="${entry.WorkOrderName}">
                                    <div class="o_tag_badge_text">${entry.AccountName}</div>
                                </span>
                            </div>
                        </div>

                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="quantity">${entry.ApprovedData.Holiday.Hours}</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one" data-tooltip-delay="1000" tabindex="-1" name="product_uom_id" data-tooltip="Hours">Hours</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="price_unit">${entry.ApprovedData.Holiday.Rate}</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_many2many_tags_cell" data-tooltip-delay="1000" tabindex="-1" name="tax_ids" data-tooltip="No records">
                        <div name="tax_ids" class="o_field_widget o_field_many2many_tags">
                            <div class="o_field_tags d-inline-flex flex-wrap"></div>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_readonly_modifier" data-tooltip-delay="1000" tabindex="-1" name="price_subtotal">$&nbsp;${entry.ApprovedData.Holiday.Total}</td>
                        <td tabindex="-1"></td>
                    </tr>`
                }

            if (entry.Expenses != null){

                // ADD EXPENSES
                tableHTML += `                    
                    <tr class="o_data_row o_is_product" data-id="datapoint_308">
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_handle_cell" data-tooltip-delay="1000" tabindex="-1" name="sequence">
                        <div name="sequence" class="o_field_widget o_field_handle"><span class="o_row_handle fa fa-sort ui-sortable-handle"></span></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one o_many2one_barcode_cell" data-tooltip-delay="1000" tabindex="-1" name="product_id" data-tooltip="[Expenses] All">
                        <div name="product_id" class="o_field_widget o_field_many2one_barcode"><a class="o_form_uri"><span>[Expenses] All</span></a></div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_char o_section_and_note_text_cell" data-tooltip-delay="1000" tabindex="-1" name="name" data-tooltip="[Expenses] All\nPer Diem, Fuel, Hotel, Misc. items.">
                        <div name="name" class="o_field_widget o_field_section_and_note_text o_field_text"><span>[Expenses] All\nPer Diem, Fuel, Hotel, Misc. items.</span>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_analytic_distribution_cell" data-tooltip-delay="1000" tabindex="-1" name="analytic_distribution">
                        <div name="analytic_distribution" class="o_field_widget o_field_analytic_distribution">
                            <div class="o_field_tags d-inline-flex flex-wrap mw-100">
                                <span tabindex="-1">
                                    <div></div>
                                </span>
                            </div>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="quantity">1.00</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one" data-tooltip-delay="1000" tabindex="-1" name="product_uom_id" data-tooltip="Unit">Unit</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number" data-tooltip-delay="1000" tabindex="-1" name="price_unit">${parseFloat(entry.Expenses.ApprovedTotalExpenses).toLocaleString('en-US', { minimumFractionDigits: 3, maximumFractionDigits: 3 })}</td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_many2many_tags_cell" data-tooltip-delay="1000" tabindex="-1" name="tax_ids" data-tooltip="No records">
                        <div name="tax_ids" class="o_field_widget o_field_many2many_tags">
                            <div class="o_field_tags d-inline-flex flex-wrap"></div>
                        </div>
                        </td>
                        <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_readonly_modifier" data-tooltip-delay="1000" tabindex="-1" name="price_subtotal">$&nbsp;${parseFloat(entry.Expenses.ApprovedTotalExpenses).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
                        <td tabindex="-1"></td>
                    </tr>
                `;
            }
        });

        // DONE WITH LOOPING THROUGH THE DATA


        // CLOSE THE DIV TAGS IN THE TABLE

        tableHTML += `
                                                                    
                                                    </tbody>
                                                    <tfoot class="o_list_footer cursor-default">
                                                        <tr>
                                                            <td></td>
                                                            <td></td>
                                                            <td></td>
                                                            <td></td>
                                                            <td></td>
                                                            <td></td>
                                                            <td></td>
                                                            <td></td>
                                                            <td></td>
                                                            <td></td>
                                                        </tr>
                                                    </tfoot>
                                                </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        `;
        
        tableWrapper.innerHTML = tableHTML;

        container.appendChild(tableWrapper);

        // ADD THE MESSAGE ON INVOICE SECTION NOT THAT TABLE IS CREATED
        const textarea = document.getElementById(`x_studio_message_on_invoice_${sow_contact_id}`);
        const firstEntry = entries[0];  // Assuming all entries have the same PO and date range
        const poSection = createCustomerPOSection(firstEntry.LineItemWeekEnding);
        textarea.value = poSection;

    };


    function updatePagination() {
        const tablesContainer = document.getElementById('tables-container');
        const tableWrappers = tablesContainer.querySelectorAll('.table-wrapper');

        tableWrappers.forEach((wrapper, index) => {
            if (index >= (currentPage - 1) * tablesPerPage && index < currentPage * tablesPerPage) {
                wrapper.classList.add('active');
            } else {
                wrapper.classList.remove('active');
            }
        });

        document.getElementById('page-info').textContent = `${currentPage} / ${totalPages}`;
        document.getElementById('prev-page').disabled = currentPage === 1;
        document.getElementById('next-page').disabled = currentPage === totalPages;
    }

    document.getElementById('prev-page').addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            updatePagination();
        }
    });

    document.getElementById('next-page').addEventListener('click', () => {
        if (currentPage < totalPages) {
            currentPage++;
            updatePagination();
        }
    });

    function getStartDate(endDateString) {
        const endDate = new Date(endDateString.substring(0, 4), endDateString.substring(4, 6) - 1, endDateString.substring(6, 8));
        const startDate = new Date(endDate);
        startDate.setDate(endDate.getDate() - 6);
        const year = startDate.getFullYear();
        const month = ('0' + (startDate.getMonth() + 1)).slice(-2);
        const day = ('0' + startDate.getDate()).slice(-2);
        return `${month}/${day}/${year}`;
    }

    function formatDate(dateString) {
        const year = dateString.substring(0, 4);
        const month = dateString.substring(4, 6);
        const day = dateString.substring(6, 8);
        return `${month}/${day}/${year}`;
    }

    function createCustomerPOSection(weekEnding) {
        const endDate = formatDate(weekEnding);
        const startDate = getStartDate(weekEnding);

        return `Start Date: ${startDate}\nEnd Date: ${endDate}`;
    }

    function getFormattedDate() {
        const today = new Date();
        const month = String(today.getMonth() + 1).padStart(2, '0'); // Add 1 because months are zero-indexed
        const day = String(today.getDate()).padStart(2, '0');
        const year = today.getFullYear();
        return `${month}/${day}/${year}`;
    }

</script>
{% endblock %}

{% block content %}
<body class="o_web_client">
    <h2 class="text-center" style="display: none; font-size: 21px;" id="waiting_message">Thank you for being patient. This process is expected to take a few minutes.</h2>
    <!-- <div id="loading"></div> -->
    <div id="progress-container" style="display: none; width: 90%; margin-top: 50px;"><!-- Container to control visibility -->
        <div id="progress-bar" class="progress-bar">
            <span id="progress-label">0%</span>
        </div>
    </div>
    <div id="paging_tabs" style="display: none;">
        <div class="pagination-controls">
            <button id="prev-page">Previous</button>
            <span id="page-info">1 / 1</span>
            <button id="next-page">Next</button>
        </div>
        <div class="pagination-controls">
            <button id="export_button">Looks good!</button>
        </div>
    </div>
    <div id="loading"></div>
    <div id="tables-container" class="table-container"></div>
</body>
{% endblock %}