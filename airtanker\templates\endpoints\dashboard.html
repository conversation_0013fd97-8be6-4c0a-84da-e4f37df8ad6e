{% extends 'base.html' %}

{% block styles %}
<link href="https://cdn.datatables.net/rowgroup/1.5.0/css/rowGroup.dataTables.min.css" rel="stylesheet">
<style>
    #workOrderTable tr th,
    .odd td, 
    .even td {
        text-align: center;
        vertical-align: middle;
    }
    .dtrg-group td {
        text-align: left;
        background-color: lightgray !important;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
<script src="{{ url_for('static', filename='js/gauge.js') }}"></script>
<script src="https://cdn.datatables.net/rowgroup/1.5.0/js/dataTables.rowGroup.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', () => setMostRecentSunday());

    function setMostRecentSunday() {
        const selectedWeekEnding = '{{ selected_week_ending }}';
        const formattedDate = selectedWeekEnding !== 'False' 
            ? selectedWeekEnding 
            : new Date(new Date().setDate(new Date().getDate() - new Date().getDay())).toISOString().split('T')[0];
        
        document.getElementById('weekEndingPicker').value = formattedDate;
    }

    function setDateField(value) {
        const date = new Date(value);
        if (date.getDay() === 6) {
            document.getElementById('weekEndingPicker').value = date.toISOString().split('T')[0];
        } else {
            alert('Please select a Sunday.');
            setMostRecentSunday();
        }
    }
</script>
{% endblock %}

{% block content %}
<div class="container">
    <h3 class="text-center">Work Order Dashboard</h3>
    
    <!-- Date Picker Section -->
    <div class="date-picker-container text-center mb-3">
        <label for="weekEndingPicker">Select a Week Ending:</label>
        <br>
        <input type="date" id="weekEndingPicker" class="date-picker" onchange="setDateField(this.value)">
        <div class="mt-3">
            <button onclick="fetchData()" class="btn btn-primary">Refresh Data</button>
        </div>
    </div>

    <!-- Finance Section -->
    <div id="financeContainer" class="mb-4">
        <div class="row">
            <!-- Invoices Table -->
            <div class="col">
                <h2 id="invoiceTitle" class="my-2">Invoices</h2>
                <table id="invoiceTable" class="table table-sm w-100">
                    <thead class="text-center align-middle">
                        <tr>
                            <th>Invoice</th>
                            <th>Partner</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody class="text-center align-middle"></tbody>
                    <tfoot>
                        <tr>
                            <th colspan="2" class="text-end">Total:</th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- Totals Summary -->
            <div class="col-3 px-5">
                <h2 class="my-2">Totals</h2>
                <div class="d-flex flex-column gap-2">
                    <div class="d-flex justify-content-between">
                        <p class="mb-0">Invoiced:</p>
                        <span id="spanTotalInvoiced"></span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <p class="mb-0">Billed:</p>
                        <span id="spanTotalBilled"></span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <p class="mb-0">Net:</p>
                        <span id="spanNetTotal"></span>
                    </div>
                    <div class="text-center mt-3">
                        <canvas id="gauge"></canvas>
                        <p>Internal Resource Utilization: <span class="fw-semibold" id="utilization"></span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bills Table -->
        <div class="row">
            <h2 id="billsTitle" class="my-2">Vendor Bills</h2>
            <table id="billsTable" class="table table-sm w-100">
                <thead class="text-center align-middle">
                    <tr>
                        <th>Bill</th>
                        <th>Partner</th>
                        <th>Amount</th>
                    </tr>
                </thead>
                <tbody class="text-center align-middle"></tbody>
                <tfoot>
                    <tr>
                        <th colspan="2" class="text-end">Total:</th>
                        <th></th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>

    <!-- Work Orders Section -->
    <div id="workOrderContainer">
        <h2 class="my-2">Work Orders</h2>
        <table id="workOrderTable" class="table table-bordered table-sm align-middle w-100">
            <thead class="text-center">
                <tr>
                    <th colspan="6">Work Orders</th>
                    <th colspan="3">Status</th>
                </tr>
                <tr class="table-light">
                    <th>Start Date</th>
                    <th>Display Name</th>
                    <th>Employee</th>
                    <th>Site Name</th>
                    <th>Project</th>
                    <th>Comments</th>
                    <th>Invoice Ref.</th>
                    <th>Bill Ref.</th>
                    <th class="bg-success text-light">Approval Status</th>
                </tr>
            </thead>
            <tbody class="text-center"></tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block modal %}
<div id="spinnerModal" class="modal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-0">Processing...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
