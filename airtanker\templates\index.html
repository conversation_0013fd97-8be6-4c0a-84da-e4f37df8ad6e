
<link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='gantt.css') }}"  />
<script type="text/javascript" charset="utf8" src="{{ url_for('static', filename='highcharts-gantt.js') }}"></script>
<script type="text/javascript" charset="utf8" src="{{ url_for('static', filename='draggable-points.js') }}"></script>
<script type="text/javascript" charset="utf8" src="{{ url_for('static', filename='accessibility.js') }}"></script>

<body>
    <div class="main-container">

        <!-- Main chart here. Created in Javascript -->
        <div id="container"></div>

        <!-- Buttons to add or remove tasks -->
        <div id="buttonGroup" class="button-row">
            <button id="btnShowDialog">
                <i class="fa fa-plus"></i>
                Add task
            </button>
            <button id="btnRemoveSelected" disabled="disabled">
                <i class="fa fa-remove"></i>
                Remove selected
            </button>
        </div>
        

        <!-- Popup to add Work Order -->
        <div id="addTaskDialog" class="hidden overlay">
            <label>Employee: 
                <select id="selectEmployee"></select>
            </label><br>
            <label>Work Order: 
                <select id="selectWorkOrder"></select>
            </label><br>
            <button id="btnAddTask">Add</button>
            <button id="btnCancelAddTask">Cancel</button>
        </div>


        <!-- button to add employee -->
        <button id="btnAddEmployee">+ Add Employee</button>
        

        <!-- Filter by year. Not working yet -->
        <label>Filter by Year: 
            <select id="filterYear">
                <option value="">All</option>
                <option value="2023">2023</option>
                <option value="2024">2024</option>
            </select>
        </label>
    </div>
    <script type="text/javascript" charset="utf8" src="{{ url_for('static', filename='gantt.js') }}"></script>
</body>
