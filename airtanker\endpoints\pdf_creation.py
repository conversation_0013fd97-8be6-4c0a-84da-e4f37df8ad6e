from openpyxl import load_workbook
from main import airtanker_app
import datetime
from services.DatabaseService import DatabaseService
import io  # For in-memory file handling
from PyPDF2 import PdfMerger
import subprocess
import platform
from flask import url_for, jsonify, send_file
from endpoints.progress import update_progress
import os
import threading
import time
import zipfile


pdf_results = {}

@airtanker_app.route('/create_single_pdf/<int:timesheet_id>/<hours_source>', methods=['GET', 'POST'])
def create_single_pdf(timesheet_id, hours_source):
    # Your logic here
    try:
        database_service = DatabaseService()
        database_service.connect()

        query = """
            SELECT *
            FROM [AirTanker-PROD].[dbo].[vw_EmployeeDetailsWeekly]
            WHERE [TimeSheetID] = ?
        """

        weekly_data = database_service.execute_query(query, (timesheet_id))[0]

        contractor_name = f'{weekly_data["FirstName"]} {weekly_data["LastName"]}'
        week_ending = weekly_data["WeekEnding"]
        work_order_number = weekly_data["WorkOrderNumber"]

        timesheet_entry = {
            "TimeSheetID": timesheet_id,
            "ApprovedHoursSource": hours_source
        }

        # Get all the timesheet entries
        approved_hours_list = database_service.get_employee_reported_hours(timesheet_entry)
        approved_hours_grouped = {}

        # Group the approved hours by the TaskName column
        for approved_hours in approved_hours_list:
            if approved_hours['TaskName'] in approved_hours_grouped:
                approved_hours_grouped[approved_hours['TaskName']].append(approved_hours)
            else:
                approved_hours_grouped[approved_hours['TaskName']] = []
                approved_hours_grouped[approved_hours['TaskName']].append(approved_hours)                

        # Create the workbook based off the excel sheet template in the assets folder

        # Get the base directory of your application
        base_dir = os.path.dirname(os.path.abspath(__file__))

        # Construct the correct path to the Excel template file
        excel_template_path = os.path.join(base_dir, '..', 'static', 'assets', 'AtomTech_Timesheet_Template.xlsx')
        airtanker_app.logger.debug("Loading template Excel Workbook: %s", excel_template_path)

        wb = load_workbook(filename=excel_template_path)
        ws = wb.active  # Get the active sheet (assuming data goes in the first sheet)

        ws.cell(row=4, column=9).value = contractor_name
        ws.cell(row=4, column=12).value = week_ending

        row_start = 10
        hours_column_start = 5

        task_name_column = 3
        work_order_number_column = 2



        # Write to the template now with each key in the approved_hours_grouped to be a new row in the sheet.
        for task_name, hours_entries in approved_hours_grouped.items():

            # Add the task and the work order number to the row
            ws.cell(row=row_start, column=task_name_column).value = task_name
            ws.cell(row=row_start, column=work_order_number_column).value = work_order_number

            # Loop through the hours now, get the dates and add them to the each column
            # Loop through the hours entries
            for hours in hours_entries:
                curr_date = hours["Date"]

                if isinstance(curr_date, str):
                    curr_date = datetime.datetime.strptime(curr_date, "%Y-%m-%d")

                # Get the day of the week (0=Monday, 6=Sunday)
                day_of_week = curr_date.weekday()

                # Calculate the column based on day of the week (offset by starting column)
                column_to_write = hours_column_start + day_of_week

                # Write the hours to the corresponding column
                ws.cell(row=row_start, column=column_to_write).value = hours["Hours"]

            # Increment row for the next entry
            row_start += 1  # Move to the next row for the next set of hours

        # Save the workbook to a temporary file
        temp_excel_path = os.path.join('output', 'staging', f"{contractor_name.replace(', ', '_').replace(' ', '_')}_{week_ending.strftime('%m-%d-%Y')}.xlsx")
        wb.save(temp_excel_path)
        pdf_path = temp_excel_path.replace('.xlsx', '.pdf')

        # Convert the workbook to a PDF
        pdf_path = convert_excel_to_pdf(temp_excel_path, pdf_path)
        if pdf_path:
            url = url_for('download_pdf', filename=os.path.join('staging', os.path.basename(pdf_path)), _external=False)
            delete_files([pdf_path], delay=60)  # Adjust the delay as needed
            return jsonify({"pdf_path": url})

    except Exception as e:
        airtanker_app.logger.error(e)
        return jsonify({"error": str(e)}), 500
    finally:
        database_service.disconnect()


def create_pdfs(entries, task_id):
    pdfs = {}

    total_entries = sum(len(value_list) for value_list in entries.values())
    progress = 100 / (total_entries * 2.5)  # Adjust denominator if needed
    total_progress = 0

    try:
        database_service = DatabaseService()
        database_service.connect()

        # Loop through each project
        for key, value_list in entries.items():

            project_pdf_files = []
            project_name = entries[key][0]['ProjectName']

            week_ending = entries[key][0]["WeekEnding"]
            # Extract year, month, and day as separate strings
            year = week_ending[:4]
            month = week_ending[4:6]
            day = week_ending[6:]

            # Format the date string in desired format (MM/DD/YYYY)
            week_ending = f"{month}/{day}/{year}"

            # loop through each entry in associated with the project
            for index, entry in enumerate(value_list):

                total_progress += progress
                update_progress(task_id=task_id, progress=total_progress)

                # get the contractor name
                contractor_name = entry["ContractorName"]
                work_order_number = entry["WorkOrderName"]

                query = """
                    SELECT TimesheetID, ApprovedHoursSource
                    FROM dbo.Timesheets
                    WHERE TimesheetID = ?
                """

                timesheet_entry = database_service.execute_query(query, (entry['TimesheetID']))[0]
                timesheet_entry['TimeSheetID'] = entry['TimesheetID']

                # Get all the timesheet entries
                approved_hours_list = database_service.get_employee_reported_hours(timesheet_entry)
                approved_hours_grouped = {}

                # Group the approved hours by the TaskName column
                for approved_hours in approved_hours_list:
                    if approved_hours['TaskName'] in approved_hours_grouped:
                        approved_hours_grouped[approved_hours['TaskName']].append(approved_hours)
                    else:
                        approved_hours_grouped[approved_hours['TaskName']] = []
                        approved_hours_grouped[approved_hours['TaskName']].append(approved_hours)                

                # Create the workbook based off the excel sheet template in the assets folder

                # Get the base directory of your application
                base_dir = os.path.dirname(os.path.abspath(__file__))

                # Construct the correct path to the Excel template file
                excel_template_path = os.path.join(base_dir, '..', 'static', 'assets', 'AtomTech_Timesheet_Template.xlsx')

                wb = load_workbook(filename=excel_template_path)
                ws = wb.active  # Get the active sheet (assuming data goes in the first sheet)

                ws.cell(row=4, column=9).value = contractor_name
                ws.cell(row=4, column=12).value = week_ending

                row_start = 10
                hours_column_start = 5

                task_name_column = 3
                work_order_number_column = 2



                # Write to the template now with each key in the approved_hours_grouped to be a new row in the sheet.
                for task_name, hours_entries in approved_hours_grouped.items():

                    # Add the task and the work order number to the row
                    ws.cell(row=row_start, column=task_name_column).value = task_name
                    ws.cell(row=row_start, column=work_order_number_column).value = work_order_number

                    # Loop through the hours now, get the dates and add them to the each column
                    # Loop through the hours entries
                    for hours in hours_entries:
                        curr_date = hours["Date"]

                        if isinstance(curr_date, str):
                            curr_date = datetime.datetime.strptime(curr_date, "%Y-%m-%d")

                        # Get the day of the week (0=Monday, 6=Sunday)
                        day_of_week = curr_date.weekday()

                        # Calculate the column based on day of the week (offset by starting column)
                        column_to_write = hours_column_start + day_of_week

                        # Write the hours to the corresponding column
                        ws.cell(row=row_start, column=column_to_write).value = hours["Hours"]

                    # Increment row for the next entry
                    row_start += 1  # Move to the next row for the next set of hours

                # Save the workbook to a temporary file
                base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
                staging_path = os.path.join(base_dir, 'output', 'staging')
                temp_excel_path = os.path.join(staging_path, f"{contractor_name.replace(', ', '_').replace(' ', '_')}_{week_ending.replace('/', '-')}.xlsx")
                wb.save(temp_excel_path)
                pdf_path = temp_excel_path.replace('.xlsx', '.pdf')

                # Convert the workbook to a PDF
                pdf_path = convert_excel_to_pdf(temp_excel_path, pdf_path)
                if pdf_path:
                    project_pdf_files.append(pdf_path)

                total_progress += progress
                update_progress(task_id=task_id, progress=total_progress)

            merged_pdf_path = merge_pdfs(project_pdf_files, project_name, week_ending.replace('/', '-'))
            pdfs[project_name] = merged_pdf_path

            # Delete old PDF and Excel files
            delete_files_in_list(project_pdf_files)

    except Exception as e:
        airtanker_app.logger.error(e)
        update_progress(task_id=task_id, progress="Failed")
    finally:
        database_service.disconnect()

    pdf_results[task_id] = pdfs # pdf_results will look like this {task_id: {project_name1: pdf_filepath, project_name2: pdf_filepath2 } }
    update_progress(task_id=task_id, progress="Completed")

@airtanker_app.route('/download_pdfs/<task_id>', methods=['GET'])
def download_pdfs(task_id):
    airtanker_app.logger.info(f"Request received for task_id: {task_id}")
    
    # Retrieve the PDF paths stored for the given task_id
    pdf_paths = pdf_results.get(task_id)
    
    # If no PDFs are found for the given task_id, return a message indicating they are not ready
    if not pdf_paths:
        airtanker_app.logger.info(f"No PDFs found for task_id: {task_id}")
        return jsonify({"message": "PDFs not ready"}), 202

    # Prepare a list of download URLs
    pdf_urls = []
    file_paths_to_delete = []  # List of file paths to delete after export

    for project_name, pdf_path in pdf_paths.items():
        # Create a URL for each PDF
        url = url_for('download_pdf', filename=os.path.basename(pdf_path), _external=False)
        pdf_urls.append({'project_name': project_name, 'url': url})
        file_paths_to_delete.append(pdf_path)  # Add to delete list

    airtanker_app.logger.info(f"Returning PDF URLs for task_id: {task_id}")
    
    # Schedule the cleanup task to delete the files after a delay
    delete_files(file_paths_to_delete, delay=60)  # Adjust the delay as needed

    # Remove the key and its values from the dictionary
    if task_id in pdf_results:
        del pdf_results[task_id]

    return jsonify(pdf_urls)


@airtanker_app.route('/download_pdf/<path:filename>', methods=['GET'])
def download_pdf(filename):
    pdf_path = os.path.join('output', filename)
    if os.path.exists(pdf_path):
        return send_file(pdf_path, as_attachment=True)
    else: 
        return jsonify({"message": "PDF file not found"}), 404
    

@airtanker_app.route('/download_all_pdfs', methods=['GET'])
def download_all_pdfs():
    zip_buffer = io.BytesIO()
    file_paths = []

    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        for root, dirs, files in os.walk('output'):
            for file in files:
                if file.endswith('.pdf'):
                    file_path = os.path.join(root, file)
                    zip_file.write(file_path, os.path.relpath(file_path, 'output'))
                    file_paths.append(file_path)
    
    delete_files(file_paths, delay=60) 

    zip_buffer.seek(0)

    return send_file(zip_buffer, mimetype='application/zip', as_attachment=True, download_name='all_pdfs.zip')


def delete_files(file_paths, delay=60):
    """Delete files after a delay to ensure downloads are complete."""
    def delayed_delete():
        time.sleep(delay)  # Delay to ensure files are downloaded
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    airtanker_app.logger.debug(f"Deleted file: {file_path}")
                else:
                    airtanker_app.logger.info(f"File not found: {file_path}")
            except Exception as e:
                airtanker_app.logger.exception(f"Error deleting file {file_path}: {e}")

    threading.Thread(target=delayed_delete).start()


def delete_files_in_list(file_paths):
    for file_path in file_paths:
        try:
            os.remove(file_path)
        except OSError as e:
            airtanker_app.logger.exception(f"Error deleting file {file_path}: {e}")


def convert_excel_to_pdf(excel_path, pdf_filename):
    # Convert the relative path to an absolute path
    excel_full_path = os.path.abspath(excel_path)
    pdf_path = os.path.abspath(pdf_filename)

    try:
        if platform.system() == 'Windows':
            # Use subprocess to call libreoffice on Windows
            libreoffice_path = 'C:\\Program Files\\LibreOffice\\program\\soffice.exe'
            subprocess.run([libreoffice_path, '--headless', '--convert-to', 'pdf', '--outdir', os.path.dirname(pdf_path), excel_full_path], check=True)
        else:
            # Use subprocess to call libreoffice on Linux
            subprocess.run(['libreoffice', '--headless', '--convert-to', 'pdf', '--outdir', os.path.dirname(pdf_path), excel_full_path], check=True)
        
        if not os.path.exists(pdf_path):
            raise Exception(f"PDF file not created: {pdf_path}")
    except Exception as e:
        airtanker_app.logger.exception(f"Error converting Excel to PDF: {e}")
        pdf_path = None
    finally:
        try:
            os.remove(excel_full_path)
        except OSError as e:
            airtanker_app.logger.exception(f"Error deleting file {excel_full_path}: {e}")

    return pdf_path


def merge_pdfs(pdf_files, project_name, weekending):
    base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    output_path = os.path.join(base_dir, 'output')
    output_pdf_path = os.path.join(output_path, f"{project_name.replace('/', '-')}_{weekending}.pdf")
    output_pdf_path = os.path.abspath(output_pdf_path)
    merger = PdfMerger()

    for pdf_file in pdf_files:
        merger.append(pdf_file)

    merger.write(output_pdf_path)
    merger.close()
    return output_pdf_path
    


