{% extends 'base.html' %}

{% block styles %}
<!-- <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/faqs.css') }}"  /> -->
{% endblock %}

{% block scripts %}
<script type="text/javascript" charset="utf8" src="static/js/faqs.js"></script>
{% endblock %}

{% block content %}
<div class="container">
    <h2 class="text-center mt-2">Frequently Asked Questions</h2>
    <div id="divQuestions" class="col accordion mt-4">
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne"
                    aria-expanded="true" aria-controls="collapseOne">
                    How does this app work?
                </button>
            </h2>
            <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#divQuestions">
                <div class="accordion-body">
                    <p>To use this app, start by clicking "Start Import Process" in the navigation bar, guiding you through the steps to import data. Ensure you're logged into your Odoo account to import Work Orders, followed by uploading internal timesheets from ADP and contractors. Address any errors through prompts provided. The next phase involves uploading Customer Timesheets directly sent by the customer. Similar to previous steps, correct any discrepancies through the prompt after upload. Once data upload is complete, proceed to the <a href="/approvals">Approvals</a> page to approve weekly hours.</p>
                </div>                
            </div>
        </div>            
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo"
                    aria-expanded="true" aria-controls="collapseTwo">
                    How do I delete data entries?
                </button>
            </h2>
            <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#divQuestions">
                <div class="accordion-body">                    
                    <p>To delete data entries, the entire parsed file must be removed from the database. This action will erase all data associated with that file. For scenarios requiring the deletion of individual entries, especially when a file contains data for multiple employees, follow these steps:</p>
                    <ol>
                        <li>Navigate to <a href="/edit-files">Edit Files</a> and remove the desired file.</li>
                        <li>Open the excel sheet and delete the rows corresponding to the entries you wish to remove.</li>
                        <li>Save the modified file and reupload it to ensure only the desired data remains in the database.</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}