let internalSelectedFiles = [];
let customerSelectedFiles = [];

function showProgress(){
    $('#spinnerModal').modal({
    backdrop: 'static', // Prevent clicking outside the modal to close
        keyboard: false // Prevent closing the modal with keyboard ESC key
    });
}


// Customer Methods
function addFilesCustomer() {
    var files = document.getElementById('file-upload-left').files;
    for (var i = 0; i < files.length; i++) {
        customerSelectedFiles.push(files[i]);
    }
    //console.log(len(customerSelectedFiles))
    updateFileListCustomer();
    document.getElementById('file-upload-left').value = ''; // Clear the current selection
}

function updateFileListCustomer() {
    var output = document.getElementById('file-list-left');
    output.innerHTML = '';
    for (var i = 0; i < customerSelectedFiles.length; ++i) {
        output.innerHTML += '<li class="list-group-item">' +
                            '<i class="fas fa-file-alt"></i> ' +
                            customerSelectedFiles[i].name +
                            '<i class="fas fa-times" onclick="removeFileCustomer(' + i + ')"></i>' +
                            '</li>';
    }
    updateVisibilityOfProcessButton();
}

function removeFileCustomer(index) {
    customerSelectedFiles.splice(index, 1); // Remove the file from the array
    updateFileListCustomer(); // Update the list
}
                
    
// Internal
function addFilesInternal() {
    var files = document.getElementById('file-upload-right').files;
    for (var i = 0; i < files.length; i++) {
        internalSelectedFiles.push(files[i]);
    }
    updateFileListInternal();
    document.getElementById('file-upload-right').value = ''; // Clear the current selection
}

function updateFileListInternal() {
    var output = document.getElementById('file-list-right');
    output.innerHTML = '';
    for (var i = 0; i < internalSelectedFiles.length; ++i) {
        output.innerHTML += '<li class="list-group-item">' +
                            '<i class="fas fa-file-alt"></i> ' +
                            internalSelectedFiles[i].name +
                            '<i class="fas fa-times" onclick="removeFileInternal(' + i + ')"></i>' +
                            '</li>';
    }
    updateVisibilityOfProcessButton();
}

function removeFileInternal(index) {
    internalSelectedFiles.splice(index, 1); // Remove the file from the array
    updateFileListInternal(); // Update the list
}


// Show compare button
function updateVisibilityOfProcessButton() {
    // Check if both arrays have at least one file
    if (internalSelectedFiles.length > 0 && customerSelectedFiles.length > 0) {
        document.getElementById('process-files-btn').style.display = 'block'; // Show the button
    } else {
        document.getElementById('process-files-btn').style.display = 'none'; // Hide the button
    }
}    
        

// Compare button click
function processFiles() {
    $('#spinnerModal').modal({
        backdrop: 'static', // Prevent clicking outside the modal to close
        keyboard: false // Prevent closing the modal with keyboard ESC key
    });

    const formData = new FormData()

    internalSelectedFiles.forEach(file=> {
        formData.append(`internalFiles[]`, file);
    });

    customerSelectedFiles.forEach(file => {
        formData.append(`customerFiles[]`, file);
    });
    fetch('/ats', {
        method: 'POST',
        body: formData            
    })
    .then(response => {
        if (response.ok) {
            // Resetting the UI elements as needed
            document.getElementById('file-list-right').files = null;
            internalSelectedFiles = [];
            updateFileListInternal();
            document.getElementById('file-list-left').files = null;
            customerSelectedFiles = [];
            updateFileListCustomer();
            return response.json(); // This already parses the JSON response
        } else {
            throw new Error('Network response was not ok.');
        }
    })
    .then(data => {
        displayErrorMessages(data.messages);
    })
    .finally(() => {
        // fetchWeeklyData()
        $('#spinnerModal').modal('hide');
    })            
};

function displayErrorMessages(messages) {
    // Display error messages in a console-like area
    var consoleArea = document.getElementById('console');

    // Append each error message to the console area
    messages.forEach(function (message) {
        var errorDiv = document.createElement('div');
        errorDiv.className = 'error';
        errorDiv.textContent = message;
        consoleArea.appendChild(errorDiv);
    });

    // Scroll the console area to the bottom to show the latest messages
    consoleArea.scrollTop = consoleArea.scrollHeight;
}

function handleBrowseClickLeft(event) {
    event.stopPropagation(); // Stop event propagation to prevent double triggering
    document.getElementById('file-upload-left').click();
}
function handleBrowseClickRight(event) {
    event.stopPropagation(); // Stop event propagation to prevent double triggering
    document.getElementById('file-upload-right').click();
}