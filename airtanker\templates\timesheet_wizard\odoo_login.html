<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" href="{{ url_for('static', filename='assets/favicon-32x32.png') }}" type="image/png">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css"
          integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <link href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" rel="stylesheet">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" rel="stylesheet">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.7.1/css/buttons.dataTables.min.css">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/styles_odoo.css') }}"  />

    <style>
        .file-upload-wrapper {
            border: 2px dashed #91b0b3;
            border-radius: 10px;
            padding: 10px;
            text-align: center;
            position: relative;
            cursor: pointer;
        }
        .flash-message {
            color: white;
            background-color: rgb(216, 49, 49);
            padding: 10px;
            margin: 0 auto;
            border-radius: 5px;
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            display: none; /* Initially not displayed */
            opacity: 0; /* Start fully transparent */
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }
        div#loading {
            width: 500px;
            height: 500px;
            display: none;
            background: url(/static/assets/loadingOdoo.gif) no-repeat center center;
            background-size: contain;
            cursor: wait;
            z-index: 1000;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            
            box-shadow: none; /* Ensure no shadow is applied */
            filter: none; /* Remove any filters that might create a shadow effect */
        }
    </style>
    <title>Odoo Login</title>
</head>


<br>
<body class="bg-gradient-white">
    <div id="loading"></div>
    <!-- Place this within the body of your HTML template -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        {% from "includes/_formhelpers.html" import render_field %}
        <div class="wrapper fadeIn" id="content">
            <div id="formContent">                                   
                <img class="fadeIn first" src="{{ url_for('static', filename='assets/Odoo_logo_rgb.svg') }}" width="180" alt="">
                <h1 class="h4 text-gray-900 mb-4">                                            
                    Odoo Login
                </h1>
                <form method="POST" action="{{ url_for('odoo_login', next=request.args.get('next', '')) }}">
                    {{ render_field(form.user_name_pid, css_class="fadeIn first", value=email) }}
                    <div style="margin: auto; width: 85%;">
                        {{ render_field(form.user_pid_Password , css_class="fadeIn second text-center", value="") }}
                    </div>
                    <input type="submit" class="btn underlineHover" onclick="loading();" value="Submit">
                </form>
                <div id="formFooter">
                    <a class="underlineHover" href="/">Cancel</a>
                </div>
            </div>
        </div>

<script type="text/javascript">// <![CDATA[
    function loading(){
        $("#loading").show();
        $("#content").hide();
    }
// ]]></script>

<script src="https://code.jquery.com/jquery-3.2.1.slim.min.js"
        integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN"
        crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
        integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q"
        crossorigin="anonymous"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"
        integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl"
        crossorigin="anonymous"></script>

<script>
    document.addEventListener("DOMContentLoaded", function() {
        var flashMessages = document.querySelectorAll('.flash-message');
        flashMessages.forEach(function(flashMessage) {
            // Show the flash message immediately with a fade-in effect
            flashMessage.style.display = 'block';
            flashMessage.style.animation = 'fadeIn 1s forwards';
    
            // After the fade-in, plus the duration of visibility, start the fade-out
            setTimeout(function() {
                flashMessage.style.animation = 'fadeOut 2s forwards';
    
                // Wait for the fade-out animation to complete before setting display to none
                setTimeout(function() {
                    flashMessage.style.display = 'none';
                }, 2000); // Duration of the fadeOut animation
            }, 4000); // 1s for fadeIn to complete + 3s visible = 4s total before fadeOut begins
        });
    });
    </script>                                    
</body>
</html>
