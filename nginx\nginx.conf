worker_processes auto;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Define the format of log messages.
    log_format  main_ext  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for" '
                      '"$host" sn="$server_name" '
                      'rt=$request_time '
                      'ua="$upstream_addr" us="$upstream_status" '
                      'ut="$upstream_response_time" ul="$upstream_response_length" '
                      'cs=$upstream_cache_status' ;

    access_log  /var/log/nginx/access.log main_ext;
    error_log  /var/log/nginx/error.log warn;

    sendfile        on;

    keepalive_timeout  65;

    # Enable Compression
    gzip  on;

    # Disable Display of NGINX Version
    server_tokens off;

    # Size Limits
    client_body_buffer_size 10K;
    client_header_buffer_size 1k;
    client_max_body_size 16m;
    large_client_header_buffers 2 1k;

    # # SSL / TLS Settings - Suggested for Security
    # ssl_protocols TLSv1.2 TLSv1.3;
	# ssl_session_timeout 15m;
	# ssl_ciphers ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256;
	# ssl_prefer_server_ciphers on;
	# ssl_session_tickets off;

    include /etc/nginx/conf.d/*.conf;

}