<!DOCTYPE html>
<html lang="en">    
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AirTanker</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" rel="stylesheet">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap4.min.css">
    <style>
        .file-upload-wrapper {
            border: 2px dashed #91b0b3;
            border-radius: 10px;
            padding: 10px;
            text-align: center;
            position: relative;
            cursor: pointer;
        }
        .dataTables_info {
            display: none;
        }
        .dataTables_filter {
            display: none;
        }
        .dataTables_length, .dataTables_paginate {
            display: none;
        }

        .file-upload-wrapper:hover {
            background-color: #f3f4f6;
        }
        .file-upload-wrapper i {
            color: #5cb85c;
        }
        .list-group-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .list-group-item i.fa-times {
            color: red;
            cursor: pointer;
        }
        #browse-btn {
            color: blue; /* Set the text color */
            text-decoration: underline; /* Underline the text to mimic a hyperlink */
            cursor: pointer; /* Change the cursor to indicate it's clickable */
        }
    </style>
</head>
<body>
    
    <div class="container mt-4">
        <div class="row">
            <!-- Left side upload form for Customer Sheets -->
            <div class="col-md-6">
                <h3 class="text-center">Customer Sheets</h3> <!-- Title for the left container -->
                <form id="upload-form-left" enctype="multipart/form-data">
                    <div class="file-upload-wrapper" onclick="document.getElementById('file-upload-left').click()">
                        <i class="fas fa-cloud-upload-alt fa-2x"></i>
                        <p>Drag files here or <a href="#" onclick="handleBrowseClickLeft(event)">Browse</a></p>
                    </div>
                    <input id="file-upload-left" type="file" name="customerFiles[]" multiple style="display: none;" onchange="addFilesCustomer()">
                    <ul class="list-group mt-3" id="file-list-left" style=" margin-bottom: 20px;">
                        <!-- Files will be listed here -->
                    </ul>
                </form>
            </div>
            
            <!-- Right side upload form for Internal Sheets -->
            <div class="col-md-6">
                <h3 class="text-center">Internal Sheets</h3> <!-- Title for the right container -->
                <form id="upload-form-right" enctype="multipart/form-data">
                    <div class="file-upload-wrapper" onclick="document.getElementById('file-upload-right').click()">
                        <i class="fas fa-cloud-upload-alt fa-2x"></i>
                        <p>Drag files here or <a href="#" onclick="handleBrowseClickRight(event)">Browse</a></p>
                    </div>
                    <input id="file-upload-right" type="file" name="internalFiles[]" multiple style="display: none;" onchange="addFilesInternal()">
                    <ul class="list-group mt-3" id="file-list-right" style=" margin-bottom: 20px;">
                        <!-- Files will be listed here -->
                    </ul>
                </form>
            </div>
        </div>
    </div>
    <div class="d-flex justify-content-center">
        <button id="process-files-btn"  onclick="processFiles()" class="btn btn-primary" style="display:none;">Compare</button>
    </div>
    <div class="row justify-content-center">
        <div class="col-auto">
            <table id="dataTable" class="table table-hover" style="display:none; width:100%; margin-top: 20px;">                
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Start</th>
                        <th>End</th>
                        <th>Internal Hours</th> 
                        <th>External Hours</th> 
                        <th>JobName</th>            
                        <th>Customer</th>
                        <th>Error</th>
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>

                </tbody>
                <tfoot>
                    <tr>
                        <th>Name</th>
                        <th>Start</th>
                        <th>End</th>
                        <th>Internal Hours</th> 
                        <th>External Hours</th> 
                        <th>JobName</th>            
                        <th>Customer</th>
                        <th>Error</th>
                        <th>Notes</th>
                    </tr>
                </tfoot>
            </table>
        </div>            
    </div>


    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.3/js/dataTables.bootstrap4.min.js"></script>
    <script>
    $(document).ready(function() {
        $('#dataTable').DataTable(); // Initialize DataTables
    });
    </script>
    <!-- <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script> -->
    <script>
        let internalSelectedFiles = [];
        let customerSelectedFiles = [];
        
        // Customer Methods
        function addFilesCustomer() {
            var files = document.getElementById('file-upload-left').files;
            for (var i = 0; i < files.length; i++) {
                customerSelectedFiles.push(files[i]);
            }
            //console.log(len(customerSelectedFiles))
            updateFileListCustomer();
            document.getElementById('file-upload-left').value = ''; // Clear the current selection
        }
        
        function updateFileListCustomer() {
            var output = document.getElementById('file-list-left');
            output.innerHTML = '';
            for (var i = 0; i < customerSelectedFiles.length; ++i) {
                output.innerHTML += '<li class="list-group-item">' +
                                    '<i class="fas fa-file-alt"></i> ' +
                                    customerSelectedFiles[i].name +
                                    '<i class="fas fa-times" onclick="removeFileCustomer(' + i + ')"></i>' +
                                    '</li>';
            }
            updateVisibilityOfProcessButton();
        }
        
        function removeFileCustomer(index) {
            customerSelectedFiles.splice(index, 1); // Remove the file from the array
            updateFileListCustomer(); // Update the list
        }
        
        
        
        // Internal
        function addFilesInternal() {
            var files = document.getElementById('file-upload-right').files;
            for (var i = 0; i < files.length; i++) {
                internalSelectedFiles.push(files[i]);
            }
            updateFileListInternal();
            document.getElementById('file-upload-right').value = ''; // Clear the current selection
        }
        
        function updateFileListInternal() {
            var output = document.getElementById('file-list-right');
            output.innerHTML = '';
            for (var i = 0; i < internalSelectedFiles.length; ++i) {
                output.innerHTML += '<li class="list-group-item">' +
                                    '<i class="fas fa-file-alt"></i> ' +
                                    internalSelectedFiles[i].name +
                                    '<i class="fas fa-times" onclick="removeFileInternal(' + i + ')"></i>' +
                                    '</li>';
            }
            updateVisibilityOfProcessButton();
        }
        
        function removeFileInternal(index) {
            internalSelectedFiles.splice(index, 1); // Remove the file from the array
            updateFileListInternal(); // Update the list
        }
        
        
        // Show compare button
        function updateVisibilityOfProcessButton() {
            // Check if both arrays have at least one file
            if (internalSelectedFiles.length > 0 && customerSelectedFiles.length > 0) {
                document.getElementById('process-files-btn').style.display = 'block'; // Show the button
            } else {
                document.getElementById('process-files-btn').style.display = 'none'; // Hide the button
            }
        }    
        

        // Compare button click
        function processFiles() {
            const formData = new FormData()
                                    
            formData.append(`Test`, "test");

            internalSelectedFiles.forEach(file=> {
                formData.append(`internalFiles[]`, file);
            });

            customerSelectedFiles.forEach(file => {
                formData.append(`customerFiles[]`, file);
            });
            fetch('/process-files', {
                method: 'POST',
                body: formData,
            })
            .then(response => {
                if (response.ok) {
                    // Resetting the UI elements as needed
                    document.getElementById('file-list-right').files = null;
                    internalSelectedFiles = [];
                    updateFileListInternal();
                    document.getElementById('file-list-left').files = null;
                    customerSelectedFiles = [];
                    updateFileListCustomer();
                    return response.json(); // This already parses the JSON response
                } else {
                    throw new Error('Network response was not ok.');
                }
            })
            .then(dataArray => {

                // Assuming dataTable is already initialized
                let table = $('#dataTable').DataTable();
                table.clear();

                dataArray.forEach(row => {
                    // Add new row through DataTables API
                    table.row.add([
                        row.employee_name,
                        row.start_date,
                        row.end_date,
                        row.internal_daily_hours,
                        row.external_daily_hours,
                        row.job_name,
                        row.customer,
                        row.error == 1 ? "Yes" : "No",
                        row.notes
                    ]).draw(true); // 'false' to redraw the table once after all rows are added
               });

                // show the datatable
                document.getElementById('dataTable').style.display = 'block';

                // show the filter stuff
                $('.dataTables_filter').css('display', 'block');
                $('.dataTables_length').css('display', 'block');
                $('.dataTables_paginate').css('display', 'block');
                $('.dataTables_info').css('display', 'block');

                table.draw();
                }
            )
            .catch(error => console.error('Error:', error));

        };


        
        function handleBrowseClickLeft(event) {
            event.stopPropagation(); // Stop event propagation to prevent double triggering
            document.getElementById('file-upload-left').click();
        }
        function handleBrowseClickRight(event) {
            event.stopPropagation(); // Stop event propagation to prevent double triggering
            document.getElementById('file-upload-right').click();
        }
                
    </script>

    

</body>
</html>
