const spinnerModal = new bootstrap.Modal('#spinnerModal', {
    backdrop: 'static',
    keyboard: false 
});
const detailsModal = new bootstrap.Modal('#detailsModalDaily', {});
const approveModal = new bootstrap.Modal('#hoursSelectionModal', {
    backdrop: 'static',
    keyboard: false 
});

$(document).ready(function() {
    $( '#divWeeklyExpenses' ).hide();

})

function reinitTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('.dyn-tt'));
    var tooltipList = tooltipTriggerList.map( function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            trigger: 'hover'
        });
    });
}

function GetWeekEnding(){
    return $('#weekEndingPicker').val();
}

function fetchWeeklyData() {
    spinnerModal.show();

    var weeklyDiv = $( '#divWeeklyExpenses' );
    if (weeklyDiv.is(":visible")) {
        refreshWeeklyTable();
        return;
    }
    weeklyDiv.show();

    $( '#employeeWeeklyDetailsTable' ).DataTable().destroy();
    let table = $( '#employeeWeeklyDetailsTable' ).DataTable({
        ajax: {
            url: '/api/employee_details_weekly/expenses',
            data: function(d) {
                d.weekEnding = GetWeekEnding();
            },
            dataSrc: ''
        },
        columns: [
            { data: 'FirstName' },
            { data: 'LastName' },
            { data: 'WorkOrderNumber' },
            { data: 'SiteSheetName' },
            {
                data: 'TotalExpenses',
                render: (data) => {
                    var currency = DataTable.render
                        .number(',','.',2,'$')
                        .display(data);
                    
                    return currency;
                }
            },
            {
                data: 'TotalAllowedExpenses',
                render: (data) => {
                    var currency = DataTable.render
                        .number(',','.',2,'$')
                        .display(data);
                    
                    return currency;
                }
            },
            {
                data: 'ApprovedTotalExpenses',
                render: (data) => {
                    var currency = DataTable.render
                        .number(',','.',2,'$')
                        .display(data);
                    
                    return currency;
                }
            },
            {
                render: (data, type, row) => {
                    return getActionButtons(row);
                }
            },
            { data: 'Notes' },
            {
                data: 'Status',
                // render: (data, type, row) => {

                // }
            },
            {
                data: 'DailyExpenseBreakdown',
                visible: false
            }
        ],
        rowCallback: (row, data) => {
            setRowClasses(data, row);
        },
        pageLength: 25
    })
    
    table.on('click', 'tr', function(e) {
        let parentRow = e.target.closest('tr');
        let rowData = table.row(parentRow).data();

        if (e.target && e.target.classList.contains('status-action-btn')) {
            e.stopPropagation();
            let action = e.target.dataset.bsTitle;
            if (action.includes('Approve')) {
                approveDenyExpenses(parentRow, rowData, 3);
            }
            else if (action.includes('Deny')) {
                approveDenyExpenses(parentRow, rowData, 4);
            }
            return;
        }

        if (rowData) {
            if (rowData.DailyExpenseBreakdown) {
                populateAdditionalDailyTable(rowData.DailyExpenseBreakdown, rowData.FirstName + ' ' + rowData.LastName);
                detailsModal.show();
            }
        }
    })

    table.on('draw', function () {
        reinitTooltips();
        spinnerModal.hide();
    })

    $.fn.dataTable.ext.search.push(
        function(settings, data, dataIndex) {
            let selectedStatuses = [];
            $('.status-checkbox:checked').each(function() {
                selectedStatuses.push($(this).val());
            });

            let status = data[9]; // Assuming status is the 10th column (index 9)

            if (selectedStatuses.length === 0 || selectedStatuses.includes(status)) {
                return true;
            }
            return false;
        }
    );
    $('.status-checkbox').on('change', function() {
        table.draw();
    });
}

function approveDenyExpenses(parentRow, rowData, statusId) {
    var timesheetId = rowData.TimesheetID;
    var hasError = rowData.Notes != '';

    if (hasError || rowData.TotalExpenses != rowData.TotalAllowedExpenses) {
        if (statusId == 3) {
            // Approve, but errors exist
            openHoursSelectionModal(rowData.TotalExpenses, rowData.TotalAllowedExpenses, timesheetId, 6);
            return;
        }
        else if (statusId == 4) {
            // Deny with errors
            statusId = 5;
        }
    }

    // If we made it this far, the reported and allowed expenses are equal. Take reported
    updateStatus(timesheetId, statusId, rowData.TotalExpenses, 'reportedExp')
    .then(data => {
        if (data) {
            refreshWeeklyTable();
        }        
    })
}

function getActionButtons(data) {

    var approveButton = `
        <button class="btn btn-success dyn-tt status-action-btn ri-checkbox-circle-fill" data-bs-toggle="tooltip" data-bs-title="Approve Expenses"></button>
    `;
    var denyButton = `
        <button class="btn btn-danger dyn-tt status-action-btn ri-close-circle-fill" data-bs-toggle="tooltip" data-bs-title="Deny Expenses"></button>
    `;
    var exportButton = `
        <button class="btn btn-primary dyn-tt button-pdf-export status-action-btn disabled-button ri-download-fill" data-bs-toggle="tooltip" data-bs-title="Export Hours to PDF Timesheet"></button>
    `

    return approveButton + denyButton; // + exportButton;
}

function setRowClasses(row, addedRow) {
    let hasError = row.Notes != '';

    //console.log(`Assigning row classes: Original [${addedRow.className}] - Adding for [${row.Status}]`)
    if (row.Status == "Exported") {
        $(addedRow).addClass('gray-out-row');
        $(addedRow).addClass('table-secondary');
        $(addedRow).find('.status-action-btn')
            .removeClass('btn-success btn-danger status-action-btn')
            .addClass('disabled-button');
        $(addedRow).find('.button-pdf-export')
            .removeClass('disabled-button');
            // .addClass('status-action-btn');
    } else if (row.Status == "Approved With Errors") {
        $(addedRow).addClass('table-success');
    } else if (row.Status == "Denied With Errors") {
        $(addedRow).addClass('table-danger');
    } else if (row.Status == "Denied") {
        $(addedRow).addClass('table-danger');
    } else if (row.Status == "Approved") {
        $(addedRow).addClass('table-success');
    } else if (hasError == true) {
        $(addedRow).addClass('table-warning');
    } else {
        // $(addedRow).addClass('table-secondary');
    }
}

function refreshWeeklyTable() {
    //console.log('Refreshing weekly table...');
    spinnerModal.show();
    $('#employeeWeeklyDetailsTable').DataTable().ajax.reload();
}

function openHoursSelectionModal(reportedExp, allowedExp, timesheetId, statusId) {
    try{
        const form = document.getElementById('hoursSelectionForm');
        form.innerHTML = '';
        console.log("INside method openHoursSelectionModal");
        // Save timesheetId and statusId in the form for later access
        form.dataset.timesheetId = timesheetId;
        form.dataset.statusId = statusId;
    
        // Mapping for custom labels
        const labelsMapping = {
            reportedExp: "Reported",
            allowedExp: "Allowed"
        };
    
        // Create radio options dynamically
        const expTypes = {reportedExp, allowedExp};
        for (const [type, value] of Object.entries(expTypes)) {
            const label = document.createElement('label');
            label.classList.add('form-check-label');
    
            const input = document.createElement('input');
            input.type = 'radio';
            input.classList.add('form-check-input');
            input.name = 'approvedExpenses';
            input.value = type;
            input.dataset.hoursValue = value; // Store the hours value in dataset for easy access
            
            // Event listener to enable the Save Changes button when a selection is made
            input.addEventListener('change', () => {
                document.getElementById('saveSelectedHours').disabled = false;
            });
    
            // Use the custom label from mapping
            const customLabel = labelsMapping[type] || type;
    
            label.appendChild(input);
            label.appendChild(document.createTextNode(`${customLabel}: ${value}`));
    
            const div = document.createElement('div');
            div.classList.add('form-check');
            div.appendChild(label);
    
            form.appendChild(div);
        }
    
        document.getElementById('saveSelectedHours').disabled = true;
        document.getElementById('saveSelectedHours').addEventListener('click', function() {
            const selectedHoursType = document.querySelector('input[name="approvedExpenses"]:checked').value;
            const selectedHoursValue = document.querySelector('input[name="approvedExpenses"]:checked').dataset.hoursValue;
            console.log(`Selected hours type: ${selectedHoursType}, value: ${selectedHoursValue}`);
            
            const form = document.getElementById('hoursSelectionForm');
            const timesheetId = form.dataset.timesheetId;
            const statusId = form.dataset.statusId;
        
            updateStatus(timesheetId, statusId, selectedHoursValue, selectedHoursType)
            .then(data => {
                //console.log(data);
                if (data) {
                    refreshWeeklyTable();
                    approveModal.hide();
                }
            })
        
            console.log('Saving approved hours:', selectedHoursValue, 'for timesheetId:', timesheetId, 'with statusId:', statusId);
            // Implement the logic to handle the approved hours selection.
            // This might involve an API call to update the timesheet status and approved hours based on the user's selection
        });
        
        approveModal.show();
    }
    catch (error) {
        console.error('Error opening hours selection modal:', error);
    }
}

function updateStatus(timeSheetId, statusId, selectedExp, selectedExpType) {

    return fetch(`/api/update_status/expenses`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `timesheetID=${encodeURIComponent(timeSheetId)}&statusID=${encodeURIComponent(statusId)}&expenses=${encodeURIComponent(selectedExp)}&type=${encodeURIComponent(selectedExpType)}`
    })
    .then(response => {
        if (!response.ok) {
            // If the response is not ok, throw an error to be caught by the catch block
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .catch(error => {
        // Handle fetch errors
        alert('Error: ' + error.message); // Make sure to use error.message to get the actual error message
        throw error; // Rethrow the error if you want to chain a catch outside of this function
    });
};

function getReportedExpenses(reported, allowed) {
    if (reported == null) {
        reported = 0;
    }
    let formatted = DataTable.render
        .number(',','.',2,'$')
        .display(reported);
    if (allowed != reported){
        return `<span class="text-danger">${formatted}</span>`
    }
    else if (allowed > 0 && reported > 0) {
        return `<span class="text-success">${formatted}</span>`
    } else {
        return formatted;
    }
}

function populateAdditionalDailyTable(data, employee) {
    $( '#detailsModalLabelDaily' ).empty();
    $( '#detailsModalLabelDaily' ).append(`<h4>Entries for ${employee} on Week Ending ${GetWeekEnding()}</h4>`)
    try {
        // Assuming additionalTable is already initialized
        $('#additionalDetailsTableDaily').DataTable().destroy();
        let table = $('#additionalDetailsTableDaily').DataTable({
            data: data,
            columns: [
                {
                    data: 'Date',
                    render: DataTable.render.date()
                },
                { data: 'Type' },
                { data: 'FirstName', visible: false },
                { data: 'LastName', visible: false },
                {
                    data: 'ReportedExpenses.Lodging',
                    render: (data, type, row) => {
                        let allowedExpenses = row.AllowedExpenses[0];
                        return getReportedExpenses(data, allowedExpenses.x_lodging);
                    }
                },
                {
                    data: 'AllowedExpenses[0].x_lodging',
                    render: $.fn.dataTable.render.number(',','.','2','$')
                },
                {
                    data: 'ReportedExpenses.Airfare',
                    render: (data, type, row) => {
                        let allowedExpenses = row.AllowedExpenses[0];
                        return getReportedExpenses(data, allowedExpenses.x_airfare);
                    }
                },
                {
                    data: 'AllowedExpenses[0].x_airfare',
                    render: $.fn.dataTable.render.number(',','.','2','$')
                },
                {
                    data: 'ReportedExpenses.RentalCar',
                    render: (data, type, row) => {
                        let allowedExpenses = row.AllowedExpenses[0];
                        return getReportedExpenses(data, allowedExpenses.x_vehicle);
                    }
                },
                {
                    data: 'AllowedExpenses[0].x_vehicle',
                    render: $.fn.dataTable.render.number(',','.','2','$')
                },
                {
                    data: 'ReportedExpenses.Misc',
                    render: (data, type, row) => {
                        let allowedExpenses = row.AllowedExpenses[0];
                        return getReportedExpenses(data, allowedExpenses.x_misc);
                    }
                },
                {
                    data: 'AllowedExpenses[0].x_misc',
                    render: $.fn.dataTable.render.number(',','.','2','$')
                },
                {
                    data: 'ReportedExpenses.PerDiem',
                    render: (data, type, row) => {
                        let allowedExpenses = row.AllowedExpenses[0];
                        return getReportedExpenses(data, allowedExpenses.x_perdiem);
                    }
                },
                {
                    data: 'AllowedExpenses[0].x_perdiem',
                    render: $.fn.dataTable.render.number(',','.','2','$')
                },
                {
                    data: 'ReportedExpenses.TravelHours',
                    render: (data, type, row) => {
                        let reported = (data == null) ? 0 : data;
                        let allowedExpenses = row.AllowedExpenses[0];
                        if (reported != allowedExpenses.x_travel_time) {
                            return `<span class="text-danger">${reported}</span>`;
                        }

                        return reported;
                    }
                },
                {
                    data: 'AllowedExpenses[0].x_travel_time'
                },
                {
                    data: 'ReportedExpenses.MileageTotal',
                    render: (data, type, row) => {
                        let allowedExpenses = row.AllowedExpenses[0];
                        return getReportedExpenses(data, allowedExpenses.total_allowed_mileage_expense);
                    }
                },
                {
                    data: 'AllowedExpenses[0].total_allowed_mileage_expense',
                    render: $.fn.dataTable.render.number(',','.','2','$')
                }
            ],
            fixedColumns: {
                left: 2
            },
            order: [
                [0, "asc"]
            ],
            scrollX: true,
            //scrollCollapse: true,
            paging: false
        });
        table.draw();
    }
    catch (error) {
        console.log(error);
    }
}