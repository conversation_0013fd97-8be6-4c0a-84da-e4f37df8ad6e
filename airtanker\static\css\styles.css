body, html {
    height: 100%;
    margin: 0;
    font-family: Arial, sans-serif;
  }
  
  .container {
    width: 80%;
    margin: 0 auto;
    padding: 20px;
    box-sizing: border-box;
  }
  
  .section {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
  }
  
  .files {
    align-items: flex-end;
  }
  
  .file-container, .result {
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
  }
  
  .file-display, .result-display {
    width: 100%;
    height: 150px;
    background-color: #f0f0f0;
    margin-bottom: 10px;
  }
  
  .upload-btn, .compare-btn {
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    background-color: #4CAF50;
    color: white;
    border-radius: 4px;
  }
  
  .upload-btn {
    margin-top: 10px;
  }
  
  .compare-btn {
    margin-top: 20px;
    align-self: center;
  }
  
  span {
    display: block;
    margin-top: 10px;
    color: #555;
  }
  
  .result-display {
    height: 300px;
  }
  
  @media (max-width: 768px) {
    .container {
      width: 95%;
    }
  
    .section {
      flex-direction: column;
    }
  
    .file-container {
      margin-bottom: 20px;
    }
  
    .compare-btn {
      margin-bottom: 20px;
    }
    
    .upload-container {
      margin: 20px;
      padding: 20px;
      border: 1px solid #ccc;
      border-radius: 5px;
      text-align: center;
    }
    
    .upload-container h2 {
      margin-bottom: 10px;
    }
    
    .upload-container input[type="file"] {
      margin-bottom: 10px;
    }
    
  }
  body, html {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
  }
  
  .loading-screen {
    position: fixed;
    width: 100%;
    height: 100%;
    background: #000;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease;
  }
  
  .main-content {
    display: none;
    opacity: 0;
    transition: opacity 2s ease;
  }
  
  .fade-in {
    opacity: 1;
  }
  