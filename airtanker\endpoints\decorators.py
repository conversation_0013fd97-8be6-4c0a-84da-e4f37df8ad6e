import traceback
from main import airtanker_app
from flask import abort, request, redirect, url_for, session, flash, render_template
from functools import wraps

# Decorators to check if the user is authenticated
def requires_authentication(func):
    @wraps(func)
    def wrapper(*args, **kwargs):        
        real_ip = request.headers.get('X-Forwarded-For', request.remote_addr).split(',')[0].strip()
        if airtanker_app.config.get('MAINTENANCE_MODE', False) and not real_ip in airtanker_app.config.get('ALLOWED_IPS', []):
            return render_template('maintenance.html')

        if 'username' not in session:
            return redirect(url_for('login'))
        try:
            return func(*args, **kwargs)
        except Exception as error:
            # Log the error along with its traceback
            error_message = f"Server Error: {error}, Traceback: {traceback.format_exc()}"
            session['error'] = error_message
            abort(500)
            # Optional: Handle the error differently here if needed
            # For instance, you could redirect to a custom error page
            # return redirect(url_for('error_page'))

            # Re-raise the error if you want to propagate it further
            # (e.g., to another error handler or to show <PERSON>lask's default error page in debug mode)
            raise
    return wrapper

def requires_odoo_authentication(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        if 'odoo_service' not in session:
            flash("Please login.")
            next_url = request.full_path
            airtanker_app.logger.info(f"current endpoint is {request.endpoint}, next is {next_url}")
            # If we're already on the login page, do not include the 'next' parameter
            if request.endpoint != 'odoo_login':
                return redirect(url_for('odoo_login', next=next_url))
            else:
                return redirect(url_for('odoo_login'))
        return func(*args, **kwargs)
    return wrapper

def requires_ldap_group_authentication(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        if 'member_of' not in session:
            return redirect(url_for("home"))
        elif session.get('member_of') != 'finances':
            flash("You are not authorized to view that page.")
            return redirect(url_for("home"))
        return func(*args, **kwargs)
    return wrapper