{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Employee Name</th>\n", "      <th>Cost Center</th>\n", "      <th>Employee Number</th>\n", "      <th><PERSON><PERSON><PERSON>cy</th>\n", "      <th>TRAVEL</th>\n", "      <th>RT</th>\n", "      <th>OT</th>\n", "      <th>DT</th>\n", "      <th>TRAVEL</th>\n", "      <th>RT</th>\n", "      <th>OT</th>\n", "      <th>DT</th>\n", "      <th>TRAVEL</th>\n", "      <th>RT</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>** PLEASE ENSURE INVOICE IS ISSUED TO: VALIANT...</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>HOLIDAY THURSDAY</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>MEDRANO FELIX, FRANCISCO ALEJANDRO</td>\n", "      <td>20120N11</td>\n", "      <td>816003</td>\n", "      <td>USD</td>\n", "      <td>58.00</td>\n", "      <td>58.00</td>\n", "      <td>75.40</td>\n", "      <td>92.80</td>\n", "      <td>32.00\\n30.00\\n25.50</td>\n", "      <td>-</td>\n", "      <td>1,856.00</td>\n", "      <td>2,262.00</td>\n", "      <td>2,366.40</td>\n", "      <td>6,484.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>20120N11</td>\n", "      <td>6,484.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                       Employee Name Cost Center  \\\n", "0  ** PLEASE ENSURE INVOICE IS ISSUED TO: VALIANT...               \n", "1                                                                  \n", "2                 MEDRANO FELIX, FRANCISCO ALEJANDRO    20120N11   \n", "3                                                                  \n", "4                                                                  \n", "5                                                                  \n", "\n", "  Employee Number Currency TRAVEL     RT     OT     DT               TRAVEL  \\\n", "0                                                                             \n", "1                                                          HOLIDAY THURSDAY   \n", "2          816003      USD  58.00  58.00  75.40  92.80  32.00\\n30.00\\n25.50   \n", "3                                                                             \n", "4                                                                             \n", "5                                                                             \n", "\n", "  RT        OT        DT    TRAVEL        RT  \n", "0                                             \n", "1                                             \n", "2  -  1,856.00  2,262.00  2,366.40  6,484.40  \n", "3                                             \n", "4                         20120N11  6,484.40  \n", "5                                             "]}, "metadata": {}, "output_type": "display_data"}], "source": ["import camelot\n", "import pandas as pd\n", "from IPython.display import display\n", "import re\n", "from datetime import datetime\n", "from nameparser import HumanName\n", "\n", "\n", "def is_header_row(row, expected_headers, threshold=0.5):\n", "    \"\"\"\n", "    Check if the given row is likely to be a header row based on the presence of expected headers.\n", "    \"\"\"\n", "    match_count = sum(1 for cell in row if any(header.lower() in str(cell).lower() for header in expected_headers))\n", "    return match_count / len(expected_headers) >= threshold\n", "\n", "\n", "file_paths = [\n", "    \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\816 Atom Tech USA 2024.07.07.pdf\",\n", "]\n", "\n", "for file_path in file_paths:\n", "    # Regular expression to find the date in the filename\n", "    \n", "\n", "    # Extract tables from page 1 of the PDF\n", "    tables = camelot.read_pdf(file_path, pages='2', multiple_tables=False)\n", "\n", "    # Check if any tables were extracted\n", "    if not tables:\n", "        raise ValueError(\"No tables found on the specified page.\")\n", "\n", "    # Convert the first table to a DataFrame\n", "    table = tables[0]\n", "\n", "    #modified_data = table.df.map(lambda x: x.replace('\\n', ' ') if isinstance(x, str) else x)\n", "    #table.df = modified_data\n", "\n", "    df = table.df\n", "    df = df.iloc[4:].reset_index(drop=True)\n", "    # Use the modified row 3 as headers and split cells with '\\n' into separate columns\n", "    headers = [\n", "            'Employee Name', 'Cost Center', 'Employee Number', 'Currency', 'TRAVEL', 'RT', 'OT', 'DT', 'TRAVEL', 'RT', 'OT',\n", "            'DT', 'TRAVEL', 'RT', 'OT', 'DT', 'Approved Total'\n", "        ]\n", "\n", "    # Adjust the number of columns in the DataFrame to match the headers\n", "    df.columns = headers[:len(df.columns)]\n", "\n", "    df.fillna(\"PLACEHOLDER\", inplace=True)    # df.dropna(how='all', inplace=True)\n", "\n", "    #df = tables[0].df\n", "    display(df)\n", "\n", "    # # Remove empty rows\n", "    # df = df.iloc[3:].reset_index(drop=True)\n", "\n", "    # # Replace empty cells with NaN\n", "    # df.replace(\"\", float(\"NaN\"), inplace=True)\n", "\n", "    # # Fill NaN with placeholder value to standardize columns\n", "\n", "    # display(df)\n", "    # headers = [\n", "    #         'Employee Name', 'Cost Center', 'Employee Number', 'Currency', 'TRAVEL', 'RT', 'OT', 'DT', 'TRAVEL', 'RT', 'OT',\n", "    #         'DT', 'TRAVEL', 'RT', 'OT', 'DT', 'Approved Total'\n", "    #     ]\n", "\n", "    # # Set the columns in the dataframe\n", "    # df.columns=headers\n", "\n", "    # # Remove the header row from the data if it exists\n", "    # #df = df[1:].reset_index(drop=True)\n", "    # display(df)\n", "\n", "    # # Keep only the 1st and 4th row (index 0, 3) and remove all other rows\n", "    # #df = df.iloc[4:].reset_index(drop=False)\n", "\n", "\n", "    # # Extract values from row 1, column 1 (index 0)\n", "    # #employee_name = HumanName(df.iloc[1, 0])\n", "\n", "    # # Since the PDF table doesn't contain a breakdown then we'll just have to put all hours on one day.\n", "    # week_ending = pd.to_datetime(df.iloc[0, -1].split('\\n')[-1], format='%m.%d.%Y')\n", "\n", "    # total_hours = sum(pd.to_numeric(df.iloc[1, [8, 9, 10, 11]], errors='coerce').fillna(0))\n", "\n", "\n", "#display(df)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}