from main import airtanker_app
from flask import render_template, request, session, flash, redirect, url_for

from endpoints.decorators import requires_authentication
from services.OdooService import OdooService
from auth.global_ldap_authentication import *

from forms.WorkOrderForm import *
from forms.LoginForm import *
from datetime import timedelta

@airtanker_app.route('/test123')
def test123():
    return 'Test route is working!'

@airtanker_app.route('/login', methods=['GET', 'POST'])
def login():
    
    real_ip = request.headers.get('X-Forwarded-For', request.remote_addr).split(',')[0].strip()
    if airtanker_app.config.get('MAINTENANCE_MODE', False) and not real_ip in airtanker_app.config.get('ALLOWED_IPS', []):
        return render_template('maintenance.html')
    
    if 'username' in session:
        return redirect(url_for('home'))
    # initiate the form..
    form = LoginValidation()
    email = ''
    if form.validate_on_submit():  
        login_id = form.user_name_pid.data
        login_password = form.user_pid_Password.data
        # create a directory to hold the Logs
        login_msg, name = global_ldap_authentication(login_id, login_password)
        # validate the connection
        if "**" not in login_msg:
            session['username'] = login_id      
            session['name'] = name
            session['member_of'] = login_msg
            session.modified = True
            session.permanent = True
            airtanker_app.permanent_session_lifetime = timedelta(minutes=15)
            airtanker_app.logger.info(f"{login_id} successfully logged in under the group: {login_msg}")
            return redirect(url_for('home'))
        else:
            flash(f"*** Authentication Failed - {login_msg}")
            airtanker_app.logger.error(f"Failed to login {login_id}. {login_msg}")
            email = login_id
        
    return render_template('endpoints/login.html', form=form, email=email)


@airtanker_app.route('/logout')
def logout():
    session.clear()  # Clears all data including CSRF tokens
    response = redirect(url_for('login'))
    response.headers['Cache-Control'] = 'no-store'  # Ensures this redirect doesn't get cached
    return response


@airtanker_app.route('/odoo_login', methods=['GET', 'POST'])
@requires_authentication
def odoo_login():    
    # initiate the form..
    form = LoginValidation_Odoo()
    email = ''
    if 'internal_errors_found' in session:
        session.pop('internal_errors_found', None)
    # if request.method in ('POST') :
    #     airtanker_app.logger.debug("Submitted CSRF token: %s", request.form.get('csrf_token'))
    #     airtanker_app.logger.debug("Session CSRF token: %s", session.get('_csrf_token'))
    if form.validate_on_submit():  # This will automatically validate the CSRF token
        username = form.user_name_pid.data

        password = form.user_pid_Password.data
        odoo_service = OdooService(password=password)

        # create a directory to hold the Logs
        userId = odoo_service.login(username)

        # validate the connection
        if userId:
            # add Odoo session to the current users session
            session['odoo_service'] = odoo_service
            next_url = request.args.get('next')
            if next_url:
                next_url = next_url.replace('?','')
                return redirect(next_url.replace('?',''))
            else:
                # redirect to step 1 - import work orders           
                return redirect(url_for('import_work_orders'))
        else:
            next_url = request.args.get('next')
            flash(f"*** Authentication Failed")
            email = username  # Pass the email back to the form

            if next_url:
                return render_template('timesheet_wizard/odoo_login.html', form=form, email=email, next=next_url)
            return render_template('timesheet_wizard/odoo_login.html', form=form, email=email)
    elif 'odoo_service' in session:
        # if user already logged into Odoo, redirect them to page
        flash('You are already logged into Odoo.', 'info')
        return redirect(url_for('import_work_orders'))

    return render_template('timesheet_wizard/odoo_login.html', form=form)


@airtanker_app.route("/")
@requires_authentication
def home():
    name = session['name']
    return render_template("endpoints/home.html", username=name)


@airtanker_app.route('/faqs', methods=['GET'])
@requires_authentication
def faqs():
    return render_template('endpoints/faqs.html')