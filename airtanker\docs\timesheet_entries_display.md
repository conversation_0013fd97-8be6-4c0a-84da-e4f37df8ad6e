# Timesheet Entries Display Feature

## Overview
The Timesheet Entries Display page provides a comprehensive view of processed customer timesheet data from the AI upload feature. It displays parsed timesheet entries with filtering, sorting, grouping, and export capabilities.

## Features

### Statistics Dashboard
- **Total Hours**: Sum of all reported hours
- **Total Entries**: Number of timesheet entries
- **Employees**: Count of unique employees
- **Customers**: Count of unique customers  
- **Errors**: Number of entries with error messages

### Filtering & Controls
- **Search**: Text search across all entry fields
- **Group By**: Group entries by:
  - No Grouping (default)
  - Customer
  - Employee
  - Project
  - Date
  - Rate Type
- **Sort By**: Sort entries by:
  - Date (default)
  - Hours
  - Customer
  - Employee
  - Project
- **Sort Order**: Ascending or Descending

### Data Table
Displays the following columns:
- **Date**: Entry date
- **Employee**: Employee name
- **Customer**: Customer name
- **Project**: Project ID
- **Work Order**: Work Order ID
- **Hours**: Reported hours (formatted to 2 decimal places)
- **Rate Type**: REG (Regular) or OT (Overtime)
- **Notes**: Additional notes
- **Status**: <PERSON><PERSON> or <PERSON><PERSON>r (with error message tooltip)

### Export Options
- Copy to clipboard
- CSV export
- Excel export
- PDF export
- Print

## Usage

### Accessing the Page
1. Upload customer timesheets using the AI upload feature (`/upload_customer_timesheets_ai`)
2. After processing, you'll be automatically redirected to the display page
3. Or access directly at `/timesheet_entries_display` (requires processed data in session)

### Navigation
- **Back to Upload**: Return to the upload page
- **Export Data**: Quick access to Excel export

### Testing
For development/testing purposes, access `/timesheet_entries_display_test` to view the page with sample data.

## Technical Implementation

### Endpoints
- `GET /timesheet_entries_display`: Main display page
- `GET /api/timesheet_entries_data`: API endpoint for data retrieval
- `GET /timesheet_entries_display_test`: Test page with sample data

### Data Flow
1. AI upload process stores processed dataframe in session as JSON
2. Display page retrieves data from session
3. JavaScript initializes DataTables with the data
4. User interactions trigger table updates and filtering

### Dependencies
- DataTables with extensions:
  - Buttons (for export functionality)
  - RowGroup (for grouping functionality)
- Bootstrap 5 (for styling)
- RemixIcon (for icons)

## Data Structure
The page expects timesheet entries with the following structure:
```json
{
  "Date": "2025-06-16",
  "EmployeeName": "John Doe",
  "CustomerName": "ABC Corp",
  "ProjectID": "21001",
  "WorkOrderID": "3001",
  "CustomerReportedHours": 8.0,
  "RateTypeID": 1,
  "Notes": "Regular work",
  "ErrorMessage": "",
  "CustomerID": 101,
  "EmployeeID": 1001
}
```

## Error Handling
- Displays informative message when no data is available
- Handles null/undefined data gracefully
- Provides navigation back to upload page when no data exists
- Shows error status and messages for problematic entries
