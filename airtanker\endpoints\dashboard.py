from main import airtanker_app
from flask import render_template, request, jsonify, session, flash
from endpoints.decorators import requires_authentication, requires_odoo_authentication
from services.DatabaseService import DatabaseService

from datetime import datetime
from nameparser import HumanName

@airtanker_app.route('/dashboard', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def dashboard():
    if request.method == 'POST':
        # things/stuff
        pass
    else:
        selected_week_ending = session.get('selected_week_ending')
        if not selected_week_ending:
            selected_week_ending = False

        return render_template('endpoints/dashboard.html', selected_week_ending = selected_week_ending)
    
@airtanker_app.route('/api/get_workorder_dashboard', methods=['GET'])
@requires_authentication
def get_workorders_by_weekending():
    weekEnding = request.args.get('weekEnding')
    
    odoo_service = session['odoo_service']
    database_service = DatabaseService()
    database_service.connect()

    query = """
        EXECUTE [dbo].[GetWorkOrderReport] 
        @WeekEnding = ?
        """
    work_order_data = database_service.execute_query(query, weekEnding)

    # Coalesce bills and find in Odoo
    invoices = [datum['Invoice_Created'] for datum in work_order_data if datum['Invoice_Created'] is not None]
    if invoices:
        odoo_invoices = odoo_service.get_account_moves(set(invoices))
        pass

    # Coalesce bills and find in Odoo
    odoo_bills = []
    bills = [datum['Bill_Created'] for datum in work_order_data if datum['Bill_Created'] is not None]
    if bills:
        odoo_bills = odoo_service.get_account_moves(set(bills))
        pass

    work_order_ids = [datum['ID'] for datum in work_order_data]
    work_orders = odoo_service.get_work_orders_for_talent_desk_by_ids(work_order_ids)

    week_ending_date = datetime.strptime(weekEnding, '%Y-%m-%d')
    inv_week_ending = datetime.strftime(week_ending_date, '%m/%d/%Y')
    week_invoices = odoo_service.get_invoices_by_week(inv_week_ending)
    
    for row in work_order_data:        
        row['StartDate'] = datetime.strftime(row['StartDate'], '%m/%d/%Y')
        # Check for Work Order data
        wo_id = row['ID']
        work_order =  next((wo for wo in work_orders if wo["id"] == wo_id), None)
        if work_order:
            row['Site'] = work_order['x_studio_plant_name']
        else:
            row['Comments'] = 'No Work order found in Odoo'
            continue
        # Apply comments
        if not row['Approved Hours'] or row['Approved Hours'] <= 0:
            if row['Employee Hours'] and row['Employee Hours'] > 0:
                row['Comments'] = 'Employee Hours Unapproved'
            elif row['Customer Hours'] and row['Customer Hours'] > 0:
                row['Comments'] = 'Only Customer Hours Reported'
            else:
                row['Comments'] = 'No Hours Submitted'
                      
        # Check for Bill/Invoice data
        invoice_legit = False
        invoice_id = False

        if row['Invoice_Created']:            
            invoice_id = row['Invoice_Created']
            invoice = next((inv for inv in odoo_invoices if inv['id'] == invoice_id), None)
            if invoice:
                row['Invoice_Created'] = invoice['name']
                invoice_legit = True
        
        if not invoice_legit:            
            # Invoice ID is stale or missing in MSSQL. Search Odoo for related invoices               
            project_invoices = get_project_invoices(work_order['project_analytic_account_id'], week_invoices)
            if project_invoices:
                related_invoices = []
                employee_name = HumanName(row['Employee'])
                for invoice in project_invoices:
                    for line in invoice['lines']:
                        line_name = HumanName(line['name'])
                        if line_name == employee_name or line_name.last in employee_name.surnames_list:
                            related_invoices.append(invoice)
                            break
                        else:
                            pass
                if len(related_invoices) > 0:
                    row['Invoice_Created'] = "/".join([inv['name'] for inv in related_invoices])
                else:
                    if row['Comments']:
                        row['Comments'] += ', '
                    row['Comments'] += 'Employee name not found in invoice(s)'
                    # row['Invoice_Created'] = "/".join([inv['name'] for inv in project_invoices])
            elif invoice_id:
                row['Invoice_Created'] = '[Not Found]'
                airtanker_app.logger.error(f'Invoice {invoice_id} cannot be found in Odoo!')
            elif row['Approved Hours'] and row['Approved Hours'] > 0:
                row['Invoice_Created'] = '[Missing]'
        
        if row['Bill_Created']:
            bill_id = row['Bill_Created']
            bill = next((b for b in odoo_bills if b['id'] == bill_id), None)
            if bill:
                row['Bill_Created'] = bill['name']
                pass
            else:
                airtanker_app.logger.error(f'Bill {bill_id} cannot be found in Odoo!')  

    data = {
        'WorkOrders': work_order_data,
        'Invoices': week_invoices,
        'Bills': odoo_bills
    }

    return jsonify(data)

def get_project_invoices(analytic_id, invoices):
    analytic_search = str(analytic_id[0])
    found_invoices = []
    for invoice in invoices:
        for line in invoice['lines']:
            if line['analytic_distribution'] and analytic_search in line['analytic_distribution']:
                found_invoices.append(invoice)
                break

    return found_invoices
