<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" href="{{ url_for('static', filename='assets/favicon-32x32.png') }}" type="image/png">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css"
          integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <link href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" rel="stylesheet">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" rel="stylesheet">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.7.1/css/buttons.dataTables.min.css">
    
    <style>
        .file-upload-wrapper {
            border: 2px dashed #91b0b3;
            border-radius: 10px;
            padding: 10px;
            text-align: center;
            position: relative;
            cursor: pointer;
        }
        .flash-message {
            color: white;
            background-color: rgb(216, 49, 49);
            padding: 10px;
            margin: 0 auto;
            border-radius: 5px;
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            display: none; /* Initially not displayed */
            opacity: 0; /* Start fully transparent */
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        #browse-btn {
            color: blue; /* Set the text color */
            text-decoration: underline; /* Underline the text to mimic a hyperlink */
            cursor: pointer; /* Change the cursor to indicate it's clickable */
        }
        
        .admin-badge {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            margin-left: 10px;
        }
    </style>
    <title>AirTanker Admin Login</title>
</head>


<br>
<body class="bg-gradient-white">
<div id="page-wrapper">
    <div class="container">
    <!-- Place this within the body of your HTML template -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        {% from "includes/_formhelpers.html" import render_field %}
        
        <div class="row justify-content-center">
            <div class="col-xl-10 col-xl-12 col-xl-9">
                <div class="card o-hidden border-0 shadow-lg my-5">
                    <div class="card-body p-0">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="p-4">
                                    <div class="text-left">
                                        <h1 class="h4 text-gray-900 mb-4">
                                            <div class="image-wrapper">
                                                <img src="{{ url_for('static', filename='assets/favicon-32x32.png') }}" style="margin-right: 5px;" alt="sidebar background" />
                                                  AirTanker Admin Login
                                                  <span class="admin-badge">🔧 ADMIN</span>
                                            </div>
                                        </h1>
                                        <div class="alert alert-warning" role="alert">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            <strong>Admin Access:</strong> This login bypasses maintenance mode and provides access to admin settings.
                                        </div>
                                    </div>

                                    <form method="POST" class="form-horizontal needs-validation" action="{{ url_for('admin_login') }}">
                                        <div class="form-group row">
                                            <div class="col-sm-10">
                                                {{ form.user_name_pid(class_="form-control", placeholder="Enter Your Email Address", value=email) }}
                                            </div>
                                        </div>
                                        
                                        <div class="form-group row">
                                            <div class="col-sm-10">
                                                {{ form.user_pid_Password(class_="form-control", placeholder="Enter Your Password") }}
                                            </div>
                                        </div>
                                        
                                        <div class="form-group row">
                                            <div class="col-sm-4 col-form-label">
                                                <input type="submit" class="btn btn-danger" value="Admin Login">
                                            </div>
                                        </div>
                                    </form>
                                    
                                    <hr>
                                    <div class="text-center">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle"></i>
                                            This admin login works even during maintenance mode.<br>
                                            Requires admin privileges (finances group).
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.2.1.slim.min.js"
        integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN"
        crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
        integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q"
        crossorigin="anonymous"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"
        integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl"
        crossorigin="anonymous"></script>
        <script>
            document.addEventListener("DOMContentLoaded", function() {
                var flashMessages = document.querySelectorAll('.flash-message');
                flashMessages.forEach(function(flashMessage) {
                    // Show the flash message immediately with a fade-in effect
                    flashMessage.style.display = 'block';
                    flashMessage.style.animation = 'fadeIn 1s forwards';
            
                    // After the fade-in, plus the duration of visibility, start the fade-out
                    setTimeout(function() {
                        flashMessage.style.animation = 'fadeOut 2s forwards';
            
                        // Wait for the fade-out animation to complete before setting display to none
                        setTimeout(function() {
                            flashMessage.style.display = 'none';
                        }, 2000); // Duration of the fadeOut animation
                    }, 4000); // 1s for fadeIn to complete + 3s visible = 4s total before fadeOut begins
                });
            });
            </script>
            
            
            
</body>
</html>
