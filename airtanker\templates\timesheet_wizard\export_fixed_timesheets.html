{% extends 'wizard-base.html' %}

{% block styles %}
<!-- 
<link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/export_summary.css') }}"  />
<style>
    .progress-container {
        width: 100%;
        background-color: #ddd;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .progress-bar {
        height: 30px;
        background-color: #4CAF50; /* Green background */
        background-image: linear-gradient(to right, #4caf50, #81c784); /* Gradient effect */
        border-radius: 8px;
        width: 0%; /* Initial width */
        position: relative; /* Relative position for inner text placement */
        overflow: hidden;
        display: flex;
        align-items: center; /* Center the label vertically */
    }

    .progress-bar span {
        color: white;
        margin-left: 10px; /* Give some space for the text from the start */
        font-weight: bold;
        z-index: 1; /* Make sure the text is above the background */
    }
    .progress-bar {
        transition: width 0.4s ease;
    }
    div#loading {
        width: 500px;
        height: 500px;
        display: none;
        background: url(/static/assets/loadingOdoo.gif) no-repeat center center;
        background-size: contain;
        cursor: wait;
        z-index: 1000;
        position: relative;
        top: 25%;
        left: 50%;
        transform: translate(-50%, -50%);
        
        box-shadow: none; /* Ensure no shadow is applied */
        filter: none; /* Remove any filters that might create a shadow effect */
    }
    .timesheet-container {
        position: relative;
        height: 500px; /* Adjust height as needed */
    }
    .timesheet-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: none;
        z-index: 0;
    }
    .timesheet-wrapper.active {
        display: block;
        z-index: 1;
    }
    .pagination-controls {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
        margin-top: 20px;
    }
    .pagination-controls button {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        margin: 0 10px;
        cursor: pointer;
        border-radius: 5px;
    }
    .pagination-controls button:disabled {
        background-color: #cccccc;
        cursor: not-allowed;
    }
    .pagination-controls span {
        font-size: 16px;
        margin: 0 10px;
    }
</style> 
-->
<!-- <style>
    .o_main_navbar {background-color: #e48108; border-bottom: 0px;}
    .o_main_navbar .show .dropdown-toggle {background-color: #e48108;}
    .o_main_navbar > ul > li > a:hover, .o_main_navbar 
    > ul > li > label:hover {background-color: #f79e30;}
    .o_main_navbar > a:hover, .o_main_navbar > a:focus, .o_main_navbar 
    > button:hover, .o_main_navbar 
    > button:focus {background-color: #f79e30; color: inherit;}
</style> -->
<style>
    #stepProgress div {
        animation-name: loadProgress;
        animation-duration: 2s; /* Customize this value */
        animation-fill-mode: forwards; /* Keeps the state at 66% after animation */
    }
    
    @keyframes loadProgress {
        from {
            width: 34%;
        }
        to {
            width: 66%;
        }
    }
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript" charset="utf8" src="static/js/progress.js"></script>
<script>
    $( document ).ready(function() {
        $( "#stepLabel" ).append("<p>Step 2: Export Timesheets to Odoo</p>");
        $( "#stepProgress" ).attr("aria-valuenow", "66")
    });

    function export_to_odoo(event, data) {
        document.getElementById('waiting_message').style.display = 'block';
        document.getElementById('timesheet-container').style.display = 'none';
        document.getElementById("paging_tabs").style.display = 'none';

        let actionUrl = '/post_timesheets';
        fetch(actionUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(response_data),
            credentials: 'same-origin'
        })        
        .then(response => {
            if (!response.ok) {
                // If the server response is not ok (e.g., 500 Internal Server Error), throw an error
                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }

            return response.json();
        })
        .then(data => {
            if (data.task_id){
                startProgress(`/progress/${data.task_id}`);
            }
            else{
                console.log("No task_id");
            }
        })
        .catch(error => {
            // Handle any errors that occurred during the fetch or due to a server error
            console.error('Error:', error);
            doneLoading();
        })
    };    

    document.getElementById('export_button').addEventListener('click', function(event) {
        export_to_odoo(event, response_data);
    });

    let response_data = null;
    let response_errors = null;

    document.addEventListener('DOMContentLoaded', () => {
        console.log("getting data...");
        loading();
        fetch('/get_timesheets?type=internal')
        .then(response => response.json())
        .then(data => {
            if (data.redirect_url) {
                alert("Error: No timesheets available for selected week ending");
                window.location.href = "/";
            } else {                
                console.log("Errors first:", data.errors);
                if (data.status === 'error' && data.errors) {
                    handle_timesheet_errors(data.errors, data.projects);
                } else {
                    showTablePopupTimesheets(data.data);
                    document.getElementById("paging_tabs").style.display = 'block';
                }                
            }
        })
        .catch(error => {
            console.error('Error fetching data:', error);
            alert(`Error fetching data: ${error}`);
            window.location.href = "/";
        });
    });

    function showTablePopupTimesheets(data) {
        doneLoading();
        response_data = data;
        createTimesheetList(data);
    }
    
    async function loadTasks(index) {
        const projectId = document.getElementById(`project-${index}`).value;
        const taskSelect = document.getElementById(`task-${index}`);

        // Clear existing options
        taskSelect.innerHTML = '';

        // Fetch tasks for the selected project
        try {
            const response = await fetch(`/get_tasks/${projectId}`);
            const tasks = await response.json();

            // Populate the task dropdown
            tasks.forEach(task => {
                const option = document.createElement('option');
                option.value = task.id;
                option.text = task.name.trim(); // Trim whitespace from task names
                taskSelect.appendChild(option);
            });

            // Adjust width after populating options
            adjustWidth(taskSelect);
            validateForm();
        } catch (error) {
            console.error('Error fetching tasks:', error);
        }
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        
        // Extract the components
        const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-based
        const day = String(date.getDate()).padStart(2, '0');
        const year = date.getFullYear();

        // Construct the formatted date string
        return `${month}/${day}/${year}`;
    }

    function adjustWidth(selectElement) {
        const tempSelect = document.createElement('select');
        tempSelect.style.visibility = 'hidden';
        tempSelect.style.position = 'absolute';

        selectElement.parentNode.appendChild(tempSelect);
        tempSelect.innerHTML = selectElement.innerHTML;
        tempSelect.selectedIndex = selectElement.selectedIndex;

        const tempSelectWidth = tempSelect.offsetWidth;
        selectElement.style.width = tempSelectWidth + 'px';

        selectElement.parentNode.removeChild(tempSelect);
    }

    async function handle_timesheet_errors(invalidEntries, projects) {
        response_errors = invalidEntries;

        let tableHtml = `
            <style>
                .table-custom th, .table-custom td {
                    padding: 8px;
                    text-align: left;
                }
                .contractor-column {
                    min-width: 150px;
                    max-width: 220px; /* Set a maximum width */
                    overflow: hidden;
                }
                .date-column {
                    min-width: 110px;
                }
                .project-column, .task-column, .description-column {
                    min-width: 200px;
                }
                .hours-column {
                    min-width: 80px;
                    max-width: 100px;
                }
                select {
                    min-width: 350px; /* Set a minimum width */
                    max-width: 350px; /* Set a maximum width */
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            </style>
            <div style="max-height: 700px; max-width: 100%; overflow: auto;"> <!-- Container with scrollbars -->
                <table class="table table-custom" style="min-width: 1500px;"> <!-- Table with min-width -->
                    <thead>
                        <tr>
                            <th class="contractor-column">Contractor</th>
                            <th class="date-column">Date</th>
                            <th class="project-column">Project</th>
                            <th class="task-column">Task</th>
                            <th class="hours-column">Orig. Project</th>
                            <th class="description-column">Task Desc.</th>
                            <th class="hours-column">Hours</th>
                        </tr>                    
                    </thead>
                    <tbody>`;

        for (let index = 0; index < invalidEntries.length; index++) {
            const entry = invalidEntries[index];
            const formattedDate = formatDate(entry.Date); // Format the date
            let tasks = [];

            if (entry.ProjectID) {
                // Fetch tasks for the pre-populated ProjectID
                tasks = await fetchTasks(entry.ProjectID);
            }

            tableHtml += `
                <tr>
                    <td class="contractor-column">${entry.EmployeeName}</td>
                    <td class="date-column">${formattedDate}</td>
                    <td class="project-column">
                        <select id="project-${index}" onchange="loadTasks(${index})" ${entry.ProjectID ? 'disabled' : ''}>
                            ${projects.map(project => `<option value="${project.id}" ${project.id === entry.ProjectID ? 'selected' : ''}>${project.name}</option>`).join('')}
                        </select>
                    </td>
                    <td class="task-column">
                        <select id="task-${index}" oninput="adjustWidth(this)">
                            ${tasks.map(task => `<option value="${task.id}" ${task.id === entry.TaskID ? 'selected' : ''}>${task.name.trim()}</option>`).join('')}
                        </select>
                    </td>
                    <td class="project-column">${entry.ProjectName}</td>
                    <td class="description-column">${entry.Description}</td>
                    <td class="hours-column">${entry.Hours}</td>
                </tr>`;
        }

        tableHtml += `
                </tbody>
            </table>
        </div>`; // Close the container div

        Swal.fire({
            title: 'Update Tasks On Timesheets',
            html: tableHtml,
            width: '1700px',
            showCancelButton: true, // This line ensures the cancel button is shown
            cancelButtonText: 'Cancel',
            confirmButtonText: 'Submit',
            allowOutsideClick: false, // Disable outside click
            didOpen: () => {
                document.querySelectorAll('select').forEach(select => {
                    adjustWidth(select);
                    select.addEventListener('change', validateForm); // Add change event listener for validation
                });
                validateForm(); // Initial validation check
                document.body.classList.remove('swal2-height-auto');

            },
            preConfirm: () => {
                let updatedEntries = invalidEntries.map((entry, index) => {
                    return {
                        ...entry,
                        ProjectID: document.getElementById(`project-${index}`).value,
                        TaskID: document.getElementById(`task-${index}`).value
                    };
                });
                return updatedEntries;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                $('#spinnerModal').modal({
                    backdrop: 'static', // Prevent clicking outside the modal to close
                    keyboard: false // Prevent closing the modal with keyboard ESC key
                }).appendTo('body');
                // result.value contains the updated entries
                fetch('/update_timesheet_entries', {
                    method: 'POST',
                    body: JSON.stringify(result.value),
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(()  => {
                    fetch('/get_timesheets')
                    .then(response => response.json())
                    .then(data => {
                        if (data.redirect_url) {
                            alert("Error: No timesheets available for selected week ending");
                            window.location.href = "/";
                        }
                        else{                            
                            console.log("Errors first:", data.errors);
                            if (data.status === 'error' && data.errors) {
                                handle_timesheet_errors(data.errors, data.projects);
                            }
                            else{
                                showTablePopupTimesheets(data.data);
                                document.getElementById("paging_tabs").style.display = 'block';
                            }


                        }
                    })
                    .catch(error => {
                        console.error('Error fetching data:', error);
                        alert(`Error fetching data: ${error}`);
                        window.location.href = "/";
                    });
                })
                .catch(error => console.error('Error updating entries:', error))
            } else if (result.dismiss === Swal.DismissReason.cancel) {
                // The user clicked on "Cancel" button
                console.log("Update cancelled by user.");
                window.location.href = "/";
            }
        });
    }

    async function fetchTasks(projectId) {
        try {
            const response = await fetch(`/get_tasks/${projectId}`);
            const tasks = await response.json();
            return tasks;
        } catch (error) {
            console.error('Error fetching tasks:', error);
            return [];
        }
    }
    
    function validateForm() {
        let allValid = true;
        document.querySelectorAll('select[id^="project-"]').forEach((projectSelect, index) => {
            const taskSelect = document.getElementById(`task-${index}`);
            if (!projectSelect.value || !taskSelect.value) {
                allValid = false;
            }
        });
        Swal.getConfirmButton().disabled = !allValid;
    }

    function createTimesheetList(data) {
        const timesheetContainer = document.getElementById('timesheet-container');
        const timesheetWrapper = document.createElement('div');
        timesheetWrapper.classList.add('o_content');

        // create the headers and the overall container of the list.
        html = `
                    <div class="o_list_renderer o_renderer table-responsive" tabindex="-1">
                    <table class="o_list_table table table-sm table-hover position-relative mb-0 o_list_table_ungrouped table-striped" style="table-layout: fixed;">
                        <thead>
                            <tr>

                            
                                <!-- Checkbox -->
                                <th class="o_list_record_selector o_list_controller align-middle pe-1 cursor-pointer" tabindex="-1" style="width: 40px;">
                                    <div class="o-checkbox form-check d-flex">
                                        <input type="checkbox" class="form-check-input" id="select-all" checked/>
                                        <label class="form-check-label" for="select-all"></label>
                                    </div>
                                </th>
                            
                            
                                <!-- Date -->
                                <th data-tooltip-delay="1000" tabindex="-1" data-name="date" class="align-middle o_column_sortable position-relative cursor-pointer opacity-trigger-hover" style="min-width: 92px; width: 96px;">
                                    <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">Date</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                    <span class="o_resize position-absolute top-0 end-0 bottom-0 ps-1 bg-black-25 opacity-0 opacity-50-hover z-index-1"></span>
                                </th>
                                

                                <!-- Name -->
                                <th data-tooltip-delay="1000" tabindex="-1" data-name="employee_id" class="align-middle o_column_sortable position-relative cursor-pointer o_many2one_avatar_employee_cell opacity-trigger-hover" style="width: 236px;">
                                    <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">Employee</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                    <span class="o_resize position-absolute top-0 end-0 bottom-0 ps-1 bg-black-25 opacity-0 opacity-50-hover z-index-1"></span>
                                </th>


                                <!-- Project -->
                                <th style="width: 300px;" data-tooltip-delay="1000" tabindex="-1" data-name="project_id" class="align-middle o_column_sortable position-relative cursor-pointer opacity-trigger-hover" style="width: 259px;">
                                    <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">Project</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                    <span class="o_resize position-absolute top-0 end-0 bottom-0 ps-1 bg-black-25 opacity-0 opacity-50-hover z-index-1"></span>
                                </th>

                                
                                <!-- Task -->
                                <th style="width: 200px;" data-tooltip-delay="1000" tabindex="-1" data-name="task_id" class="align-middle o_column_sortable position-relative cursor-pointer o_task_with_hours_cell opacity-trigger-hover" style="width: 166px;">
                                    <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">Task</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                    <span class="o_resize position-absolute top-0 end-0 bottom-0 ps-1 bg-black-25 opacity-0 opacity-50-hover z-index-1"></span>
                                </th>


                                <!-- Description -->
                                <th style="width: 300px;" data-tooltip-delay="1000" tabindex="-1" data-name="name" class="align-middle o_column_sortable position-relative cursor-pointer opacity-trigger-hover" style="width: 243px;">
                                    <div class="d-flex"><span class="d-block min-w-0 text-truncate flex-grow-1">Description</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                    <span class="o_resize position-absolute top-0 end-0 bottom-0 ps-1 bg-black-25 opacity-0 opacity-50-hover z-index-1"></span>
                                </th>

                            
                                <!-- Hours -->
                                <th style="width: 150px;" data-tooltip-delay="1000" tabindex="-1" data-name="unit_amount" class="align-middle o_column_sortable position-relative cursor-pointer o_list_number_th o_timesheet_uom_timer_cell opacity-trigger-hover" style="width: 109px;">
                                    <div class="d-flex flex-row-reverse"><span class="d-block min-w-0 text-truncate flex-grow-1 o_list_number_th">Hours Spent</span><i class="fa fa-lg px-2 fa-angle-down opacity-0 opacity-75-hover"></i></div>
                                    <span class="o_resize position-absolute top-0 end-0 bottom-0 ps-1 bg-black-25 opacity-0 opacity-50-hover z-index-1"></span>
                                </th>


                            </tr>
                        </thead>`

        data.forEach((entry, index) => {  
            // Add the data in dynamically
            html += `<tbody class="ui-sortable">
                        <tr class="o_data_row" data-id="${entry.RowID}">

                            
                            <!-- Check Box -->
                            <td class="o_list_record_selector" tabindex="-1">
                                <div class="o-checkbox form-check">
                                    <input type="checkbox" class="form-check-input row-checkbox" id="checkbox-${index}" ${entry.IsChecked ? 'checked' : ''}/>
                                    <label class="form-check-label" for="checkbox-${index}"></label>
                                </div>
                            </td>


                            <!-- Date -->
                            <td class="o_data_cell cursor-pointer o_field_cell o_required_modifier" data-tooltip-delay="1000" tabindex="-1" name="date">${entry.Date}
                            </td>


                            <!-- Name -->
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one o_many2one_avatar_employee_cell o_required_modifier" data-tooltip-delay="1000" tabindex="-1" name="employee_id" data-tooltip="${entry.ContractorName}">
                                <div name="employee_id" class="o_field_widget o_required_modifier o_field_many2one_avatar_employee o_field_many2one_avatar o_field_many2one_avatar_user">
                                    <div class="d-flex" data-tooltip="${entry.ContractorName}"><span><span>${entry.ContractorName}</span></span></div>
                                </div>
                            </td>


                            <!-- Project -->
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one o_required_modifier" data-tooltip-delay="1000" tabindex="-1" name="project_id" data-tooltip="${entry.ProjectName}">${entry.ProjectName}</td>
                            </td>


                            <!-- Task -->
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_many2one o_task_with_hours_cell" data-tooltip-delay="1000" tabindex="-1" name="task_id" data-tooltip="${entry.TaskName}" title="">
                                <div name="task_id" class="o_field_widget o_field_task_with_hours"><span><span>${entry.TaskName}</span></span></div>
                            </td>

                            
                            <!-- Description -->
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_char" data-tooltip-delay="1000" tabindex="-1" name="name" data-tooltip="${entry.Description}">${entry.Description}</td>
                                                        
                            
                            <!-- Hours -->
                            <td class="o_data_cell cursor-pointer o_field_cell o_list_number o_timesheet_uom_timer_cell" data-tooltip-delay="1000" tabindex="-1" name="unit_amount">
                                <div name="unit_amount" class="o_field_widget o_field_timesheet_uom_timer"><span>${entry.Hours}</span></div>
                            </td>


                            <td tabindex="-1"></td>
                        </tr>
                    </tbody>`
        });

        html += `</table>
            </div>`

        timesheetWrapper.innerHTML = html;

        timesheetContainer.appendChild(timesheetWrapper);

        // Event listener for the "Select All" checkbox
        document.getElementById('select-all').addEventListener('click', (e) => {
            const isChecked = e.target.checked;
            document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                checkbox.checked = isChecked;
                const rowId = checkbox.closest('.o_data_row').dataset.id;
                const entry = data.find(d => d.RowID == rowId);
                if (entry) {
                    entry.IsChecked = isChecked;
                }
            });
        });

        // Event listeners for individual row checkboxes
        document.querySelectorAll('.row-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const isChecked = e.target.checked;
                const rowId = checkbox.closest('.o_data_row').dataset.id;
                const entry = data.find(d => d.RowID == rowId);
                if (entry) {
                    entry.IsChecked = isChecked;
                }
            });
        });
    }
</script>
<!-- Scripts -->
<!-- 
<script type="text/javascript" charset="utf8" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
-->
{% endblock %}

{% block content %}
    <div id="progress-container" class="my-2" style="display: none;">
        <p id="progressLabel" class="fadeIn text-center mt-4">Exporting to Odoo...</p>
        <div id="progress-bar" class="progress w-75 position-absolute start-50 translate-middle" role="progressbar" 
            aria-valuemin="0" aria-valuemax="100" aria-valuenow="0">            
            <div class="progress-bar bg-success"></div>
        </div>
    </div>

    <h2 id="waiting_message" class="fs-2 text-uppercase" style="display: none;">
        Thank you for being patient. This process is expected to take a few minutes.
    </h2>

    <div id="paging_tabs" style="display: none;">
        <div class="pagination-controls my-2">
            <button class="btn btn-primary" id="export_button">Looks good!</button>
            <a class="btn btn-secondary mx-2" role="button" href="/">Cancel</a>            
        </div>
    </div>
    
    <div id="timesheet-container" class="timesheet-container">
        
    </div>
{% endblock %}








          