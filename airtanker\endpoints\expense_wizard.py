from main import airtanker_app
from flask import render_template, request, jsonify, session, flash, redirect, url_for

from endpoints.decorators import requires_authentication, requires_odoo_authentication

from services.EmployeeService import push_updates_expenses
from services.OdooService import OdooService
from services.ExcelFileService import ExcelFileService

from forms.WorkOrderForm import *
from services.DatabaseService import DatabaseService
from datetime import datetime, timedelta


# Step 1 - Import Work Orders From Odoo
@airtanker_app.route('/import_work_orders_expenses', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def import_work_orders_expenses():
    form = WorkOrderForm()
    if request.method in ('GET'):
        database_service = DatabaseService()
        database_service.connect()
        last_import = "No Imports Yet."

        query = """
            SELECT TOP 1 [ImportDate]
            FROM [dbo].[WorkOrders]
            ORDER BY [ImportDate] DESC
        """
        results = database_service.execute_query(query)

        if results:
            last_import_date = results[0]["ImportDate"]
            adjusted_import_date = last_import_date - timedelta(hours=4)  # Subtract 4 hours            
            today_date = datetime.now().date()
            
            if adjusted_import_date.date() == today_date:
                last_import = f"Today at {adjusted_import_date.strftime('%I:%M %p')}"
            else:
                last_import = adjusted_import_date.strftime("%Y-%m-%d %I:%M %p")

        database_service.disconnect()        
        return render_template('expense_wizard/import_work_orders.html', form=form, last_import=last_import)
    
    if form.validate_on_submit():  # This will automatically validate the CSRF token
        selected_week_ending = form.sundayPicker.data  # Access the selected date
        session['selected_week_ending'] = selected_week_ending
        odoo_service = session['odoo_service'] # type: OdooService
        odoo_service.import_work_orders_to_db(selected_week_ending)

        # redirect to step 2 - import internal sheets
        return redirect(url_for('upload_internal_expenses'))


# Step 2 - Upload Internal Sheets
@airtanker_app.route('/upload_internal_expenses', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def upload_internal_expenses():
    if request.args.get("week_ending_date"):
        session['selected_week_ending'] = request.args.get("week_ending_date")

    if request.method in ('POST'):
        excel_file_service = ExcelFileService()

        excel_file_service.name_errors = []
        excel_file_service.error_logs = []

        uploaded_files = request.files.getlist('uploaded_files[]')        
        errors, name_errors, fileIDs = excel_file_service.parse_internal_expenses(uploaded_files,
                                                                        session['selected_week_ending'],
                                                                        session["odoo_service"])
        if errors or name_errors:
            redirect_url = url_for('resolve_internal_errors_expenses')
            return jsonify({'redirect_url': redirect_url, "errors":errors, "name_errors":name_errors, "fileIds":fileIDs})
        else:
            completion_html = render_template('expense_wizard/wizard_complete.html')

            # Return the HTML content in the JSON response
            return jsonify({'success': True, 'completion_html': completion_html})

    return render_template('expense_wizard/upload_internal_expenses.html')
    

# Step 2.5 - Resolve Internal Timesheet Conflicts
@airtanker_app.route('/resolve_internal_errors_expenses', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def resolve_internal_errors_expenses():        
    if request.method in ('POST'):
        data_received = request.json
        error_logs = None
        if data_received:
            error_logs = push_updates_expenses(data_received, session['username'])

        completion_html = render_template('expense_wizard/wizard_complete.html')

        # Return the HTML content in the JSON response
        return jsonify({'success': True, 'completion_html': completion_html, "push_update_errors": error_logs})      

    return render_template('expense_wizard/resolve_internal_errors.html')


@airtanker_app.route('/approvals/expenses', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def approvals_expenses():
    form = WorkOrderForm()
    
    selected_week_ending = session.get('selected_week_ending')
    if not selected_week_ending:
        selected_week_ending = False

    return render_template('endpoints/expense_approvals.html', form=form, selected_week_ending=selected_week_ending)