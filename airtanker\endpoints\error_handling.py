from main import *
from flask import session, render_template, request, jsonify
from flask_wtf.csrf import CSRFError
from endpoints.decorators import requires_authentication
import re
from datetime import datetime

class LogEntry:
    log_pattern = re.compile(r'(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - (?P<level>\w+) - (?P<message>.*)')

    def __init__(self, timestamp=None, level=None, message=""):
        self.timestamp = timestamp
        self.level = level
        self.message = message

    @classmethod
    def parse(cls, log_line):
        match = cls.log_pattern.match(log_line)
        if match:
            timestamp = datetime.strptime(match.group('timestamp'), '%Y-%m-%d %H:%M:%S,%f')
            return cls(timestamp, match.group('level'), match.group('message'))
        else:
            return None  # Return None to indicate this is a continuation line

    def append_message(self, additional_message):
        # Append continuation of a log message to the existing message
        self.message += "\n" + additional_message

    def __str__(self):
        return f"{self.timestamp.strftime('%Y-%m-%d %H:%M:%S,%f')} - {self.level} - {self.message}"


def parse_log_file(filename):
    log_entries = []
    current_entry = None

    try:
        with open(filename, 'r') as file:
            for line in file:
                parsed_entry = LogEntry.parse(line)
                if parsed_entry:
                    if current_entry:
                        log_entries.append(current_entry)
                    current_entry = parsed_entry
                elif current_entry:
                    current_entry.append_message(line.strip())

        if current_entry:  # Append the last entry if it exists
            log_entries.append(current_entry)

    except FileNotFoundError:
        log_entries = [LogEntry(message="Log file not found.")]

    return log_entries


@airtanker_app.errorhandler(500)
def internal_error(e):
    error = "Error occurred on server."
    if 'error' in session:
        error = session['error']

    airtanker_app.logger.error('Server Error: %s', str(error))
    return render_template('errors/error_page.html', error_message=str(e)), 500


@airtanker_app.errorhandler(Exception)
def handle_exception(error):
    # You can decide to log the error here
    airtanker_app.logger.exception(f'Unhandled Exception in application!: {error}')
    # Return a custom error page or JSON response
    return render_template('errors/error_page.html'), 500


@airtanker_app.errorhandler(CSRFError)
def handle_csrf_error(e):
    return render_template('csrf_error.html', reason=e.description), 400


@airtanker_app.errorhandler(404)
def not_found_error(error):
    airtanker_app.logger.error(f'404 Error: Attempted to access: {request.url}')
    # return render_template('errors/404.html'), 404
    return

@airtanker_app.route('/logs')
@requires_authentication
def view_logs():
    log_level = request.args.get('level', 'ALL')
    search_query = request.args.get('search', '')
    sort_order = request.args.get('sort', 'desc')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')

    # Parse log file
    log_entries = parse_log_file('airtanker.log')
    filtered_entries = []

    try:
        # Apply filters sequentially
        filtered_entries = log_entries

        # Filter by log level if specified
        if log_level != 'ALL':
            filtered_entries = [entry for entry in filtered_entries if entry.level == log_level]

        # Apply date filtering
        if start_date or end_date:
            try:
                if start_date and end_date:
                    start = datetime.strptime(start_date, '%Y-%m-%d')
                    end = datetime.strptime(end_date, '%Y-%m-%d')
                    # Include full days by setting times to start and end of day
                    start = start.replace(hour=0, minute=0, second=0, microsecond=0)
                    end = end.replace(hour=23, minute=59, second=59, microsecond=999999)
                    filtered_entries = [entry for entry in filtered_entries 
                                     if start <= entry.timestamp <= end]
                elif start_date:
                    start = datetime.strptime(start_date, '%Y-%m-%d')
                    start = start.replace(hour=0, minute=0, second=0, microsecond=0)
                    filtered_entries = [entry for entry in filtered_entries 
                                     if entry.timestamp >= start]
                elif end_date:
                    end = datetime.strptime(end_date, '%Y-%m-%d')
                    end = end.replace(hour=23, minute=59, second=59, microsecond=999999)
                    filtered_entries = [entry for entry in filtered_entries 
                                     if entry.timestamp <= end]
            except ValueError as e:
                airtanker_app.logger.error(f'Date parsing error: {str(e)}')
                # If date parsing fails, continue without date filtering
                pass

        # Apply search filter if specified
        if search_query:
            filtered_entries = [entry for entry in filtered_entries 
                              if search_query.lower() in entry.message.lower()]

        # Sort entries
        if sort_order == 'desc':
            filtered_entries.reverse()

    except Exception as e:
        airtanker_app.logger.error(f'Error processing logs: {str(e)}')
        filtered_entries = []

    # Get summary counts
    total_entries = len(log_entries)
    filtered_count = len(filtered_entries)
    
    # Prepare statistics by log level
    level_counts = {}
    for entry in filtered_entries:
        level_counts[entry.level] = level_counts.get(entry.level, 0) + 1

    return render_template('endpoints/view_logs.html', 
                         log_content=[str(entry) for entry in filtered_entries],
                         log_level=log_level, 
                         search_query=search_query, 
                         sort_order=sort_order,
                         start_date=start_date,
                         end_date=end_date,
                         total_entries=total_entries,
                         filtered_count=filtered_count,
                         level_counts=level_counts)

# Function to update an existing key-value pair in the .env file
def update_env_file(key, value):
    env_file_path = '.env'
    updated = False
    with open(env_file_path, 'r') as env_file:
        lines = env_file.readlines()

    with open(env_file_path, 'w') as env_file:
        for line in lines:
            if line.startswith(f'{key}='):
                env_file.write(f'{key}={value}\n')
                updated = True
            else:
                env_file.write(line)

    if not updated:
        with open(env_file_path, 'a') as env_file:
            env_file.write(f'{key}={value}\n')
