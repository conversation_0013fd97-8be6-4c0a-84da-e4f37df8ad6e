{% extends 'base.html' %}

{% block styles %}
<style>
    .dropdown-check-list {
        display: inline-block;
        position: relative;
        width: 250px; /* Adjust the width as needed */
    }

    .dropdown-check-list .anchor {
        cursor: pointer;
        display: inline-block;
        padding: 5px 10px;
        border: 1px solid #ccc;
        border-radius: 4px;
        background-color: #fff;
        width: 100%;
        box-sizing: border-box;
    }

    .dropdown-check-list .anchor::after {
        content: "\25BC"; /* Down arrow */
        float: right;
        margin-left: 10px;
    }

    .dropdown-check-list .anchor.active::after {
        content: "\25B2"; /* Up arrow */
    }

    .dropdown-check-list .items {
        position: absolute;
        z-index: 1;
        border: 1px solid #ccc;
        border-radius: 4px;
        background-color: #fff;
        display: none;
        padding: 10px;
        margin: 0;
        list-style: none;
        max-height: 300px;
        overflow-y: auto;
        box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        width: 100%; /* Make the dropdown items container as wide as the anchor */
        box-sizing: border-box;
    }

    .dropdown-check-list .items li {
        padding: 5px 10px;
    }

    .dropdown-check-list .items li:hover {
        background-color: #f1f1f1;
    }

    .dropdown-check-list .items input {
        margin-right: 5px;
    }

    .warning-icon {
        color: sandybrown; /* Yellow color */
        margin-left: 5px; /* Space between the text and the icon */
    }

    .gray-out-row {
        background-color: #d3d3d3; /* Light gray background */
        color: #808080; /* Gray text */
    }

    .disabled-button {
        background-color: #d3d3d3; /* Light gray background */
        pointer-events: none; /* Disable pointer events */
        color: #808080; /* Gray text */
        cursor: not-allowed; /* Show not-allowed cursor */
    }

    /* Add this inside the <style> tag */
    .row-loading td {
        background-color: #f0f0f0;
        /* Reduce the vertical component of the pattern */
        background-image: repeating-linear-gradient(
            45deg,
            transparent,
            rgba(170, 170, 170, 0),
            rgba(170, 170, 170, 0.603),
            rgba(102, 102, 102, 0) 50%
        );
        background-size: 60px 60px; /* Smaller overall pattern */
        animation: moveStripes 1.2s linear infinite;
        color: #999;
    }

    @keyframes moveStripes {
        0% {
            background-position: 0 0;
        }
        100% {
            background-position: 60px 0; /* Match the new background-size */
        }
    }
</style>
{% endblock %}

{% block scripts %}
<script type="text/javascript" charset="utf8" src="{{ url_for('static', filename='js/timesheet_approvals.js') }}"></script>
<script>    
    document.addEventListener('DOMContentLoaded', function () {
        setMostRecentSunday();
    });

    function setMostRecentSunday() {
        const selectedWeekEnding = '{{ selected_week_ending }}';
        let formattedDate;

        if (selectedWeekEnding != 'False') {
            formattedDate = selectedWeekEnding;
        } else {
            const today = new Date();
            const dayOfWeek = today.getDay(); // Sunday - 0, Monday - 1, ..., Saturday - 6
            const difference = dayOfWeek % 7; // Calculate difference to get back to the previous Sunday
            const mostRecentSunday = new Date(today.setDate(today.getDate() - difference));
            
            // Format the date as YYYY-MM-DD
            formattedDate = mostRecentSunday.toISOString().split('T')[0];
        }
        
        document.getElementById('weekEndingPicker').value = formattedDate;
    }

    function setDateField(value) {
        var date = new Date(value);
        if (checkSunday(date)){
            const formattedDate = date.toISOString().split('T')[0];
            document.getElementById('weekEndingPicker').value = formattedDate;
        }
    }

    function checkSunday(selectedDate) {
        console.log(selectedDate);
        if (selectedDate.getDay() !== 6) {
            alert('Please select a Sunday.');
            setMostRecentSunday(); // Reset to the most recent Sunday if the check fails
            return false;
        }
        else {
            return true;
        }
    }
    
    // Toggle the dropdown
    $('.dropdown-check-list .anchor').on('click', function(e) {
        e.stopPropagation();
        $(this).toggleClass('active');
        $(this).next('.items').toggle();
    });

    // Hide dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.dropdown-check-list').length) {
            $('.dropdown-check-list .items').hide();
            $('.dropdown-check-list .anchor').removeClass('active');
        }
    });
</script>
{% endblock %}

{% block content %}
    <h3 class="text-center">Timesheet Approvals</h3>
    <!-- DatePicker -->
    <div class="date-picker-container">
        <label for="weekEndingPicker">Select a Week Ending:</label>
        <br>
        <input type="date" id="weekEndingPicker" name="weekEndingPicker" class="date-picker" onchange="setDateField(this.value);">
    </div>
    <!-- Get Data button and Compare button -->
    <div class="d-flex justify-content-center gap-2 my-2">
        <button onclick="fetchInitialData()" class="btn btn-primary static-tt" data-bs-toggle="tooltip" data-bs-title="Get the Daily Timesheet Records for Employees">Get Daily Data</button>
        <button onclick="fetchWeeklyData()" class="btn btn-primary static-tt" data-bs-toggle="tooltip" data-bs-title="Get the Weekly Timesheet Records for Employees">Get Weekly Data</button>
    </div>

    <!-- The Weekly Table -->
    <div id="divWeeklyTimesheets" class="row justify-content-center">
        <div id="employeeWeeklyDetailsTitle" class="text-center my-2"></div>
        <div id="legend" class="text-center mt-2">
            <i class="ri-error-warning-fill warning-icon"></i> <span>No expenses uploaded</span>
        </div>
        <div class="align-self-start">
            <div id="list1" class="dropdown-check-list" tabindex="100">
                <span class="anchor">Filter by Status</span>
                <ul class="items">
                    <li><label><input type="checkbox" value="Approved" class="status-checkbox"> Approved</label></li>
                    <li><label><input type="checkbox" value="Denied" class="status-checkbox"> Denied</label></li>
                    <li><label><input type="checkbox" value="Pending" class="status-checkbox"> Pending</label></li>
                    <li><label><input type="checkbox" value="Approved With Errors" class="status-checkbox"> Approved With Errors</label></li>
                    <li><label><input type="checkbox" value="Denied With Errors" class="status-checkbox"> Denied With Errors</label></li>
                    <li><label><input type="checkbox" value="Exported" class="status-checkbox"> Exported</label></li>
                </ul>
            </div>      
        </div>      
        <table id="employeeWeeklyDetailsTable" class="table table-bordered" style="width:100%;">
            <thead>
                <tr class="text-center align-middle">
                    <th colspan="2" >Name</th>
                    <th rowspan="2" style="min-width: 200px;">Work Order #</th>
                    <th colspan="4" style="min-width: 150px;">Reported Hours</th>
                    <th rowspan="2" style="min-width: 170px;">Actions</th>
                    <th rowspan="2" style="min-width: 250px;">Notes</th>
                    <th rowspan="2" style="min-width: 100px;">Status</th>
                </tr>
                <tr class="text-center align-middle">
                    <th>First</th>
                    <th>Last</th>
                    <th>ADP</th>
                    <th data-bs-toggle="tooltip" data-bs-title="Employee-reported Hours">Emp.</th>
                    <th data-bs-toggle="tooltip" data-bs-title="Customer-reported Hours">Cust.</th>
                    <th data-bs-toggle="tooltip" data-bs-title="Approved Hours">Appr.</th>
                </tr>
            </thead>
            <tbody class="text-center align-middle">
                <!-- JavaScript will populate table body here -->
            </tbody>
        </table>        
    </div>

    <!-- The Daily Table -->
    <div id="divDailyTimesheets" class="row justify-content-center">
        <div id="employeeDetailsTitle" class="text-center my-2"></div>
        <table id="employeeDetailsTable" class="table table-bordered mt-2" style="width:100%;">
            <thead>
                <tr>
                    <th rowspan="2">Date</th>
                    <th colspan="2">Name</th>
                    <th rowspan="2">Work Order #</th>
                    <th rowspan="2">Customer Name</th>
                    <th colspan="3">Reported Hours</th>
                    <th class="w-25" rowspan="2">Notes</th>
                    <th rowspan="2"></th>
                </tr>
                <tr>
                    <th>First</th>
                    <th>Last</th>
                    <th>ADP</th>
                    <th>Emp.</th>
                    <th>Cust.</th>
                </tr>
            </thead>
            <tbody class="text-center align-middle">
                <!-- JavaScript will populate table body here -->
            </tbody>
        </table>    
    </div>
{% endblock %}

{% block modal %}
    <!-- Modal for the spinner overlay -->
    <div id="spinnerModal" class="modal" tabindex="-1" aria-labelledby="spinnerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only"></span>
                    </div>
                    <p class="mt-2">Processing...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- The additional table popup for details - all entries for that day -->
    <div id="detailsModal" class="modal fade" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="detailsModalLabel">All Entries For Day</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <!-- Content of the modal goes here -->
              <table id="additionalDetailsTable" class="table" style="width:100%">
                <thead class="text-center">
                    <tr>
                        <th rowspan="2">Date</th>
                        <th rowspan="2">Source</th>
                        <th colspan="2">Name</th>
                        <th rowspan="2">Work Order #</th>
                        <th rowspan="2">Reported Hours</th>
                        <th rowspan="2">Task Name</th>
                        <th rowspan="2">FileName</th>
                    </tr>
                    <tr>
                        <th data-orderable="false">First</th>
                        <th data-orderable="false">Last</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- JavaScript will populate table body here -->
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
    </div>

      <!-- The additional DAILY popup for details - all entries for that week for that employee-->
    <div id="detailsModalDaily" class="modal fade" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="detailsModalLabelDaily">All Entries For Week</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <!-- Content of the modal goes here -->
              <table id="additionalDetailsTableDaily" class="table" style="width:100%">
                <thead class="text-center">
                    <tr>
                        <th rowspan="2">Date</th>
                        <th colspan="2">Name</th>
                        <th rowspan="2">Work Order #</th>
                        <th rowspan="2">Customer Name</th>
                        <th colspan="3">Reported Hours</th>
                        <th rowspan="2">Notes</th>
                        <th rowspan="2">Error</th>
                    </tr>
                    <tr>
                        <th>First</th>
                        <th>Last</th>
                        <th>ADP</th>
                        <th>Emp.</th>
                        <th>Cust.</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- JavaScript will populate table body here -->
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
    </div>

    <!-- Popup for user to select the correct approval hours if there's an error. -->
    <div class="modal fade" id="hoursSelectionModal" tabindex="-1" aria-labelledby="hoursSelectionModalLabel" aria-hidden="true">
        <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <div class="container">
                    <h4 class="modal-title" id="hoursSelectionModalLabel">Select Approved Hours</h4>
                    <h5 class="fst-italic">(Customer Hours Not Available For Selection)</h5>
                </div>
                <button type="button" class="close btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            <!-- Radio buttons for selecting hours will be dynamically added here -->
            <form id="hoursSelectionForm">
                <!-- Radio buttons will be inserted here by JavaScript -->
            </form>
            </div>
            <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-primary" id="saveSelectedHours" onclick="hoursSelected()">Save Changes</button>
            </div>
        </div>
        </div>
    </div>
{% endblock %}