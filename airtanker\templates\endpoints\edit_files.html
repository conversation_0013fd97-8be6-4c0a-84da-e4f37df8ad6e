{% extends 'base.html' %}

{% block styles %}
<link rel="stylesheet"
  href="https://cdn.datatables.net/fixedcolumns/5.0.3/css/fixedColumns.dataTables.css">
<link rel="stylesheet"
  href="https://cdn.datatables.net/select/2.1.0/css/select.dataTables.css">
<style>
  input[type="checkbox"] {
    transform: scale(1.5);
    margin-left: 10px;
  }
  .file-upload-wrapper {
    border: 2px dashed #91b0b3;
    border-radius: 10px;
    padding: 10px;
    text-align: center;
    cursor: pointer;
    position: relative;
  }
  .file-upload-wrapper:hover {
    background-color: #f3f4f6;
  }
  .file-upload-wrapper i {
    color: #5cb85c;
  }
  #browse-btn {
    color: blue;
    text-decoration: underline;
    cursor: pointer;
  }
  .buttons-excel {
    margin: 0 auto;
  }
  #detailsModal .modal-dialog {
    max-width: 90%;
  }
  #detailsModal .modal-body {
    overflow-x: auto;
  }
  .list-group-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .list-group-item .fa-times {
    color: red;
    cursor: pointer;
  }
  /* Hide DataTables controls */
  .employeeDetailsTable_info,
  .employeeDetailsTable_filter,
  .employeeDetailsTable_length,
  .employeeDetailsTable_paginate,
  .employeeWeeklyDetailsTable_info,
  .employeeWeeklyDetailsTable_filter,
  .employeeWeeklyDetailsTable_length,
  .employeeWeeklyDetailsTable_paginate {
    display: none;
  }
</style>
{% endblock %}

{% block scripts %}
<script
  src="https://cdn.datatables.net/fixedcolumns/5.0.3/js/dataTables.fixedColumns.js">
</script>
<script
  src="https://cdn.datatables.net/select/2.1.0/js/dataTables.select.js">
</script>
<script src="{{ url_for('static', filename='js/edit_files.js') }}"></script>
{% endblock %}

{% block content %}
{% with messages = get_flashed_messages(with_categories=true) %}
  {% if messages %}
    <div id="flash-message-container">
      {% for category, message in messages %}
        <div class="alert alert-{{ category }}">{{ message }}</div>
      {% endfor %}
    </div>
  {% endif %}
{% endwith %}

<h3 class="text-center">Edit Files</h3>
<div class="d-flex justify-content-center my-2">
    <button
    id="openDeleteModal"
    class="btn btn-secondary mt-1"
    disabled
    data-bs-toggle="modal"
    data-bs-target="#confirmDeleteModal"
  >
    Delete Files
  </button>
</div>

<div class="row justify-content-center">
  <div class="col-auto">
    <table
      id="filesTable"
      class="table table-hover"
      style="display: block; width: 100%; margin-top: 20px;"
    >
      <thead>
        <tr>
          <th></th>
          <th style="width: 42px">File ID</th>
          <th style="width: 420px">File Name</th>
          <th style="width: 85px">Sheet Name</th>
          <th style="width: 70px">Source Type</th>
          <th style="width: 200px">Date Processed</th>
        </tr>
      </thead>
      <tbody></tbody>
    </table>
  </div>
</div>
{% endblock %}

{% block modal %}
<div
  class="modal fade"
  id="confirmDeleteModal"
  tabindex="-1"
  aria-labelledby="confirmDeleteModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5
          class="modal-title"
          id="confirmDeleteModalLabel"
        >
          Confirm Deletion
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to delete the following files?</p>
        <ul id="fileListToDelete"></ul>
      </div>
      <div class="modal-footer">
        <button
          type="button"
          class="btn btn-secondary"
          data-bs-dismiss="modal"
        >
          Cancel
        </button>
        <button
          type="button"
          class="btn btn-danger"
          id="deleteConfirmed"
        >
          <span id="deleteButtonText">Delete</span>
          <span
            id="deleteSpinner"
            class="spinner-border spinner-border-sm ms-1"
            role="status"
            style="display: none;"
          >
            <span class="visually-hidden">Loading...</span>
          </span>
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %}
