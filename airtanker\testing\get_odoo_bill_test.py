import xmlrpc.client
from datetime import datetime, timedelta
import pprint
import os
from dotenv import load_dotenv

class OdooAPI:
    def __init__(self):
        load_dotenv()
        self.url = os.getenv('ODOO_URL')
        self.db = os.getenv('ODOO_DB')

        self.username = os.getenv("ODOO_USER")
        self.password = os.getenv("ODOO_PASS")

        common = xmlrpc.client.ServerProxy('{}/xmlrpc/2/common'.format(self.url))
        self.uid = common.authenticate(self.db, self.username, self.password, {})


    def get_recent_bills(self):
        common = xmlrpc.client.ServerProxy('{}/xmlrpc/2/common'.format(self.url))
        uid = common.authenticate(self.db, self.username, self.password, {})

        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))

        # Search for the most recent draft bills
        bill_ids = models.execute_kw(self.db, self.uid, self.password, 'account.move', 'search', [
            [('move_type', '=', 'in_invoice'), ('state', '=', 'draft')],  # Filtering for incoming bills in draft state
            0, 3, 'create_date desc'  # Limit to 10 most recent bills and sort by creation date descending
        ])
        
        if not bill_ids:
            return "No draft bills found."

        # Read details of these bills
        bills = models.execute_kw(self.db, self.uid, self.password, 'account.move', 'read', [bill_ids, ['name', 'partner_id', 'amount_total', 'create_date']])
        
        return bills


    def get_recent_bills_with_line_items(self):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        
        # Search for the most recent draft bills
        bill_ids = models.execute_kw(self.db, self.uid, self.password, 'account.move', 'search', [
            [('move_type', '=', 'in_invoice'), ('state', '=', 'draft')],
            0, 1, 'create_date desc'
        ])
        
        if not bill_ids:
            return "No draft bills found."

        # Read details of these bills including their line items
        bills = models.execute_kw(self.db, self.uid, self.password, 'account.move', 'read', [bill_ids, ['name', 'partner_id', 'amount_total', 'invoice_line_ids', 'create_date']])
        
        # Fetch details for each line item associated with the bills
        for bill in bills:
            line_ids = bill.get('invoice_line_ids', [])
            line_items = models.execute_kw(self.db, self.uid, self.password, 'account.move.line', 'read', [line_ids, ['product_id', 'quantity', 'price_unit', 'name', 'tax_ids']])
            bill['line_items'] = line_items
        
        return bills
    

    def pretty_print_bills(self, bills):
        for bill in bills:
            print(f"Bill Name: {bill['name']}")
            print(f"Partner Name: {bill['partner_id'][1]}")
            print(f"Total Amount: {bill['amount_total']}")
            print(f"Date: {bill['create_date']}")
            print("Line Items:")
            for item in bill['line_items']:
                if item['product_id']: # print each line
                    print(f"  - Line Item Name: {item['name']} | Quantity: {item['quantity']} | Price: {item['price_unit']}")
                else:
                    # print employee name
                    print("\n" + "-"*40 + "\n")
                    print(f"  - Contractor Name: {item['name']}")
            print("\n" + "-"*40 + "\n")

        
    def get_recent_invoices_with_line_items(self):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        
        # Search for the most recent draft invoices
        invoice_ids = models.execute_kw(self.db, self.uid, self.password, 'account.move', 'search', [
            [('move_type', '=', 'out_invoice'), ('state', '=', 'draft')],
            0, 5, 'create_date desc'  # Adjust the number of invoices you want to fetch
        ])
        
        if not invoice_ids:
            return "No recent invoices found."

        # Read details of these invoices including their line items
        invoices = models.execute_kw(self.db, self.uid, self.password, 'account.move', 'read', [invoice_ids, ['x_studio_message_on_invoice', 'partner_id', 'amount_total', 'invoice_line_ids', 'create_date']])
        
        # Fetch details for each line item associated with the invoices
        for invoice in invoices:
            line_ids = invoice.get('invoice_line_ids', [])
            line_items = models.execute_kw(self.db, self.uid, self.password, 'account.move.line', 'read', [line_ids, ['product_id', 'quantity', 'price_unit', 'name', 'tax_ids']])
            invoice['line_items'] = line_items
        
        return invoices
    

    def pretty_print_invoices(self, invoices):
        if isinstance(invoices, str):
            print(invoices)  # In case the returned is a string message
            return
        print('\n')
        for invoice in invoices:
            print(f"Invoice Name: {invoice['x_studio_message_on_invoice']}")
            print(f"Partner Name: {invoice['partner_id'][1]}")  # Adjust if partner_id is a list
            print(f"Total Amount: {invoice['amount_total']}")
            print(f"Date: {invoice['create_date']}")
            print("Line Items:")
            for item in invoice['line_items']:
                if item['product_id']: # print each line
                    print(f"  - Line Item Name: {item['name']} | Quantity: {item['quantity']} | Price: {item['price_unit']}")
                else:
                    # print employee name
                    print("\n" + "-"*40 + "\n")
                    print(f"  - Contractor Name: {item['name']}")
            print("-" * 40 + "\n")

    def create_bill_return_id(self, sow_contact_id, bill_name, week_ending):        
        companyID = 3 # Services
        week_ending_date = datetime.strptime(week_ending, '%Y%m%d').strftime('%Y-%m-%d')
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        
        bill_data = {
            'name': bill_name,
            'move_type': 'in_invoice',
            'company_id': companyID,
            'invoice_date': week_ending_date,
            'x_studio_service_date': week_ending_date,
            'create_date': week_ending_date,
            'date': week_ending_date
        }
        
        if isinstance(sow_contact_id, int):
            bill_data['partner_id'] = sow_contact_id
        
        writeBillID = models.execute_kw(self.db, self.uid, self.password, 'account.move', 'create', [bill_data])
        
        return writeBillID


    def get_initials_from_contact_id(self, contact_id):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))

        # Fetch the employee's name based on the contact ID
        name = models.execute_kw(self.db, self.uid, self.password,
                                    'res.partner', 'read', [contact_id], {'fields': ['name']})

        if not name:
            return None

        full_name = name[0].get('name')

        if not full_name:
            return None

        # Check if the full name looks like an email address
        if '@' in full_name:
            # Split the email address to get the initials
            local_part = full_name.split('@')[0]
            email_parts = local_part.split('.')
            initials = ''.join([part[0].upper() for part in email_parts if part])
        else:
            # Split the full name into parts
            name_parts = full_name.split()
            # Extract initials from the name parts
            initials = ''.join([part[0].upper() for part in name_parts if part])

        return initials
    

    def get_products(self):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))

        # Fetch all products
        product_ids = models.execute_kw(self.db, self.uid, self.password,
                                             'product.product', 'search', [[]])

        # Read the name and ID of each product
        products = models.execute_kw(self.db, self.uid, self.password,
                                          'product.product', 'read', [product_ids], {'fields': ['id', 'name']})

        return products

    def get_uoms(self):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))

        # Fetch all UoMs
        uom_ids = models.execute_kw(self.db, self.uid, self.password,
                                         'uom.uom', 'search', [[]])

        # Read the name and ID of each UoM
        uoms = models.execute_kw(self.db, self.uid, self.password,
                                      'uom.uom', 'read', [uom_ids], {'fields': ['id', 'name']})

        return uoms

# Example usage
api = OdooAPI()
#recent_bills = api.get_recent_bills()
# recent_bills = api.get_recent_bills_with_line_items()
# recent_invoices = api.get_recent_invoices_with_line_items()

# api.pretty_print_invoices(recent_invoices)
#api.pretty_print_bills(recent_bills)
bill_id = api.get_uoms()
print('\n')
for pro in bill_id:
    print("ProductID:", pro['id'], "-----", "ProductName:", pro['name'])
#print(recent_bills)
print('Done.')