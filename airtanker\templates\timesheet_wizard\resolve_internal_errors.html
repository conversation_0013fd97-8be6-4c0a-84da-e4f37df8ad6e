<!DOCTYPE html>
<html lang="en" data-theme="emerald">
    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <link rel="icon" href="{{ url_for('static', filename='assets/favicon-32x32.png') }}" type="image/png">

        <!-- Font <PERSON> (Optional, for icons not provided by DaisyUI/SVG) -->
        <link href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" rel="stylesheet">

        <!-- ↓↓↓ Tailwind + DaisyUI CDN ↓↓↓ -->
        <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio,line-clamp"></script>
        <link href="https://cdn.jsdelivr.net/npm/daisyui@4.10.1/dist/full.min.css" rel="stylesheet" type="text/css" />
        <script>
        tailwind.config = {
            theme: {
                extend: { // Extend Tailwind theme if needed
                }
            },
            daisyui: { // Using emerald theme at the moment
                themes: [
                    {
                        atomtechlight: {
                            "color-scheme": "light",
                            "base-100": "oklch(96% 0.005 280)",
                            "base-200": "oklch(92% 0.004 280)",
                            "base-300": "oklch(88% 0.004 280)",
                            "base-content": "oklch(18% 0.02 280)",
                            "primary": "oklch(72% 0.28 60)",
                            "primary-content": "oklch(10% 0.02 60)",
                            "secondary": "oklch(70% 0.03 280)",
                            "secondary-content": "oklch(18% 0.015 280)",
                            "accent": "oklch(82% 0.22 30)",
                            "accent-content": "oklch(10% 0.015 30)",
                            "neutral": "oklch(50% 0.02 280)",
                            "neutral-content": "oklch(98% 0.005 270)",
                            "info": "oklch(75% 0.1 240)",
                            "info-content": "oklch(25% 0.02 240)",
                            "success": "oklch(80% 0.2 135)",
                            "success-content": "oklch(20% 0.05 135)",
                            "warning": "oklch(85% 0.22 85)",
                            "warning-content": "oklch(25% 0.08 85)",
                            "error": "oklch(70% 0.22 25)",
                            "error-content": "oklch(25% 0.1 25)",
                            "--rounded-box": "1rem",
                            "--rounded-btn": "1rem",
                            "--rounded-badge": "1rem",
                            "--animation-btn": "0.25s",
                            "--animation-input": "0.2s",
                            "--btn-focus-scale": "0.95",
                            "--border-btn": "1px",
                            "--tab-border": "1px",
                            "--tab-radius": "0.5rem",
                        }
                    },
                    {
                        atomtechdark: {
                            "color-scheme": "dark",
                            "base-100": "oklch(18% 0.02 280)",
                            "base-200": "oklch(25% 0.02 280)",
                            "base-300": "oklch(30% 0.02 280)",
                            "base-content": "oklch(95% 0.005 270)",
                            "primary": "oklch(70% 0.28 60)",
                            "primary-content": "oklch(15% 0.02 60)",
                            "secondary": "oklch(85% 0.03 280)",
                            "secondary-content": "oklch(25% 0.01 280)",
                            "accent": "oklch(75% 0.25 30)",
                            "accent-content": "oklch(15% 0.02 30)",
                            "neutral": "oklch(40% 0.02 280)",
                            "neutral-content": "oklch(95% 0.01 270)",
                            "info": "oklch(75% 0.1 240)",
                            "info-content": "oklch(25% 0.02 240)",
                            "success": "oklch(80% 0.2 135)",
                            "success-content": "oklch(20% 0.05 135)",
                            "warning": "oklch(85% 0.22 85)",
                            "warning-content": "oklch(25% 0.08 85)",
                            "error": "oklch(70% 0.22 25)",
                            "error-content": "oklch(25% 0.1 25)",
                            "--rounded-box": "1rem",
                            "--rounded-btn": "1rem",
                            "--rounded-badge": "1rem",
                            "--animation-btn": "0.25s",
                            "--animation-input": "0.2s",
                            "--btn-focus-scale": "0.95",
                            "--border-btn": "1px",
                            "--tab-border": "1px",
                            "--tab-radius": "0.5rem",
                        }
                    },
                    "light",
                    "dark",
                    "autumn"
                ],
            },
        }
        </script>
        <!-- ↑↑↑ END Tailwind + DaisyUI CDN ↑↑↑ -->

        <!-- Custom Styles (Keep minimal necessary styles) -->
        <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/styles_customer.css') }}"  />

        <style>
            /* Flash Message Styling & Animation */
            .flash-message-container {
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                z-index: 1000;
                width: max-content;
                max-width: 90%;
            }

            .flash-message {
                display: none;
                opacity: 0;
            }

            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(-20px); }
                to { opacity: 1; transform: translateY(0); }
            }

            @keyframes fadeOut {
                from { opacity: 1; transform: translateY(0); }
                to { opacity: 0; transform: translateY(-20px); }
            }

            tr.row-skipped select {
                opacity: 0.6;
                pointer-events: none;
                background-color: #f3f4f6;
            }
             tr.row-skipped button.toggle-skip-btn {
                 /* Optional: Style skip button differently when skipped */
             }

            .needs-selection {
                border-color: red !important;
                box-shadow: 0 0 0 2px rgba(255, 0, 0, 0.5);
            }

            #errorsTable thead th {
                padding-top: 1rem !important;
                padding-bottom: 1rem !important;
                height: 3.5rem !important;
                vertical-align: middle;
                font-size: .875rem;
            }

            #errorsTable thead {
                position: sticky;
                top: 0;
                z-index: 10; /* Ensure header stays above scrolling content */
            }

            /* Ensure body takes full height */
            html, body {
                height: 100%;
                margin: 0;
            }
            body {
                display: flex; /* Make body a flex container */
                flex-direction: column; /* Stack children vertically */
            }
            .main-content-container {
                 flex-grow: 1; /* Allow container to grow */
                 display: flex; /* Make it a flex container */
                 /* padding is handled by body/container classes */
            }

        </style>

        <title>Review TimeSheet Errors</title>
    </head>

    <body class="bg-base-200 min-h-screen p-4 md:p-8"> <!-- DaisyUI background -->

        <!-- Flash Message Container -->
        <div class="flash-message-container">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <!-- Using DaisyUI Alert -->
                        <div role="alert" class="flash-message alert alert-error shadow-lg mb-2"> <!-- Default to error, adjust category mapping if needed -->
                             <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                            <span>{{ message }}</span>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
        </div>

        {% from "includes/_formhelpers.html" import render_field %} <!-- Keep if used -->

        <!-- MODIFIED: Added flex flex-col, removed inline style -->
        <div class="container mx-auto bg-base-100 p-6 md:p-8 rounded-lg shadow-xl flex flex-col h-full">

            <!-- NEW: Wrapper div for content above buttons, added flex-grow -->
            <div class="flex-grow flex flex-col min-h-0"> <!-- Added flex flex-col min-h-0 here -->

                <div class="text-center mb-6 flex-shrink-0"> <!-- Added flex-shrink-0 -->
                    <h2 class="text-3xl font-bold mb-2 text-error">Errors Found in Internal TimeSheets</h2>
                    <p class="text-base-content/70">Please review the details below and make selections where necessary.</p>
                </div>

                <!-- MODIFIED: Added overflow-y-auto and flex-grow -->
                <div class="overflow-x-auto overflow-y-auto mb-8 flex-grow" style="border-radius: 0.5rem; scrollbar-width: none;">
                    <table class="table table-zebra w-full" id="errorsTable">
                        <!-- head -->
                        <thead class="bg-accent text-accent-content">
                            <tr>
                                <th>Employee</th>
                                <th>Message</th>
                                <th>FileName(s)</th>
                                <th>Dates</th>
                                <th>Work Order</th>
                                <th class="text-center">Skip Update?</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Dynamic error rows will be inserted here by JavaScript -->
                            <!-- Example Placeholder Row (Remove in production) -->
                            <!--
                            <tr data-id="placeholder-1">
                                <td>John Doe</td>
                                <td>Missing Work Order</td>
                                <td>timesheet_john_doe.xlsx</td>
                                <td>04-15-2025, 04-16-2025</td>
                                <td>
                                    <select class="select select-bordered select-sm w-full max-w-xs workOrderDropdown">
                                        <option disabled selected>Choose one</option>
                                        <optgroup label="Project A">
                                            <option value="wo1">WO-001</option>
                                            <option value="wo2">WO-002</option>
                                        </optgroup>
                                    </select>
                                </td>
                                <td class="text-center">
                                    <button class="btn btn-xs toggle-skip-btn">No</button>
                                </td>
                            </tr>
                             -->
                        </tbody>
                    </table>
                </div>
            </div> <!-- End NEW wrapper div -->


            <!-- Action Buttons -->
            <div class="flex justify-between items-center gap-4 flex-shrink-0">
                <button type="button" onclick="handleAction(true); return false;" class="btn btn-neutral hover:btn-error"> <!-- Changed to btn-secondary, hover:btn-error for cancel -->
                    Cancel Process
                </button>
                <button type="button" onclick="submitUpdates();" class="btn btn-primary">
                    Continue
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="inline-block ml-2">
                        <path d="M9 6L15 12L9 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
        </div>
        <!-- End main content container -->

        <!-- DaisyUI Modal for Cancellation Confirmation -->
        <dialog id="confirmCancelModal" class="modal modal-bottom sm:modal-middle">
            <div class="modal-box">
                <h3 class="font-bold text-lg">Confirm Cancellation</h3>
                <p class="py-4">Are you sure you want to cancel? This will attempt to delete the associated timesheet files from the processing stage.</p>
                <div role="alert" class="alert alert-warning mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
                    <span>Warning: This action cannot be undone.</span>
                </div>
                <div class="modal-action">
                    <form method="dialog" class="flex gap-2">
                        <!-- if there is a button in form, it will close the modal -->
                        <button class="btn">Go Back</button>
                         <button type="button" class="btn btn-error" id="cancelConfirmed">
                            <span id="cancelButtonText">Yes, Cancel Process</span>
                            <!-- DaisyUI Spinner -->
                            <span id="cancelSpinner" class="loading loading-spinner loading-sm ml-1" style="display: none;"></span>
                        </button>
                    </form>
                </div>
            </div>
             <form method="dialog" class="modal-backdrop">
                <button>close</button> <!-- Closes modal on backdrop click -->
            </form>
        </dialog>


        <!-- Keep jQuery Slim if needed by other scripts, otherwise consider removing -->
        <script src="https://code.jquery.com/jquery-3.2.1.slim.min.js"
                integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN"
                crossorigin="anonymous"></script>

        <script>
            // --- Flash Message Logic ---
            document.addEventListener("DOMContentLoaded", function() {
                var flashMessages = document.querySelectorAll('.flash-message');
                flashMessages.forEach(function(flashMessage, index) {
                    // Stagger the animation slightly if multiple messages
                    setTimeout(() => {
                        flashMessage.style.display = 'block';
                        flashMessage.style.animation = 'fadeIn 0.5s ease-out forwards';

                        setTimeout(function() {
                            flashMessage.style.animation = 'fadeOut 1s ease-in forwards';
                            setTimeout(function() {
                                flashMessage.style.display = 'none';
                            }, 1000); // Match fadeOut duration
                        }, 4000); // Visible duration
                    }, index * 100); // Stagger start time
                });
            });

            // --- Utility Functions ---
            function formatDate(dateString) {
                if (!dateString) return '';
                try {
                    const date = new Date(dateString);
                    // Add timezone offset to prevent date shifting
                    const offset = date.getTimezoneOffset();
                    const correctedDate = new Date(date.getTime() + (offset * 60 * 1000));
                    return correctedDate.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                    }).replace(/\//g, '-');
                } catch (e) {
                    console.error("Error formatting date:", dateString, e);
                    return 'Invalid Date';
                }
            }

            function isEmpty(obj) {
                return obj === null || obj === undefined || Object.keys(obj).length === 0;
            }

            // --- Error Handling & Row Creation ---
            const specificMessages = [
                "This file has already been processed. Delete the sheet at '/edit-files' to re-parse.",
                "This file had formulas in cell where actual hours were expected. Please correct this and reupload.",
                "These files had 'Your Name' in the name field. Please correct this and reupload."
            ];
            let messageToRowMap = {}; // Maps specific message content to the table row element

            document.addEventListener('DOMContentLoaded', function() {
                const errorsTableBody = document.querySelector('#errorsTable tbody');
                if (!errorsTableBody) {
                    console.error("Error: Table body #errorsTable tbody not found!");
                    return;
                }

                const errorsJSON = sessionStorage.getItem('errors');
                const errors = JSON.parse(errorsJSON || '[]'); // Default to empty array

                const name_errorsJSON = sessionStorage.getItem('name_errors');
                const name_errors = JSON.parse(name_errorsJSON || '[]'); // Default to empty array

                // Process regular errors
                errors.forEach(error => {
                    if (specificMessages.includes(error.Message)) {
                        const existingRow = messageToRowMap[error.Message];
                        if (existingRow) {
                            // Append filename to the existing row's filename cell
                            const fileNameCell = existingRow.querySelector('.file-name-cell');
                            if (fileNameCell) {
                                const newFileNameDiv = document.createElement('div');
                                newFileNameDiv.textContent = error.FileName;
                                fileNameCell.appendChild(newFileNameDiv);
                            }
                        } else {
                            // Create a new row and store reference
                            const newRow = createErrorRow(error, errorsTableBody);
                            if (newRow) {
                                messageToRowMap[error.Message] = newRow;
                            }
                        }
                    } else {
                        // Create a new row normally
                        createErrorRow(error, errorsTableBody);
                    }
                });

                // Process name errors
                name_errors.forEach(name_error => {
                     if (specificMessages.includes(name_error.Message)) {
                        const existingRow = messageToRowMap[name_error.Message];
                        if (existingRow) {
                            // Append filename to the existing row's filename cell
                            const fileNameCell = existingRow.querySelector('.file-name-cell');
                             if (fileNameCell) {
                                const newFileNameDiv = document.createElement('div');
                                newFileNameDiv.textContent = name_error.FileName;
                                fileNameCell.appendChild(newFileNameDiv);
                            }
                        } else {
                            // Create a new row and store reference
                            const newRow = createNameErrorRow(name_error, errorsTableBody);
                             if (newRow) {
                                messageToRowMap[name_error.Message] = newRow;
                            }
                        }
                    } else {
                        // Create a new row normally
                        createNameErrorRow(name_error, errorsTableBody);
                    }
                });

                 // Initialize employee dropdown listeners after all rows are potentially created
                initializeEmployeeDropdownListeners();
            });

            function createNameErrorRow(name_error, tableBody){
                const row = document.createElement('tr');
                row.setAttribute('data-id', name_error.ID); // Use data-id for identification

                // --- Employee Dropdown Cell ---
                const employeeCell = document.createElement('td');
                const employeeSelect = document.createElement('select');
                // Added default disabled option
                const defaultEmpOption = document.createElement('option');
                defaultEmpOption.textContent = "Select Employee";
                defaultEmpOption.disabled = true;
                defaultEmpOption.selected = true;
                employeeSelect.appendChild(defaultEmpOption);

                employeeSelect.className = 'select select-bordered select-sm w-full max-w-xs employee_dropdown';
                if (name_error.EmployeeData && Array.isArray(name_error.EmployeeData)) {
                    name_error.EmployeeData.forEach(emp_data => {
                        const option = document.createElement('option');
                        option.value = emp_data.EmployeeID; // Use EmployeeID as value
                        option.textContent = emp_data.EmployeeName;
                        // Store the WorkOrders as stringified JSON in a data attribute
                        option.setAttribute('data-workorders', JSON.stringify(emp_data.WorkOrders || {})); // Ensure it's always an object
                        employeeSelect.appendChild(option);
                    });
                } else {
                     console.warn("EmployeeData is missing or not an array for name_error ID:", name_error.ID);
                }
                employeeCell.appendChild(employeeSelect);

                // --- Message Cell ---
                const messageCell = document.createElement('td');
                messageCell.innerHTML = name_error.Message; // Use innerHTML if message contains HTML

                // --- FileName Cell ---
                const fileNameCell = document.createElement('td');
                fileNameCell.className = 'file-name-cell'; // Add class for potential targeting
                const initialFileNameDiv = document.createElement('div');
                initialFileNameDiv.textContent = name_error.FileName;
                fileNameCell.appendChild(initialFileNameDiv);

                // --- Dates Cell ---
                const datesCell = document.createElement('td');
                const html = (name_error.Hours || [])
                .map(hour => {
                    const d = formatDate(hour.Date);
                    return `<span style="white-space:nowrap">${d}</span>`;
                })
                .join('<br/>');
                datesCell.innerHTML = html;

                // --- Work Order Dropdown Cell ---
                const workOrderCell = document.createElement('td');
                const workOrderSelect = document.createElement('select');
                workOrderSelect.className = 'select select-bordered select-sm w-full max-w-xs workOrderDropdown';
                workOrderSelect.id = `workOrderSelect-${name_error.ID}`; // Unique ID
                // Add a default disabled option
                const defaultOption = document.createElement('option');
                defaultOption.textContent = "Select WO";
                defaultOption.disabled = true;
                defaultOption.selected = true;
                defaultOption.value = ""; // Important for validation
                workOrderSelect.appendChild(defaultOption);
                workOrderCell.appendChild(workOrderSelect);

                // --- Skip Toggle Cell ---
                const toggleCell = document.createElement('td');
                toggleCell.className = 'text-center';
                const toggleButton = document.createElement('button');
                toggleButton.className = 'btn btn-xs toggle-skip-btn'; // DaisyUI button classes
                toggleButton.textContent = 'No';
                toggleButton.addEventListener('click', function() {
                    const isSkipped = row.classList.toggle('row-skipped'); // Use a dedicated class
                    toggleButton.textContent = isSkipped ? 'Yes' : 'No';
                    toggleButton.classList.toggle('btn-active', isSkipped); // Optional: visual cue
                    toggleButton.classList.toggle('btn-ghost', !isSkipped); // Optional: visual cue

                    // Disable/enable the work order select visually and functionally
                    workOrderSelect.disabled = isSkipped;
                    // Optionally hide, but disabling is often clearer
                    // workOrderSelect.classList.toggle('hidden', isSkipped);
                });
                toggleCell.appendChild(toggleButton);

                // --- Append cells to row ---
                row.appendChild(employeeCell);
                row.appendChild(messageCell);
                row.appendChild(fileNameCell);
                row.appendChild(datesCell);
                row.appendChild(workOrderCell);
                row.appendChild(toggleCell);

                // --- Append row to table body ---
                tableBody.appendChild(row);
                return row; // Return the created row element
            }


            function createErrorRow(error, tableBody){
                const row = document.createElement('tr');
                row.setAttribute('data-id', error.ID);

                // --- Employee Cell ---
                const employeeCell = document.createElement('td');
                employeeCell.textContent = error.Employee || 'N/A'; // Handle potential missing employee

                // --- Message Cell ---
                const messageCell = document.createElement('td');
                messageCell.innerHTML = error.Message;

                // --- FileName Cell ---
                const fileNameCell = document.createElement('td');
                fileNameCell.className = 'file-name-cell';
                const initialFileNameDiv = document.createElement('div');
                initialFileNameDiv.textContent = error.FileName;
                fileNameCell.appendChild(initialFileNameDiv);

                // --- Dates Cell ---
                const datesCell = document.createElement('td');
                const html = (error.Hours || [])
                .map(hour => {
                    const d = formatDate(hour.Date);
                    return `<span style="white-space:nowrap">${d}</span>`;
                })
                .join('<br/>');
                datesCell.innerHTML = html;

                // --- Work Order Dropdown Cell ---
                const workOrderCell = document.createElement('td');
                let workOrderSelect = null; // Initialize select variable

                if (!isEmpty(error.WorkOrderEntry)) {
                    workOrderSelect = document.createElement('select');
                    workOrderSelect.className = 'select select-bordered select-sm w-full max-w-xs workOrderDropdown';
                    workOrderSelect.id = `workOrderSelect-${error.ID}`; // Unique ID

                    // Add a default disabled option
                    const defaultOption = document.createElement('option');
                    defaultOption.textContent = "Select WO";
                    defaultOption.disabled = true;
                    defaultOption.selected = true;
                    defaultOption.value = ""; // Important for validation
                    workOrderSelect.appendChild(defaultOption);

                    Object.keys(error.WorkOrderEntry).forEach(projectNumber => {
                        const optgroup = document.createElement('optgroup');
                        optgroup.label = projectNumber;
                        if (Array.isArray(error.WorkOrderEntry[projectNumber])) {
                            error.WorkOrderEntry[projectNumber].forEach(workOrder => {
                                // Assuming workOrder is an object like { 'workOrderId': 'workOrderNumber' }
                                const workOrderId = Object.keys(workOrder)[0];
                                const workOrderNumber = workOrder[workOrderId];
                                if (workOrderId && workOrderNumber) {
                                    const option = document.createElement('option');
                                    option.value = workOrderId;
                                    option.textContent = workOrderNumber;
                                    optgroup.appendChild(option);
                                }
                            });
                        }
                        // MODIFIED: Changed 'select' to 'workOrderSelect'
                        if (optgroup.childElementCount > 0) { // Only add optgroup if it has options
                           workOrderSelect.appendChild(optgroup);
                        }
                    });
                     workOrderCell.appendChild(workOrderSelect);
                } else {
                    // If no work orders, display placeholder text or leave empty
                    workOrderCell.textContent = 'N/A'; // Or leave empty: workOrderCell.textContent = '';
                }


                // --- Skip Toggle Cell ---
                const toggleCell = document.createElement('td');
                toggleCell.className = 'text-center';
                const toggleButton = document.createElement('button');
                toggleButton.className = 'btn btn-xs toggle-skip-btn';
                toggleButton.textContent = 'No';

                // Only add toggle functionality if there's a dropdown to toggle
                if (workOrderSelect) {
                    toggleButton.addEventListener('click', function() {
                        const isSkipped = row.classList.toggle('row-skipped');
                        toggleButton.textContent = isSkipped ? 'Yes' : 'No';
                        toggleButton.classList.toggle('btn-active', isSkipped);
                        toggleButton.classList.toggle('btn-ghost', !isSkipped);

                        workOrderSelect.disabled = isSkipped;
                        // workOrderSelect.classList.toggle('hidden', isSkipped); // Alternative: hide
                    });
                } else {
                    // If no dropdown, disable the button or hide it
                    toggleButton.disabled = true;
                    toggleButton.textContent = 'N/A'; // Indicate no action needed/possible
                    // Or hide completely: toggleButton.style.display = 'none';
                }
                toggleCell.appendChild(toggleButton);


                // --- Append cells to row ---
                row.appendChild(employeeCell);
                row.appendChild(messageCell);
                row.appendChild(fileNameCell);
                row.appendChild(datesCell);
                row.appendChild(workOrderCell);
                row.appendChild(toggleCell);

                // --- Append row to table body ---
                tableBody.appendChild(row);
                return row; // Return the created row element
            }

            // --- Dropdown Population Logic ---
            function initializeEmployeeDropdownListeners() {
                const employeeSelects = document.querySelectorAll('.employee_dropdown');

                employeeSelects.forEach((selectElement) => {
                    // Use 'change' event instead of 'click' for selects
                    selectElement.addEventListener('change', function() {
                        const selectedOption = this.options[this.selectedIndex];
                        if (!selectedOption || !selectedOption.getAttribute('data-workorders')) {
                             console.warn("Selected option or workorders data missing for", this);
                            return; // Exit if no valid option selected or data missing
                        }

                        const workOrdersData = selectedOption.getAttribute('data-workorders');
                        let workOrders = {};
                        try {
                             workOrders = JSON.parse(workOrdersData || '{}');
                        } catch (e) {
                            console.error("Failed to parse workorders JSON:", workOrdersData, e);
                            return; // Exit if JSON is invalid
                        }


                        // Find the corresponding work order select within the same table row (tr)
                        const parentRow = this.closest('tr');
                        if (!parentRow) {
                            console.error("Could not find parent row for employee select:", this);
                            return;
                        }
                        const workOrderDropdown = parentRow.querySelector('.workOrderDropdown');
                         if (!workOrderDropdown) {
                            console.error("Could not find workOrderDropdown in the same row for:", this);
                            return;
                        }

                        // Clear existing options except the default placeholder
                        workOrderDropdown.innerHTML = ''; // Clear completely first
                        const defaultOption = document.createElement('option');
                        defaultOption.textContent = "Select WO";
                        defaultOption.disabled = true;
                        defaultOption.selected = true;
                        defaultOption.value = "";
                        workOrderDropdown.appendChild(defaultOption);


                        // Populate with new work orders
                        Object.entries(workOrders).forEach(([projectNumber, workOrdersArray]) => {
                            if (projectNumber && Array.isArray(workOrdersArray) && workOrdersArray.length > 0) {
                                const optgroup = document.createElement('optgroup');
                                optgroup.label = projectNumber;

                                workOrdersArray.forEach(workOrder => {
                                    // Assuming workOrder is { 'workOrderID': 'workOrderName' }
                                    Object.entries(workOrder).forEach(([workOrderID, workOrderName]) => {
                                        if (workOrderID && workOrderName) {
                                            const option = document.createElement('option');
                                            option.value = workOrderID;
                                            option.textContent = workOrderName;
                                            optgroup.appendChild(option);
                                        }
                                    });
                                });
                                 if (optgroup.childElementCount > 0) {
                                    workOrderDropdown.appendChild(optgroup);
                                }
                            }
                        });
                         // Reset skip state if employee changes
                        const skipButton = parentRow.querySelector('.toggle-skip-btn');
                        if (parentRow.classList.contains('row-skipped')) {
                            parentRow.classList.remove('row-skipped');
                            workOrderDropdown.disabled = false;
                            if (skipButton) {
                                skipButton.textContent = 'No';
                                skipButton.classList.remove('btn-active');
                                skipButton.classList.add('btn-ghost');
                            }
                        }
                        workOrderDropdown.disabled = false; // Ensure it's enabled
                    });
                });
            }


            // --- Actions (Cancel/Submit) ---
            function handleAction(cancel = false) {
                if (cancel) {
                    // Show the DaisyUI modal
                    const modal = document.getElementById('confirmCancelModal');
                    if (modal && typeof modal.showModal === 'function') {
                        modal.showModal();
                    } else {
                        console.error("Cancel modal not found or not initialized correctly.");
                        // Fallback alert if modal fails
                        if (confirm("Are you sure you want to cancel? This will attempt to delete the associated timesheet files.")) {
                             triggerCancellation();
                        }
                    }
                    return; // Stop further execution
                }
                // Handle other actions if needed
            }

            function handleResponse(response) {
                if (!response.ok) {
                    // Try to get error message from response body
                    return response.text().then(text => {
                        throw new Error(`Network response was not ok (${response.status}): ${text || 'No details'}`);
                    });
                }
                return response.json(); // Assuming server sends JSON
            }

            function deleteFiles(fileIDs) {
                if (!fileIDs || fileIDs.length === 0) {
                    console.warn("No file IDs provided for deletion.");
                     // Close modal and potentially redirect or show message
                    const modal = document.getElementById('confirmCancelModal');
                    if (modal) modal.close();
                    alert("No files were selected for deletion."); // Or update UI
                    // Maybe redirect: window.location.href = '/';
                    window.location.href = '/';
                    return Promise.resolve({ message: "No files to delete." }); // Return a resolved promise
                }

                console.log("Attempting to delete File IDs:", fileIDs);
                return fetch('/edit-files', { // Ensure this endpoint exists and handles DELETE or POST with deletion logic
                    method: 'POST', // Or 'DELETE' if your backend supports it
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ fileIDs: fileIDs }),
                })
                .then(handleResponse)
                .then(data => {
                    console.log('Files deleted successfully:', data);
                    // Clear relevant session storage *after* successful deletion
                    sessionStorage.removeItem('errors');
                    sessionStorage.removeItem('name_errors');
                    window.location.href = '/'; // Redirect after successful deletion
                })
                .catch(error => {
                    console.error('Error deleting files:', error);
                    alert(`Error deleting files: ${error.message}`); // Show error to user
                    // Re-enable button, hide spinner in modal
                    const cancelButton = document.getElementById('cancelConfirmed');
                    const spinner = document.getElementById('cancelSpinner');
                    const buttonText = document.getElementById('cancelButtonText');
                    if (cancelButton) cancelButton.disabled = false;
                    if (spinner) spinner.style.display = 'none';
                    if (buttonText) buttonText.textContent = 'Yes, Cancel Process';
                    // Do not redirect on error
                });
            }


            function submitUpdates() {
                // Select rows that are *not* skipped and are not the header
                const selectedRows = Array.from(document.querySelectorAll('#errorsTable tbody tr:not(.row-skipped)'));
                const dataToSend = [];
                let allValid = true; // Flag to track validation

                // Clear previous validation highlights
                document.querySelectorAll('.needs-selection').forEach(el => el.classList.remove('needs-selection'));


                const errorsJSON = sessionStorage.getItem('errors');
                const errors = JSON.parse(errorsJSON || '[]');

                const name_errorsJSON = sessionStorage.getItem('name_errors');
                const name_errors = JSON.parse(name_errorsJSON || '[]');

                selectedRows.forEach(row => {
                    const id = row.getAttribute('data-id');
                    const workOrderDropdown = row.querySelector('.workOrderDropdown');
                    const employeeDropdown = row.querySelector('.employee_dropdown'); // Check if it's a name_error row

                    // Find the original data entry by ID in either errors or name_errors
                    let rowData = errors.find(data => data && data.ID === Number(id));
                    let isNameError = false;
                    if (!rowData) {
                        rowData = name_errors.find(data => data && data.ID === Number(id));
                        isNameError = true;
                    }

                    if (rowData && workOrderDropdown) { // Ensure rowData and dropdown exist
                        const selectedWorkOrderID = workOrderDropdown.value;

                        // Validation: Check if a work order is selected
                        if (!selectedWorkOrderID) { // Checks for empty value ""
                            allValid = false;
                            workOrderDropdown.classList.add('needs-selection'); // Highlight the dropdown
                        } else {
                             // Construct data based on whether it's a name error or regular error
                            if (isNameError) {
                                // For name errors, also need the selected employee ID (if applicable)
                                // and the original name
                                const selectedEmployeeID = employeeDropdown ? employeeDropdown.value : null;
                                // You might need to adjust this based on exactly what the backend expects
                                // For now, sending OriginalName, selected WorkOrderID, and Hours
                                dataToSend.push({
                                    'OriginalName': rowData['OriginalName'], // From name_error structure
                                    'WorkOrderID': selectedWorkOrderID,
                                    'Hours': rowData['Hours']
                                    // Potentially add 'EmployeeID': selectedEmployeeID if needed by backend
                                });
                            } else {
                                // For regular errors
                                dataToSend.push({
                                    'WorkOrderID': selectedWorkOrderID,
                                    'Hours': rowData['Hours']
                                    // Potentially add 'Employee': rowData['Employee'] if needed
                                });
                            }
                        }
                    } else if (rowData && !workOrderDropdown) {
                        // Row exists but has no dropdown (e.g., error type doesn't require WO selection)
                        // Decide if this row should be included or ignored.
                        // If it needs processing without WO, add it here.
                        // console.log(`Row ID ${id} has no work order dropdown, skipping submission for this row.`);
                    } else {
                         console.warn(`Could not find original data or dropdown for row ID: ${id}`);
                         // Potentially mark as invalid if data is expected
                         // allValid = false;
                    }
                });

                if (!allValid) {
                    alert("Please select a Work Order for all highlighted rows before proceeding, or mark them to be skipped using the 'Skip Update?' button.");
                    return; // Stop the submission
                }

                // TODO: dataToSend is an empty array, solve this, it essentially is not sending anything to the backend, but do not what it really is used to.
                // Commented out because it was not letting the user go to the next step
                // if (dataToSend.length === 0) {
                //     alert("No valid entries selected for update. Either select Work Orders or cancel.");
                //     return; // Stop if nothing to send
                // }

                console.log("Submitting data:", JSON.stringify(dataToSend, null, 2)); // Log data being sent

                // Send dataToSend to your server
                fetch('/resolve_internal_errors', { // Ensure this endpoint is correct
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(dataToSend),
                })
                .then(handleResponse) // Use the improved error handling
                .then(data => {
                    console.log('Success:', data);
                    if(data.redirect_url) {
                        // Clear storage *after* successful submission
                        sessionStorage.removeItem('errors');
                        sessionStorage.removeItem('name_errors');
                        // Redirect
                        window.location.href = data.redirect_url;
                    } else {
                        // Handle success case without redirect if necessary
                         alert("Updates processed successfully!"); // Or update UI
                    }
                })
                .catch(error => {
                    console.error('Error submitting updates:', error);
                     alert(`Error submitting updates: ${error.message}`); // Show detailed error
                    // Potentially re-enable submit button here if it was disabled
                });
            }

            // --- Modal Cancel Confirmation Logic ---
            document.addEventListener('DOMContentLoaded', () => {
                const confirmButton = document.getElementById('cancelConfirmed');
                if (confirmButton) {
                    confirmButton.addEventListener('click', triggerCancellation);
                }
            });

            function triggerCancellation() {
                 // Show spinner and disable button immediately
                const cancelButton = document.getElementById('cancelConfirmed');
                const spinner = document.getElementById('cancelSpinner');
                const buttonText = document.getElementById('cancelButtonText');

                if (cancelButton) cancelButton.disabled = true;
                if (spinner) spinner.style.display = 'inline-block';
                if (buttonText) buttonText.textContent = 'Processing...';

                // Collect all FileIDs from *all* rows shown (not just selected/unskipped)
                // because cancelling implies aborting the entire batch shown.
                const allRows = Array.from(document.querySelectorAll('#errorsTable tbody tr'));
                const fileIDs = new Set(); // Use a Set to avoid duplicate FileIDs

                const errorsJSON = sessionStorage.getItem('errors');
                const errors = JSON.parse(errorsJSON || '[]');
                const name_errorsJSON = sessionStorage.getItem('name_errors');
                const name_errors = JSON.parse(name_errorsJSON || '[]');
                const allErrorData = [...errors, ...name_errors]; // Combine both error types

                allRows.forEach(row => {
                    const id = row.getAttribute('data-id');
                    const rowData = allErrorData.find(data => data && data.ID === Number(id));

                    // Extract FileID - Assuming FileID is consistently within the first Hours entry
                    if (rowData && Array.isArray(rowData.Hours) && rowData.Hours.length > 0 && rowData.Hours[0].FileID) {
                        fileIDs.add(rowData.Hours[0].FileID); // Add to Set
                    } else {
                        console.warn(`Could not find FileID for row ID: ${id}`);
                    }
                });

                // Convert Set back to array and call deleteFiles
                deleteFiles(Array.from(fileIDs));
                // Note: deleteFiles now handles UI updates (spinner, redirect, errors)
            }

        </script>

    </body>
</html>
