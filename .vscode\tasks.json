{"version": "2.0.0", "tasks": [{"type": "docker-build", "label": "docker-build", "platform": "python", "dockerBuild": {"tag": "airtanker:latest", "dockerfile": "${workspaceFolder}/airtanker/Dockerfile", "context": "${workspaceFolder}/airtanker", "pull": true}}, {"type": "docker-run", "label": "docker-run: debug", "dependsOn": ["docker-build"], "dockerRun": {"env": {"FLASK_APP": "airtanker\\app.py"}}, "python": {"args": ["run", "--no-debugger", "--no-reload", "--host", "0.0.0.0", "--port", "5000"], "module": "flask"}}]}