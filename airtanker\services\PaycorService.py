from main import airtanker_app
from services.DatabaseService import DatabaseService
from services.OdooService import OdooService

from datetime import datetime, timedelta, date
from itertools import groupby
from nameparser import HumanName
from operator import itemgetter

import os, dotenv, requests, shelve, time, json

from dotenv import load_dotenv

class PaycorService:

    file_ids = []
    errors = []
    name_errors = []
    retry_count = 0

    def __init__(self):
        dotenv.load_dotenv()
        try:
            self.base_url = os.getenv("PAYCOR_BASE_URL")
            self.apim_key = os.getenv("PAYCOR_APIM_KEY")
            self.entity_id = os.getenv("PAYCOR_ENTITY_ID")
        except Exception:
            airtanker_app.logger.exception("Error getting Paycor environment variables!")

#region API Methods
    def get_access_token(self, refresh_token, invalidate_access = False):
        """
        Get Authorization Token from Paycor Public API.
        Requires refresh token provided by initial/previous authorization
        """
        dotenv.load_dotenv()
        client_id = os.getenv("PAYCOR_CLIENT_ID")
        client_secret = os.getenv("PAYCOR_CLIENT_SECRET")
        auth_file = shelve.open('auth-tokens')

        # Define authentication-based static variables
        auth_url = f"{self.base_url}/sts/v1/common/token?subscription-key={self.apim_key}"
        if not invalidate_access and 'access_token' in auth_file:
            access_token = auth_file["access_token"]
            return access_token
        
        if not refresh_token:
            if 'refresh_token' in auth_file:
                refresh_token = auth_file["refresh_token"]
            else:
                refresh_token = "a5e4192e993c88f9b4835c962dd9664074fcd689f9fc76de0fb0c7f2097bcd66"
                # airtanker_app.logger.error("Error getting Paycor authentication refresh token!")
                # return False
        
        if invalidate_access and self.retry_count > 2:
            airtanker_app.logger.error(f"Error authenticating to Paycor: Received 401 response on 3 consecutive attempts.")
            self.retry_count = 0
            return False

        # Request new access token
        auth_data = {"grant_type": 'refresh_token', "refresh_token": refresh_token, "client_id": client_id, "client_secret": client_secret}
        auth_response = requests.post(auth_url, auth_data)
        match auth_response.status_code:
            case 200: # Success
                response_json = auth_response.json()
                access_token = response_json['access_token']
                refresh_token = response_json['refresh_token']

                auth_file['access_token'] = access_token
                auth_file['refresh_token'] = refresh_token

                if invalidate_access:
                    self.retry_count += 1

                return access_token
            case _:
                airtanker_app.logger.error(f"Error authenticating to Paycor: {auth_response.reason} - {auth_response.text}")

    def paycor_get(self, url, access_token):
        """
        Wrap GET request to Paycor with appropriate authentication data
        """
        headers = {'Authorization': 'Bearer ' + access_token, 'Ocp-Apim-Subscription-Key': self.apim_key}
        #airtanker_app.logger.info('Sending request to %s:\n%s', url, headers)
        return requests.get(url, headers=headers)

    def get_timecards(self, start_date, end_date, continuation_token = False):
        """
        Get time data from Paycor between provided dates. Returns array of 'timecards'.
        Will iterate through Paycor API data paging if necessary.
        """
        access_token = self.get_access_token(False)
        if access_token:
            url = f'{self.base_url}/v1/legalEntities/{self.entity_id}/timeCard?startDate={start_date}&endDate={end_date}'
            if continuation_token:
                url += f'&continuationToken={continuation_token}'

            response = self.paycor_get(url, access_token)
            match response.status_code:
                case 200: # Success
                    response_json = response.json()
                    timecard_data = []
                    # Append records passed from Paycor response
                    for record in response_json['records']:
                            timecard_data.append(record)
                    airtanker_app.logger.info(f'Added {len(timecard_data)} to timesheet results')

                    # If the flag is set for more results, recurse and append until we're done.
                    more_timesheets = response_json['hasMoreResults']
                    if more_timesheets:
                        continuation_token = response_json['continuationToken']
                        print(f' -- Continuing with next page using {continuation_token}')
                        other_timecards = self.get_timecards(start_date, end_date, continuation_token)
                        if other_timecards:
                            timecard_data.extend(other_timecards)

                    return timecard_data
                case 401: # Unauthorized. Retry authentication
                    airtanker_app.logger.warn(f"Problem getting time data from Paycor: {response.reason} - {response.text}. Trying again in 3 seconds...")
                    time.sleep(3)
                    access_token = self.get_access_token(False, True)
                    if access_token:
                        return self.get_timecards(start_date, end_date)
                    else:
                        return False
                case _:
                    airtanker_app.logger.error(f"Error getting time data from Paycor: {response.reason} - {response.text}")
                    return False
        else:
            airtanker_app.logger.error(f"Error getting time data from Paycor: No access token found in system!")
            return False

    def get_employee(self, employee_id):
        """
        Get Paycor employee by provided ID
        """
        access_token = self.get_access_token(False)
        if access_token:
            url = f'{self.base_url}/v1/employees/{employee_id}'
            response = self.paycor_get(url, access_token)
            match response.status_code:
                case 200: # Success
                    return response.json()
                case 401: # Unauthorized. Retry authentication
                    airtanker_app.logger.warning(f"Problem getting employee data from Paycor: {response.reason} - {response.text}. Trying again in 3 seconds...")
                    time.sleep(3)
                    access_token = self.get_access_token(False, True)
                    if access_token:
                        return self.get_employee(employee_id)
                    else:
                        return False
                case _:
                    print(F'Error getting employee data: {response.reason} - {response.text}')
                    return False            
        else:
            airtanker_app.logger.error(f"Error getting employee data from Paycor: No access token found in system!")
            return False
#endregion

    def get_employee_id(self, paycor_employee, odoo_employees):
        """
        Get Odoo employee ID by provided Paycor data
        """
        try:
            # Search by badge number
            odoo_employee = next((emp for emp in odoo_employees if emp['barcode'] == paycor_employee['badgeNumber']), False)
            if not odoo_employee:
                # Try searching by name instead
                odoo_employee = next((emp for emp in odoo_employees if emp['name'] == paycor_employee['lastName'] + ', ' + paycor_employee['firstName']), False)
                if not odoo_employee:
                    return False
                
            return odoo_employee['id']
        except Exception as e:
            airtanker_app.logger.exception(f"Error getting Odoo employee ID: {e}")
            return False

    def import_timecards(self, week_ending, odoo_service, import_type):
        """
        Get Paycor time data for selected week, compare with Odoo data, and import into the AirTanker database

        Codes:
            Reg - Regular,
            OT - Overtime,
            DBT - Double Time,
            TRT - Travel Time,
            Ber - Bereavement,      ->  Ignore on invoice
            Hol - Holiday,          ->  Ignore on invoice
            PTO - Paid Time Off,    ->  Ignore on invoice
        """
        error_entry_id_counter = 1

        # Initialize date range variables
        date_fmt = '%Y-%m-%d'
        if isinstance(week_ending, str):
            week_end = datetime.strptime(week_ending, date_fmt)
        elif isinstance(week_ending, datetime):
            week_end = week_ending
        elif isinstance(week_ending, date):
            week_end = week_ending
        else:
            airtanker_app.logger.error("Error importing time data from Paycor. Week Ending input is an invalid format!")
            return self.errors, self.name_errors, self.file_ids

        week_start = week_end - timedelta(days=6)

        week_end_str = datetime.strftime(week_end, date_fmt)
        week_start_str = datetime.strftime(week_start, date_fmt)

        # Initialize database service
        database_service = DatabaseService()
        database_service.connect()

        # Get the active work orders from the database
        active_wos = database_service.get_active_work_orders(selected_week_ending=week_ending)
        
        import_run_id = f"Paycor WE{datetime.strftime(week_end, '%Y%m%d')} - {import_type}"
        
        # Check if file has already been processed.
        file_id, file_already_processed = database_service.insert_filename_get_id(filename=import_run_id, sheet_name='', source_type='Paycor')
        # if file already processed, add error, skip the file.
        if file_already_processed:
            self.errors.append({
                'ID':error_entry_id_counter,
                'FileName': import_run_id,
                "Message":f"This file has already been processed. Delete the sheet at '/edit-files' to re-parse.",
                'ReportedProjectNumber':'',
                'WorkOrderEntry':{
                },
                'Employee': '',
                'EmployeeID':'',
                'Hours': []
            })
            error_entry_id_counter += 1
            return self.errors, self.name_errors, self.file_ids
        self.file_ids.append(file_id)
        
        # Get Paycor timecards
        airtanker_app.logger.info(f"Attempting to import time data for {week_start_str} thru {week_end_str}")
        timecards = self.get_timecards(week_start_str, week_end_str)
        if not timecards:
            self.errors.append({
                'ID':error_entry_id_counter,
                'FileName': import_run_id,
                "Message":f"There are no Paycor time records for the selected week ending.",
                'ReportedProjectNumber':'',
                'WorkOrderEntry':{
                },
                'Employee': '',
                'EmployeeID':'',
                'Hours': []
            })
            error_entry_id_counter += 1
            return self.errors, self.name_errors, self.file_ids
        
        # Buffer Odoo data for comparison and lookup
        odoo_employees = odoo_service.get_all_employees()
        odoo_projects = odoo_service.get_all_projects()
        odoo_tasks = odoo_service.get_all_tasks()

        with open(f'{import_run_id}.json', 'w') as outfile:
            json.dump(timecards, outfile)

        # Sort timecards by employee
        timecards = sorted(timecards, key = itemgetter('employeeId'))
        for empId, timesheets in groupby(timecards, key = itemgetter('employeeId')):
            employee = self.get_employee(empId)
            employee_name = f"{employee['lastName']}, {employee['firstName']}"
            employee_id = self.get_employee_id(employee, odoo_employees)
            if not employee_id:
                self.errors.append({
                'ID':error_entry_id_counter,
                'FileName': import_run_id,
                "Message":f"The employee [{employee_name}] cannot be matched to any Odoo records. Please ensure Badge IDs match between services.",
                'ReportedProjectNumber':'',
                'WorkOrderEntry':{
                },
                'Employee': employee_name,
                'EmployeeID': employee['badgeNumber'],
                'Hours': []
                })
                error_entry_id_counter += 1
                continue

            airtanker_app.logger.info(f'Begin import of time data for [{employee_name}]')
            
            # Add the employee to the employees table and then add their hours to the internal timesheets table
            try:
                db_employee_id = database_service.find_or_add_employee(HumanName(employee_name), "Internal", employee_id) # Insert into Employees table. 
                if db_employee_id != employee_id:
                    self.errors.append({
                        'ID':error_entry_id_counter,
                        'FileName': import_run_id,
                        "Message":f"The Paycor employee [{employee_name}] has an ID of {db_employee_id} in AirTanker, but {employee_id} according to an Odoo search. Please ensure Badge IDs match between services.",
                        'ReportedProjectNumber':'',
                        'WorkOrderEntry':{
                        },
                        'Employee': employee_name,
                        'EmployeeID': employee['badgeNumber'],
                        'Hours': []
                        })
                    error_entry_id_counter += 1
                    continue
            except Exception as ex:
                self.errors.append({
                    'ID':error_entry_id_counter,
                    'FileName': import_run_id,
                    "Message": f"{ex=}",
                    'ReportedProjectNumber':'',
                    'WorkOrderEntry':{
                    },
                    'Employee': employee_name,
                    'EmployeeID': employee['badgeNumber'],
                    'Hours': []
                    })
                error_entry_id_counter += 1
                continue

            # Extract the labor codes into recognizable fields
            employee_timesheets = list(timesheets)
            airtanker_app.logger.info(f'  {len(employee_timesheets)} timesheet records for selected week')
            for timesheet in employee_timesheets:
                timesheet['customer_code'] = '[Missing]'
                timesheet['project_name'] = '[Missing]'
                timesheet['task_name'] = '[Missing]'
                if timesheet['laborCodes']:
                    for labor_code in timesheet['laborCodes']:
                        match labor_code['laborCategoryName']:
                            case 'Customer':
                                timesheet['customer_code'] = labor_code['laborCategoryItemCode']
                            case 'Project':
                                timesheet['project_name'] = labor_code['laborCategoryItemName']
                            case 'Task':
                                timesheet['task_name'] = labor_code['laborCategoryItemName']
                            case _:
                                airtanker_app.logger.error(f"Error parsing Paycor labor codes: {labor_code['laborCategoryName']} not recognized!") 
            
            # Get active work orders for employee
            employee_wos = [wo for wo in active_wos if wo['EmployeeID'] == employee_id]
            employee_wo_count = len(employee_wos)
            airtanker_app.logger.info(f'  {employee_wo_count} work orders found for employee')
            employee_charged_wos = []

            # Group the timesheets by Department/Customer/Project/Task to minimize records being stored to database/Odoo
            employee_timesheets = sorted(employee_timesheets, key = itemgetter('displayDate', 'departmentCode', 'customer_code', 'project_name', 'task_name'))
            for keys, records in groupby(employee_timesheets, key = itemgetter('displayDate', 'departmentCode', 'customer_code', 'project_name', 'task_name')):
                timesheet_date = datetime.strptime(keys[0], '%Y-%m-%dT%H:%M:%S')
                department_code = keys[1]
                customer_code = keys[2]
                project_name = keys[3]
                task_name = keys[4]

                # Ignore administration hours
                if department_code == 100:
                    continue
                
                hours = sum(r['hoursAmount'] for r in records if r['earningCode'] in ['Reg', 'OT', 'DBT', 'TRT'])
                if hours <= 0:
                    # no billable time. Ignore
                    airtanker_app.logger.warn(' No billable time in Paycor for employee')
                    continue

                # Determine project from reported name
                project_name_parts = project_name.split(' - ')
                odoo_project = next((p for p in odoo_projects if p['name'] and p['name'].startswith(project_name_parts[0])), False)
                if odoo_project:
                    project_id = odoo_project['id']
                else:
                    project_id = None
                    airtanker_app.logger.warn(f'    Project [{project_name}] not found in Odoo!')
                
                if import_type == 'internal':
                    if customer_code == '0000': # Get task by name and insert timesheet into 'Internal Timesheets'                        
                        if project_id:
                            if 'AS-' in project_name and employee_wos: # Services project??                                
                                employee_project_wos = [wo for wo in employee_wos if wo['ProjectID'] == project_id]
                                if employee_project_wos: # Mis-classification to AtomTech                                    
                                    self.errors.append({
                                        'ID':error_entry_id_counter,
                                        'FileName': import_run_id,
                                        'Message': f"Reported customer is AtomTech, but matches a T&M work order. Please correct in Paycor",
                                        'ReportedProjectNumber': odoo_project['name'],
                                        'WorkOrderEntry': {},
                                        'Employee': employee_name,
                                        'EmployeeID': employee_id,
                                        'Hours': [
                                            {'Date': datetime.strftime(timesheet_date, date_fmt), 'TaskID': task_name, 'Hours': hours, 'FileID': file_id,}
                                        ]
                                    })
                                    error_entry_id_counter += 1
                                continue
                            
                            if not task_name:
                                task_id = None
                            else:
                                odoo_task = next((t for t in odoo_tasks if t['project_id'][0] == project_id and t['name'] == task_name), False)
                                if not odoo_task:
                                    task_id = None
                                else:
                                    task_id = odoo_task['id']
                                    task_name = odoo_task['name']
                        else:
                            task_id = None

                        insert_query = """
                        INSERT INTO [dbo].[Internal_Timesheets] 
                            ([Date], [EmployeeID], [EmployeeName], [ProjectID], [ProjectName], 
                            [TaskID], [TaskName], [Description], [Hours], [FileID], [WeekEnding])
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """

                        # Add the week_ending_date to the parameters tuple
                        parameters = (datetime.strftime(timesheet_date,date_fmt), 
                                    employee_id, 
                                    employee_name, 
                                    project_id, 
                                    project_name, 
                                    task_id, 
                                    task_name, 
                                    task_name, 
                                    hours, 
                                    file_id, 
                                    week_end_str)
                        
                        # Execute the insert query with the parameters
                        database_service.execute_query(insert_query, parameters)
                    elif customer_code == '[Missing]': # Misreported time - Missing customer code.                        
                        self.errors.append({
                                'ID':error_entry_id_counter,
                                'FileName': import_run_id,
                                'Message': f"Employee did not charge to a customer! Unsure how to proceed with this data",
                                'ReportedProjectNumber': project_name,
                                'WorkOrderEntry': {},
                                'Employee': employee_name,
                                'EmployeeID': employee_id,
                                'Hours': [
                                    {'Date': datetime.strftime(timesheet_date, date_fmt), 'TaskID': task_name, 'Hours': hours, 'FileID':file_id,}
                                ]
                            })
                        error_entry_id_counter += 1
                    elif not employee_wos: # Employee has no Work Orders, but charged time to T&M. Is this a mistake?                        
                        self.errors.append({
                            'ID':error_entry_id_counter,
                            'FileName': import_run_id,
                            'Message': f"Employee charged to '{project_name}' and customer was not AtomTech, but has no active work orders. Is one missing?",
                            'ReportedProjectNumber':project_name,
                            'WorkOrderEntry': {},
                            'Employee': employee_name,
                            'EmployeeID': employee_id,
                            'Hours': [
                                {'Date': datetime.strftime(timesheet_date, date_fmt), 'TaskID': task_name, 'Hours': hours, 'FileID':file_id,}
                            ]
                        })
                        error_entry_id_counter += 1
                        continue
                    else: # Import is internal and customer is not AtomTech. Employee also has work orders. Assume this is intentional?                        
                        pass
                elif import_type == 'external':
                    if employee_wos:                        
                        employee_project_wos = [wo for wo in employee_wos if wo['ProjectID'] == project_id]
                        airtanker_app.logger.info(f'    {len(employee_project_wos)} work orders found for employee in project [{project_name}]')
                        if employee_project_wos:
                            if len(employee_project_wos) == 1:
                                # check reported customer to double-check
                                if customer_code != '0000':
                                    pass
                                else:
                                    # Mis-classification to AtomTech
                                    # self.errors.append({
                                    #     'ID':error_entry_id_counter,
                                    #     'FileName': import_run_id,
                                    #     'Message': f"Reported customer is AtomTech, but project matches a T&M work order. Please correct in Paycor",
                                    #     'ReportedProjectNumber':odoo_project['name'],
                                    #     'WorkOrderEntry': {},
                                    #     'Employee': employee_name,
                                    #     'EmployeeID': employee_id,
                                    #     'Hours': [
                                    #        # {'Date': datetime.strftime(timesheet_date, date_fmt), 'TaskID': task_id, 'Hours': hours, 'FileID':file_id,}
                                    #     ]
                                    # })
                                    # error_entry_id_counter += 1
                                    airtanker_app.logger.warning(f'    Customer code was AtomTech, but found an active work order for the correct project. Ignoring customer...')
                                # Assume correct work order since there is one
                                employee_wo = employee_project_wos[0]
                                database_service.insert_hours_internal(
                                    employee_id,
                                    timesheet_date,
                                    hours,
                                    project_id,
                                    employee_wo['CustomerID'],
                                    file_id,
                                    employee_wo['WorkOrderID'],
                                    None,
                                    None
                                )
                                if employee_wo['WorkOrderID'] not in employee_charged_wos:
                                    employee_charged_wos.append(employee_wo['WorkOrderID'])
                            else:
                                # Check if there is already a work order found for this employee on this date. Assume it is the same
                                using_previous_timesheet = False
                                previous_timesheet_query = """
                                    SELECT TOP (1) [WorkOrderID]
                                    FROM [dbo].[EmployeeReportedHours]
                                    WHERE [EmployeeID] = ? AND Date = ?
                                """
                                previous_timesheet = database_service.execute_query(previous_timesheet_query, (employee_id, datetime.strftime(timesheet_date, date_fmt)))
                                if previous_timesheet:
                                    airtanker_app.logger.warn(f'    Using work order from a previous successful timesheet')
                                    using_previous_timesheet = True
                                    previous_work_order_id = previous_timesheet[0]["WorkOrderID"]
                                    work_order = next((wo for wo in active_wos if wo["WorkOrderID"] == previous_work_order_id), None)
                                    if work_order:
                                        database_service.insert_hours_internal(
                                        employee_id,
                                        timesheet_date,
                                        hours,
                                        project_id,
                                        work_order['CustomerID'],
                                        file_id,
                                        work_order['WorkOrderID'],
                                        None,
                                        None)
                                        if work_order['WorkOrderID'] not in employee_charged_wos:
                                            employee_charged_wos.append(work_order['WorkOrderID'])  
                                    else:
                                        using_previous_timesheet = False

                                # Prompt user to select correct work order
                                if not using_previous_timesheet:
                                    work_order_selections = {}
                                    for wo in employee_wos:
                                        project_selection = wo['ProjectNumber'].strip()
                                        if project_selection in work_order_selections:
                                            work_order_selections[project_selection].append({
                                                wo['WorkOrderID']: wo['WorkOrderNumber'].strip()
                                            })
                                        else:
                                            work_order_selections[project_selection] = [{
                                                wo['WorkOrderID']: wo['WorkOrderNumber'].strip()
                                            }]
                                    self.errors.append({
                                        'ID': error_entry_id_counter,
                                        'FileName': import_run_id,
                                        'Message': f"Employee has more than one work order for reported project number: '{odoo_project['name']}'. Please select the correct one.",
                                        #'ReportedProjectNumber':odoo_project['name'],
                                        'WorkOrderEntry': work_order_selections,
                                        'Employee': employee_name,
                                        'EmployeeID': employee_id,
                                        'Hours': [
                                            {'Date': datetime.strftime(timesheet_date, date_fmt), 'TaskID': None, 'Hours': hours, 'FileID':file_id,}
                                            ]
                                        })
                                    error_entry_id_counter += 1
                        else:
                            # No project WOs found. Check if time is reported as internal.
                            if customer_code == '0000': # and '-AT-' in project_name_parts[0]:
                                # odoo_task = next((t for t in odoo_tasks if t['project_id'][0] == project_id and t['name'] == task_name), False)
                                # if odoo_task:
                                #     task_id = odoo_task['id']
                                #     task_name = odoo_task['name']
                                # else:
                                #     # self.errors.append({
                                #     #     'ID':error_entry_id_counter,
                                #     #     'FileName': import_run_id,
                                #     #     "Message": f"Task [{task_name}] not found in Odoo for project [{project_name}]",
                                #     #     'ReportedProjectNumber': project_name_parts[0],
                                #     #     'WorkOrderEntry':{
                                #     #     },
                                #     #     'Employee': employee_name,
                                #     #     'EmployeeID': employee['badgeNumber'],
                                #     #     'Hours': []
                                #     #     })
                                #     # error_entry_id_counter += 1
                                #     task_id = None

                                # insert_query = """
                                # INSERT INTO [dbo].[Internal_Timesheets] 
                                #     ([Date], [EmployeeID], [EmployeeName], [ProjectID], [ProjectName], 
                                #     [TaskID], [TaskName], [Description], [Hours], [FileID], [WeekEnding])
                                # VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                # """

                                # # Add the week_ending_date to the parameters tuple
                                # parameters = (datetime.strftime(timesheet_date,date_fmt), 
                                #             employee_id, 
                                #             employee_name, 
                                #             project_id, 
                                #             odoo_project['name'], 
                                #             task_id, 
                                #             task_name, 
                                #             task_name, 
                                #             timesheet['hoursAmount'], 
                                #             file_id, 
                                #             week_end_str)
                                
                                # # Execute the insert query with the parameters
                                # database_service.execute_query(insert_query, parameters) 
                                pass                               
                            else:
                                self.errors.append({
                                'ID':error_entry_id_counter,
                                'FileName': import_run_id,
                                'Message': f"Employee Reported Project Number: '{project_name}' isn't found in any assigned work orders for selected week ending {week_end_str}, and customer is not AtomTech",
                                'ReportedProjectNumber': project_name,
                                'WorkOrderEntry': {},
                                'Employee': employee_name,
                                'EmployeeID': employee_id,
                                'Hours': [
                                    {'Date': datetime.strftime(timesheet_date, date_fmt), 'TaskID': task_name, 'Hours': hours, 'FileID': file_id,}
                                    ]
                                })
                                error_entry_id_counter += 1                            
                    elif customer_code != '0000':
                        # Employee has work orders. Customer code is wrong?
                        self.errors.append({
                            'ID':error_entry_id_counter,
                            'FileName': import_run_id,
                            'Message': f"Employee did not charge to AtomTech, but does not have any assigned work orders for selected week ending {week_end_str}. Project is {project_name}",
                            'ReportedProjectNumber':project_name,
                            'WorkOrderEntry': {},
                            'Employee': employee_name,
                            'EmployeeID': employee_id,
                            'Hours': [
                                {'Date': datetime.strftime(timesheet_date, date_fmt), 'TaskID': task_name, 'Hours': hours, 'FileID':file_id,}
                            ]
                        })
                        error_entry_id_counter += 1
                        continue
                else:
                    airtanker_app.logger.error(f'Unrecognized import type: {import_type}')
                    pass
           
            if len(employee_charged_wos) != employee_wo_count:
                self.errors.append({
                    'ID':error_entry_id_counter,
                    'FileName': import_run_id,
                    "Message": f"Employee has {employee_wo_count} active work orders, but only charged to {len(employee_charged_wos)}. Please validate for missing time in Paycor!",
                    'ReportedProjectNumber':'',
                    'WorkOrderEntry':{
                    },
                    'Employee': employee_name,
                    'EmployeeID': employee['badgeNumber'],
                    'Hours': []
                    })
                error_entry_id_counter += 1
        database_service.disconnect()
        return self.errors, self.name_errors, self.file_ids

#region Report Parsing
def parse_time_report(timecards, file_id, 
                    file_name, 
                    import_type, 
                    error_entry_id_counter,
                    week_ending,
                    odoo_service,
                    database_service,
                    active_wos):
    """
    Parse Paycor time data from an exported csv/excel report, compare with Odoo data,
    and import into the AirTanker database.

    timecards is the data that comes from the Paycor report (Excel) - the one uploaded after odoo->db import

        Codes:
            Reg - Regular,
            OT - Overtime,
            DBT - Double Time,
            TRT - Travel Time,
            Ber - Bereavement,      ->  Ignore on invoice
            Hol - Holiday,          ->  Ignore on invoice
            PTO - Paid Time Off,    ->  Ignore on invoice
    """

    errors = []
    name_errors = []
    internal_timesheet_batch = []
    employee_reported_hours_batch = [] # Initialize batch for external type
    batch_size = 1000
    billable_codes = {'Reg', 'OT', 'DBT', 'TRT'}
    
    # Get rate types from database and create a mapping dictionary
    rate_types = database_service.get_rate_types()
    rate_type_map = {}
    for rate_type in rate_types:
        rate_type_map[rate_type['RateType'].lower()] = rate_type['ID']
    
    # --- Caches ---
    task_id_cache = {} # Cache for task_name -> task_id mapping
    timesheet_cache = {}  # Cache for (employee_id, date, work_order_id) -> timesheet_id
    timesheet_entry_cache = {}  # Cache for (timesheet_id, date) -> timesheet_entry_id

    # Buffer Odoo data for comparison and lookup (use dictionaries for fast lookups)
    odoo_employees = {emp['id']: emp for emp in odoo_service.get_all_employees()}
    employee_by_badge = {emp.get('barcode'): emp for emp in odoo_employees.values() if emp.get('barcode')}
    employee_by_name = {emp['name']: emp for emp in odoo_employees.values()}
    odoo_projects = {p['id']: p for p in odoo_service.get_all_projects()}
    odoo_tasks = {t['id']: t for t in odoo_service.get_all_tasks()}

    # Create employee work order mapping
    employee_wo_map = {}
    for wo in active_wos:
        employee_id = wo['EmployeeID']
        employee_wo_map.setdefault(employee_id, []).append(wo)

    week_ending_date = datetime.strptime(week_ending, '%Y-%m-%d')
    week_start = week_ending_date - timedelta(days=6)

    # Sort timecards by employee badgeNumber, lastName, firstName
    timecards.sort(key=itemgetter('badgeNumber', 'lastName', 'firstName'))

    for keys, timesheets in groupby(timecards, key=itemgetter('badgeNumber', 'lastName', 'firstName')):
        badge_number, last_name, first_name = keys
        employee_name = f"{first_name} {last_name}"
        odoo_name = f"{last_name}, {first_name}"

        # Find employee: try badge first then name
        odoo_employee = None
        if badge_number and badge_number != 'Missing':
            odoo_employee = employee_by_badge.get(badge_number)
        if not odoo_employee:
            odoo_employee = employee_by_name.get(odoo_name)
        if not odoo_employee:
            errors.append({
                'ID': error_entry_id_counter,
                'FileName': file_name,
                'Message': (f"The employee [{employee_name}] cannot be matched to any "
                            "Odoo records. Please ensure Badge IDs match between services."),
                'ReportedProjectNumber': '',
                'WorkOrderEntry': {},
                'Employee': employee_name,
                'EmployeeID': badge_number,
                'Hours': []
            })
            error_entry_id_counter += 1
            continue

        # Add/verify the employee exists in the AirTanker employees table.
        try:
            db_employee_id = database_service.find_or_add_employee(
                HumanName(employee_name), "Internal", odoo_employee['id']
            )
            if db_employee_id != odoo_employee['id']:
                errors.append({
                    'ID': error_entry_id_counter,
                    'FileName': file_name,
                    'Message': (f"The Paycor employee [{employee_name}] has an ID of "
                                f"{db_employee_id} in AirTanker, but {odoo_employee['id']} "
                                "according to an Odoo search. Please ensure Badge IDs match "
                                "between services."),
                    'ReportedProjectNumber': '',
                    'WorkOrderEntry': {},
                    'Employee': employee_name,
                    'EmployeeID': badge_number,
                    'Hours': []
                })
                error_entry_id_counter += 1
                continue
        except Exception as ex:
            airtanker_app.logger.error(f"Error finding/adding employee {employee_name}: {ex}")
            errors.append({
                'ID': error_entry_id_counter,
                'FileName': file_name,
                'Message': f"Database error processing employee {employee_name}: {ex}",
                'ReportedProjectNumber': '',
                'WorkOrderEntry': {},
                'Employee': employee_name,
                'EmployeeID': badge_number,
                'Hours': []
            })
            error_entry_id_counter += 1
            continue

        employee_id = odoo_employee['id']
        employee_timesheets = list(timesheets)
        employee_wos = employee_wo_map.get(employee_id, [])

        # Group records by date, customer, project, task for validation purposes
        employee_timesheets.sort(key=itemgetter('date', 'customer', 'project', 'task'))
        for keys, records in groupby(employee_timesheets, key=itemgetter('date', 'customer', 'project', 'task')):
            date, customer, project_name, task_name = keys
            records = list(records)

            # Ignore Administration project
            if project_name == 'Administration':
                continue

            # Only process dates that fall within the week
            if date < week_start or date > week_ending_date:
                continue

            # Check if there are any billable records
            billable_records = [r for r in records if r['code'] in billable_codes]
            if not billable_records:
                # print(f"No billable time in Paycor for employee {employee_name} on {date} for project {project_name}")
                # airtanker_app.logger.info(f"No billable time in Paycor for employee {employee_name} on {date} for project {project_name}")
                continue

            # Summarize hours for validation checks only
            total_hours = sum(r['hours'] for r in billable_records) # Sums from same date
            if total_hours > 24:
                errors.append({
                    'ID': error_entry_id_counter,
                    'FileName': file_name,
                    'Message': (f"Employee charged more than 24 hours! Date: "
                                f"{datetime.strftime(date, '%Y-%m-%d')} Hours: {total_hours}"),
                    'ReportedProjectNumber': project_name, # Added project context
                    'WorkOrderEntry': {},
                    'Employee': employee_name,
                    'EmployeeID': badge_number,
                    'Hours': [] # Maybe include details of the hours here?
                })
                error_entry_id_counter += 1
                continue

            # Look up project in Odoo: try an exact match first, otherwise fall back to a prefix match.
            project_id = None
            if project_name:
                # Try an exact match (ignoring case and extra whitespace)
                odoo_project = next(
                    (
                        p
                        for p in odoo_projects.values()
                        if p.get('name', '').strip().lower() == project_name.strip().lower()
                    ),
                    None,
                )
                # Fallback to prefix matching if exact match is not found.
                if not odoo_project:
                    project_prefix = project_name.split(' - ')[0].strip()
                    odoo_project = next(
                        (
                            p
                            for p in odoo_projects.values()
                            if p.get('name', '').strip().lower().startswith(
                                project_prefix.lower()
                            )
                        ),
                        None,
                    )
                if odoo_project:
                    project_id = odoo_project["id"]

            # Validate the time record
            validation = validate_time(import_type, customer, project_name, project_id, task_name, employee_wos, odoo_tasks, odoo_employee)

            if validation['status'] == 'error':
                errors.append({
                    'ID': error_entry_id_counter,
                    'FileName': file_name,
                    'Message': validation['message'],
                    'ReportedProjectNumber': project_name,
                    'WorkOrderEntry': {},
                    'Employee': employee_name,
                    'EmployeeID': badge_number,
                    'Hours': [{'Date': date, 'TaskID': None, 'Hours': r['hours'], 'Code': r['code'], 'FileID': file_id} for r in records]
                })
                error_entry_id_counter += 1
                continue

            if validation['status'] == 'ok':
                if import_type == 'internal':
                    # Aggregate hours for internal timesheets (assuming one entry per day/project/task)
                    internal_timesheet_batch.append((
                        datetime.strftime(date, '%Y-%m-%d'),
                        employee_id,
                        employee_name,
                        project_id,
                        project_name,
                        validation.get('task_id'), # Use validated task_id if available
                        task_name,
                        task_name, # Using task_name as description for now
                        total_hours, # Use aggregated hours
                        file_id,
                        week_ending
                    ))
                    if len(internal_timesheet_batch) >= batch_size:
                        insert_query = """
                            INSERT INTO [dbo].[Internal_Timesheets] 
                                ([Date], [EmployeeID], [EmployeeName], [ProjectID], [ProjectName], 
                                [TaskID], [TaskName], [Description], [Hours], [FileID], [WeekEnding])
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """
                        try:
                            database_service.execute_many(insert_query, internal_timesheet_batch)
                        except Exception as e:
                            airtanker_app.logger.error(f"Batch insert failed for Internal_Timesheets: {e}")
                            # Handle batch insert error (e.g., log details, add to errors)
                        internal_timesheet_batch = []
                elif import_type == 'external':
                    work_order = validation.get('work_order')
                    if work_order:
                        """
                        TimesheetEntryID        -> Is searched or created if not found in DB using EmployeeID, Date and WorkOrderID
                        Date                    -> date
                        CustomerID              -> work_order.get('CustomerID')
                        LocationID              -> Doesn't come from timecard neither from odoo so we set to None before inserting
                        ProjectID               -> project_id
                        TaskID                  -> database_service.find_or_create_task(task_name=task_name)
                        EmployeeReportedHours   -> hours
                        Notes                   -> Comes from timecard but is not parsed in the caller function, so we set to None before inserting
                        FileID                  -> file_id
                        RateTypeID              -> rate_type_map.get(code_lower)
                        WorkOrderID             -> work_order_id = work_order['WorkOrderID']
                        EmployeeID              -> employee_id
                        """
                        # Process each individual record with its specific rate type
                        for record in billable_records:
                            code = record['code']
                            hours = record['hours']
                            # Skip if hours is zero or negative
                            if hours <= 0:
                                continue

                            # Get rate type ID from the map or use default mapping
                            code_lower = code.lower() if code else '' # Transform code to lowercase for consistent lookup
                            rate_type_id = rate_type_map.get(code_lower)
                            if rate_type_id is None:
                                # Handle case where code is not in map or defaults
                                airtanker_app.logger.warning(f"RateType code '{code}' not found in database or defaults for employee {employee_name}, date {date}. Skipping record.")
                                # Optionally add to errors list
                                continue # Skip this record

                            # --- Use Task ID Cache ---
                            task_id = None
                            if task_name:
                                if task_name in task_id_cache:
                                    task_id = task_id_cache[task_name]
                                else:
                                    try:
                                        task_id = database_service.find_or_create_task(task_name)
                                        if task_id is not None:
                                            task_id_cache[task_name] = task_id # Store in cache
                                        else:
                                             airtanker_app.logger.warning(f"Could not find or create TaskID for task name '{task_name}'.")
                                    except Exception as e:
                                        airtanker_app.logger.error(f"Error finding/creating task '{task_name}': {e}")
                                        # Decide how to handle: skip record, add error, etc.
                                        continue # Skip this record for now

                            # Get customer ID from work order
                            customer_id = work_order.get('CustomerID')

                            # Add note about the hours type
                            notes = f"Paycor: {code} - {hours} hours" # Consider adding more context if available

                            try:
                                # Find or create timesheet and timesheet entry IDs
                                timesheet_id = timesheet_cache.get((employee_id, date, work_order['WorkOrderID']))

                                if timesheet_id is None:
                                    timesheet_id = database_service.find_or_create_timesheet(
                                        employee_id=employee_id,
                                        date=date,
                                        work_order_id=work_order['WorkOrderID']
                                    )
                                    if timesheet_id is not None:
                                        timesheet_cache[(employee_id, date, work_order['WorkOrderID'])] = timesheet_id

                                if timesheet_id is None:
                                    airtanker_app.logger.error(f"Failed to find or create Timesheet for EmployeeID {employee_id}, Date {date}, WO {work_order['WorkOrderID']}")
                                    continue # Skip this record

                                timesheet_entry_id = timesheet_entry_cache.get((timesheet_id, date))

                                if timesheet_entry_id is None:
                                    timesheet_entry_id = database_service.find_or_create_timesheetEntry(timesheet_id=timesheet_id, date=date)
                                    if timesheet_entry_id is not None:
                                        timesheet_entry_cache[(timesheet_id, date)] = timesheet_entry_id

                                if timesheet_entry_id is None:
                                    airtanker_app.logger.error(f"Failed to find or create TimesheetEntry for TimesheetID {timesheet_id}, Date {date}")
                                    continue # Skip this record

                                # Add to batch for later insertion
                                employee_reported_hours_batch.append((
                                    timesheet_entry_id,
                                    date,
                                    customer_id,
                                    project_id, # Use project_id found earlier
                                    task_id,    # Use task_id from cache/DB
                                    hours,      # Use hours from this specific record
                                    notes,
                                    file_id,
                                    rate_type_id, # Use specific rate_type_id
                                    work_order['WorkOrderID'],
                                    employee_id,
                                    None  # location_id - Still needs implementation if required
                                ))

                                # Process batch if it reaches the threshold
                                if len(employee_reported_hours_batch) >= batch_size:
                                    database_service.batch_insert_employee_reported_hours(employee_reported_hours_batch)
                                    employee_reported_hours_batch = [] # Reset batch

                            except Exception as e:
                                airtanker_app.logger.error(f"Error processing record for Employee {employee_id}, Date {date}, WO {work_order['WorkOrderID']}: {e}")
                                # Add specific error to errors list?
                                errors.append({
                                    'ID': error_entry_id_counter,
                                    'FileName': file_name,
                                    'Message': f"Database error processing time entry: {e}",
                                    'ReportedProjectNumber': project_name,
                                    'WorkOrderEntry': {work_order['WorkOrderID']: work_order['WorkOrderNumber']},
                                    'Employee': employee_name,
                                    'EmployeeID': employee_id,
                                    'Hours': [{'Date': date, 'TaskID': task_id, 'Hours': hours, 'FileID': file_id, 'Code': code}]
                                })
                                error_entry_id_counter += 1
                                # Decide if we should continue with the next record or stop processing this group

                    else:
                        # This block handles cases where validation status is 'ok' but no specific work_order was returned
                        # This might indicate an ambiguity or a need for manual selection, as handled below.
                        # The original code tried to look up a previous entry, which might be risky if context changed.
                        # Sticking to the error reporting for multiple WOs seems safer.

                        work_order_entry = {}
                        for wo in employee_wos: # Use the employee's assigned WOs
                            proj_num = wo.get('ProjectNumber', 'Unknown Project').strip()
                            wo_info = {wo['WorkOrderID']: wo.get('WorkOrderNumber', 'Unknown WO#').strip()}
                            if proj_num in work_order_entry:
                                work_order_entry[proj_num].append(wo_info)
                            else:
                                work_order_entry[proj_num] = [wo_info]

                        # Generate error message based on available WOs
                        if not employee_wos:
                            msg = "No active work order found for this employee and date/project."
                        elif len(employee_wos) == 1:
                             # This case should ideally be handled by validate_time returning the WO.
                             # If we reach here, it implies validate_time logic might need review.
                             msg = "Could not automatically assign to the single active work order. Please verify."
                        else:
                             msg = "Multiple work orders potentially match. Please select the correct one."


                        errors.append({
                            'ID': error_entry_id_counter,
                            'FileName': file_name,
                            'Message': msg,
                            'ReportedProjectNumber': project_name,
                            'WorkOrderEntry': work_order_entry, # Provide options
                            'Employee': employee_name,
                            'EmployeeID': employee_id, # Use Odoo ID
                            'Hours': [{
                                'Date': date,
                                'TaskID': None, # Task ID not determined here
                                'Hours': record['hours'],
                                'FileID': file_id,
                                'Code': record['code']
                            } for record in billable_records] # Report all relevant hours
                        })
                        error_entry_id_counter += 1


    # Process any remaining batch items
    if internal_timesheet_batch and import_type == 'internal':
        insert_query = """
            INSERT INTO [dbo].[Internal_Timesheets]
                ([Date], [EmployeeID], [EmployeeName], [ProjectID], [ProjectName],
                 [TaskID], [TaskName], [Description], [Hours], [FileID], [WeekEnding])
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        try:
            database_service.execute_many(insert_query, internal_timesheet_batch)
        except Exception as e:
            airtanker_app.logger.error(f"Final batch insert failed for Internal_Timesheets: {e}")
            # Handle final batch insert error

    # Process any remaining employee reported hours batch items
    if employee_reported_hours_batch and import_type == 'external':
        try:
            database_service.batch_insert_employee_reported_hours(employee_reported_hours_batch)
        except Exception as e:
             airtanker_app.logger.error(f"Final batch insert failed for EmployeeReportedHours: {e}")
             # Handle final batch insert error (e.g., add a general error message)

    return errors, name_errors, error_entry_id_counter

def validate_time(import_type, customer, project_name, project_id,
                  task_name, employee_wos, odoo_tasks, odoo_employee=None):
    """
    Validate a single time record based on the import type and associated data.
    Returns a dictionary with:
      - status: 'ok', 'error', or 'nok'
      - message: Explanation message
      - task_id (for internal) or work_order (for external) when applicable
    """
    def log_error(msg):
        airtanker_app.logger.error(msg)

    if import_type == 'internal':
        if customer == 'AtomTech':
            if project_id:
                # If project name indicates a service project ('AS-') and work orders exist...
                if 'AS-' in project_name and employee_wos:
                    employee_project_wos = [
                        wo for wo in employee_wos if wo['ProjectID'] == project_id
                    ]
                    if employee_project_wos:
                        return {
                            'status': 'error',
                            'message': (
                                f"[Internal Import] For customer 'AtomTech', project({project_id}) "
                                f"'{project_name}' was found "
                                f"in employee work orders: {employee_project_wos}. "
                                "This indicates a match to a T&M work order. Please correct in Paycor."
                            )
                        }
                # Lookup task details using Odoo tasks.
                if not task_name:
                    task_id = None
                else:
                    task_id = None
                    for task in odoo_tasks.values():
                        # Only consider tasks that list the project details in a list/tuple.
                        if (isinstance(task.get('project_id'), (list, tuple)) and
                            task['project_id'][0] == project_id and
                            task['name'] == task_name):
                            task_id = task['id']
                            break
                    # We do not log mismatches here because the result—even with no task match—
                    # is considered acceptable (status will be ok).
            else:
                # log_error(
                #     f"[Internal Import] Missing project_id for record with customer 'AtomTech', "
                #     f"project '{project_name}', and task '{task_name}'."
                # )
                task_id = None
            return {
                'status': 'ok',
                'message': 'Clear to add to Internal_Timesheets',
                'task_id': task_id
            }
        # Error: no or missing customer code.
        elif not customer or customer == '[Missing]':
            return {
                'status': 'error',
                'message': (
                    f"Customer error: Received customer value '{customer}'. "
                    "Expected a valid customer code for charging time."
                ),
            }
        # Error: employee not assigned any work orders.
        elif not employee_wos:
            return {
                'status': 'error',
                'message': (
                    f"Work order error: For project '{project_name}', customer '{customer}' "
                    "was provided and is not AtomTech, but no employee work orders were found."
                )
            }
        # Non-importable case.
        else:
            return {'status': 'nok', 'message': 'Nothing to import'}

    elif import_type == 'external':
        if employee_wos:
            employee_project_wos = [
                wo for wo in employee_wos if wo['ProjectID'] == project_id
            ]
            if employee_project_wos:
                if len(employee_project_wos) == 1:
                    return {
                        'status': 'ok',
                        'message': 'Clear to add to Employee_Reported_Time',
                        'work_order': employee_project_wos[0]
                    }
                else:
                    # Multiple work orders found; this returns an ok status so we sacrifice logging.
                    return {
                        'status': 'ok',
                        'message': 'More than 1 work order found. Look up previous work order',
                        'work_order': None
                    }
            else:
                if customer == 'AtomTech':
                    return {'status': 'nok', 'message': 'Nothing to import'}
                else:
                    return {
                        'status': 'error',
                        'message': (
                            f"[External Import] For Project({project_id}): '{project_name}' "
                            f"and customer '{customer}', no matching work orders were found in "
                            f"Employee({odoo_employee['id']}): {odoo_employee['name']} work orders from db: {employee_wos}."
                        )
                    }
        elif customer == '':
            return {
                'status': 'error',
                'message': (
                    f"[External Import] No customer info provided. Employee({odoo_employee['id']}): {odoo_employee['name']} did not charge time to a customer "
                    "or the customer was not reported by Paycor."
                )
            }
        elif customer != 'AtomTech':
            return {
                'status': 'error',
                'message': (
                    f"[External Import] Employee({odoo_employee['id']}): {odoo_employee['name']} did not charged to AtomTech but to '{customer}', and Project({project_id}) "
                    f"'{project_name}', no work orders are assigned."
                )
            }
        else:
            return {'status': 'nok', 'message': (f"External error: Record not imported for customer '{customer}' with project '{project_name}'.")}
    else:
        final_message = (
            f"Unrecognized import type: Received '{import_type}' "
            f"with customer '{customer}', project '{project_name}', "
            f"project_id '{project_id}', task '{task_name}', and work orders {employee_wos}."
        )
        return {'status': 'error', 'message': final_message}
#endregion
