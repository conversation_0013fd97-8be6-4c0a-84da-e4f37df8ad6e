{% extends 'base.html' %}

{% block styles %}
<link href="https://cdn.datatables.net/fixedcolumns/5.0.3/css/fixedColumns.dataTables.css" rel="stylesheet">
<style>
    .dropdown-check-list {
        display: inline-block;
        position: relative;
        width: 250px;
    }

    .dropdown-check-list .anchor {
        cursor: pointer;
        padding: 5px 10px;
        border: 1px solid #ccc;
        border-radius: 4px;
        background-color: #fff;
        width: 100%;
    }

    .dropdown-check-list .anchor::after {
        content: "\25BC";
        float: right;
        margin-left: 10px;
    }

    .dropdown-check-list .anchor.active::after {
        content: "\25B2";
    }

    .dropdown-check-list .items {
        position: absolute;
        z-index: 1000;
        border: 1px solid #ccc;
        border-radius: 4px;
        background-color: #fff;
        display: none;
        padding: 10px;
        list-style: none;
        max-height: 300px;
        overflow-y: auto;
        box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        width: 100%;
    }

    .dropdown-check-list .items li {
        padding: 5px 10px;
    }

    .dropdown-check-list .items li:hover {
        background-color: #f1f1f1;
    }

    .dropdown-check-list .items input {
        margin-right: 5px;
    }
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdn.datatables.net/fixedcolumns/5.0.3/js/dataTables.fixedColumns.js"></script>
<script src="{{ url_for('static', filename='js/expense_approvals.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', () => setMostRecentSunday());

    function setMostRecentSunday() {
        const selectedWeekEnding = '{{ selected_week_ending }}';
        const formattedDate = selectedWeekEnding !== 'False' 
            ? selectedWeekEnding 
            : new Date(new Date().setDate(new Date().getDate() - new Date().getDay())).toISOString().split('T')[0];
        
        document.getElementById('weekEndingPicker').value = formattedDate;
    }

    function setDateField(value) {
        const date = new Date(value);
        if (date.getDay() === 6) {
            document.getElementById('weekEndingPicker').value = date.toISOString().split('T')[0];
        } else {
            alert('Please select a Sunday.');
            setMostRecentSunday();
        }
    }

    // Dropdown functionality
    $(document).ready(() => {
        $('.dropdown-check-list .anchor').on('click', function(e) {
            e.stopPropagation();
            $(this).toggleClass('active');
            $(this).next('.items').toggle();
        });

        $(document).on('click', (e) => {
            if (!$(e.target).closest('.dropdown-check-list').length) {
                $('.dropdown-check-list .items').hide();
                $('.dropdown-check-list .anchor').removeClass('active');
            }
        });
    });
</script>
{% endblock %}

{% block content %}
<div class="container">
    <h3 class="text-center mt-2">Expense Approvals</h3>
    
    <!-- Date Picker -->
    <div class="date-picker-container text-center mb-3">
        <label for="weekEndingPicker">Select a Week Ending:</label>
        <br>
        <input type="date" id="weekEndingPicker" class="date-picker" onchange="setDateField(this.value)">
    </div>

    <!-- Action Buttons -->
    <div class="d-flex justify-content-center mb-3">
        <button onclick="fetchWeeklyData()" class="btn btn-primary">Get Weekly Data</button>
    </div>

    <!-- Weekly Expenses Table -->
    <div id="divWeeklyExpenses" class="row justify-content-center">
        <div class="col">
            <!-- Status Filter -->
            <div class="dropdown-check-list mb-3" tabindex="100">
                <span class="anchor">Filter by Status</span>
                <ul class="items">
                    <li><label><input type="checkbox" value="Approved" class="status-checkbox"> Approved</label></li>
                    <li><label><input type="checkbox" value="Denied" class="status-checkbox"> Denied</label></li>
                    <li><label><input type="checkbox" value="Pending" class="status-checkbox"> Pending</label></li>
                    <li><label><input type="checkbox" value="Approved With Errors" class="status-checkbox"> Approved With Errors</label></li>
                    <li><label><input type="checkbox" value="Denied With Errors" class="status-checkbox"> Denied With Errors</label></li>
                    <li><label><input type="checkbox" value="Exported" class="status-checkbox"> Exported</label></li>
                </ul>
            </div>

            <!-- Main Table -->
            <table id="employeeWeeklyDetailsTable" class="table table-bordered">
                <thead class="align-middle text-center">
                    <tr>
                        <th colspan="2">Name</th>
                        <th rowspan="2">Work Order #</th>
                        <th rowspan="2">SheetName</th>
                        <th colspan="3">Total Expenses</th>
                        <th rowspan="2">Actions</th>
                        <th rowspan="2" class="w-25">Notes</th>
                        <th rowspan="2">Status</th>
                    </tr>
                    <tr>
                        <th>First</th>
                        <th>Last</th>
                        <th>Reported</th>
                        <th>Allowed</th>
                        <th>Approved</th>
                    </tr>
                </thead>
                <tbody class="text-center align-middle"></tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block modal %}
<!-- Spinner Modal -->
<div class="modal fade" id="spinnerModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-0">Processing...</p>
            </div>
        </div>
    </div>
</div>

<!-- Details Modal -->
<div class="modal fade" id="detailsModalDaily" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">All Entries For Week</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <table id="additionalDetailsTableDaily" class="table table-striped">
                    <thead class="align-middle text-center">
                        <tr>
                            <th rowspan="2">Date</th>
                            <th rowspan="2">Type</th>
                            <th colspan="2">Name</th>
                            <th colspan="2">Lodging</th>
                            <th colspan="2">Airfare</th>
                            <th colspan="2">Rental Car</th>
                            <th colspan="2">Misc.</th>
                            <th colspan="2">Per Diem</th>
                            <th colspan="2">Travel Time</th>
                            <th colspan="2">Mileage</th>
                        </tr>
                        <tr>
                            <th>First</th>
                            <th>Last</th>
                            <th>Reported</th>
                            <th>Allowed</th>
                            <th>Reported</th>
                            <th>Allowed</th>
                            <th>Reported</th>
                            <th>Allowed</th>
                            <th>Reported</th>
                            <th>Allowed</th>
                            <th>Reported</th>
                            <th>Allowed</th>
                            <th>Reported</th>
                            <th>Allowed</th>
                            <th>Reported</th>
                            <th>Allowed</th>
                        </tr>
                    </thead>
                    <tbody class="text-center align-middle"></tbody>
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Hours Selection Modal -->
<div class="modal fade" id="hoursSelectionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Select Approved Expenses</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="hoursSelectionForm"></form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveSelectedHours">Save Changes</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
