import pandas as pd
import tempfile
import camelot
import re
from nameparser import <PERSON><PERSON>ame
from services.DateParser import get_date_exception, is_date_string
from services.DatabaseService import DatabaseService
import textwrap
from rapidfuzz import process, fuzz
from dotenv import load_dotenv
import os
from main import airtanker_app


def parse_valiant_data_from_pdf(df,
                        error_entry_id_counter,
                        file_name,
                        all_active_work_order_entries,
                        file_id):
    
    employee_id = None
    
    # Extract just the names from all_employee_names for matching
    names_to_match_against = set([entry["FullName"] for entry in all_active_work_order_entries])

    # connect to the database
    database_service = DatabaseService()
    database_service.connect()
    
    errors = []
    name_errors = []

    try:
        # Remove empty rows
        pd.set_option('future.no_silent_downcasting', True)
        df.replace("", float("NaN"), inplace=True)
        df.dropna(how='all', inplace=True)

        headers = [
            'Employee Name', 'Cost Center', 'Employee Number', 'Currency', 'TRAVEL', 'RT', 'OT', 'DT', 'TRAVEL', 'RT', 'OT',
            'DT', 'TRAVEL', 'RT', 'OT', 'DT', 'Approved Total'
        ]

        # Set the columns in the dataframe
        df.columns = headers

        # Remove the header row from the data if it exists
        df = df[1:].reset_index(drop=True)

        # Keep only the 1st and 4th row (index 0, 3) and remove all other rows
        df = df.iloc[[0, 3]].reset_index(drop=True)

        # df is now cleaned and ready to extract the data and place them in the database and return any errors

        # Extract values from row 1, column 1 (index 0)
        employee_name = HumanName(df.iloc[1, 0].title())

        # Since the PDF table doesn't contain a breakdown then we'll just have to put all hours on one day.
        week_ending = pd.to_datetime(df.iloc[0, -1].split('\n')[-1], format='%m.%d.%Y')

        total_hours = sum(pd.to_numeric(df.iloc[1, [8, 9, 10, 11]], errors='coerce').fillna(0))

        # ------------------------ #
        #   Begin comparing data:
        # ------------------------ #
        
        
        # match work order name to name is spreadsheet
        best_match_name, score, idx = process.extractOne(employee_name.full_name, names_to_match_against)
        if score < 90:
            name_matching_enabled = os.getenv('ENABLE_NAME_MATCHING', 'false')
            if name_matching_enabled == 'true':
                # Check the DB if name exists in view.
                query = """
                    SELECT TOP (1000) [FullName]
                        ,[EmpID]
                        ,[TS_Name]
                        ,[NameCount]
                    FROM [dbo].[View_NameSelectionCounts]
                    WHERE TS_NAME = ?
                    ORDER by NameCount
                """
                results = database_service.execute_query(query, employee_name.full_name)
                if results:
                    best_match_name = results[0]["FullName"]
                    score = 100
            if score < 90:
                no_name_found_error = True
                # Put into errors
                matches = process.extract(employee_name.full_name, names_to_match_against, limit=5)
                for match in matches:
                    match_name, score, idx = match
                    # Get the hours and get the work orders of each
                    emps_work_orders = []
                    project_numbers = []
                    for wo_entry in all_active_work_order_entries:
                        if wo_entry["FullName"] == match_name:
                            emps_work_orders.append(wo_entry)
                            curr_project_number = wo_entry['ProjectNumber'].strip()
                            project_numbers.append(curr_project_number)

                    work_order_entry = {}
                    employee_id = emps_work_orders[0]['EmployeeID']
                    for entry in emps_work_orders:
                        if entry['ProjectNumber'].strip() in work_order_entry:
                            # append it
                            work_order_entry[entry['ProjectNumber'].strip()].append({
                                entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                                })
                        else:
                            work_order_entry[entry["ProjectNumber"].strip()] = [{
                                entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                            }]

                    matching_entry = None
                    for error_entry in name_errors:
                        if error_entry['OriginalName'] == employee_name.full_name:
                            matching_entry = error_entry
                            break  # Stop searching once a match is found
                    if matching_entry:
                        data = {
                            'EmployeeName':match_name,
                            'EmployeeID':employee_id,
                            'WorkOrders':work_order_entry
                        }
                        matching_entry['EmployeeData'].append(data)
                    else:
                        name_error = {
                            'ID':error_entry_id_counter,
                            'OriginalName':employee_name.full_name,
                            'FileName': file_name,
                            'Message': f"Original Name: <strong>{employee_name.full_name}</strong>. No direct matches in the database. Please select correct employee.",
                            'EmployeeData':[{
                                'EmployeeName':match_name, 'EmployeeID':employee_id, 'WorkOrders':work_order_entry # It's the Project number and work order numbers
                            }],
                            'Hours':[]
                        }
                        name_errors.append(name_error)
                        error_entry_id_counter += 1
        
        emps_work_orders = []
        project_numbers = []
        for wo_entry in all_active_work_order_entries:
            if wo_entry["FullName"] == best_match_name:
                emps_work_orders.append(wo_entry)
                curr_project_number = wo_entry['ProjectNumber'].strip()
                project_numbers.append(curr_project_number)
                
        if score > 89:
            no_name_found_error = False
            employee_id = emps_work_orders[0]["EmployeeID"]

        # Check if there was a name mismatch
        if no_name_found_error:
                matching_entry = None
                for error_entry in name_errors:
                    if error_entry['OriginalName'] == employee_name.full_name:
                        matching_entry = error_entry
                        break  # Stop searching once a match is found
                if matching_entry:
                    data = {
                        'Date':week_ending,
                        'FileID':file_id,
                        'Hours':total_hours,
                        'TaskID':""
                    }
                    matching_entry["Hours"].append(data)

        elif len(emps_work_orders) > 1:
            
            # Put into errors
            work_order_entry = {}
            for entry in emps_work_orders:
                if entry['ProjectNumber'].strip() in work_order_entry:
                    # append it
                    work_order_entry[entry['ProjectNumber'].strip()].append({
                        entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                        })
                else:
                    work_order_entry[entry["ProjectNumber"].strip()] = [{
                        entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                    }]
            error = {
                'ID':error_entry_id_counter,
                'FileName': file_name,
                'Message': f"More than one work order assigned.",
                'WorkOrderEntry':work_order_entry,
                'Employee': employee_name.full_name,                    
                'EmployeeID':employee_id,
                'Hours': [
                    {'Date': week_ending, 'TaskID': "", 'Hours': total_hours, 'FileID':file_id,},
                    # More entries as needed
                ]
            }

            # Iterate over the list of errors to find the match
            matching_entry = None
            for error_entry in errors:
                if error_entry['EmployeeID'] == employee_id:
                    matching_entry = error_entry
                    break  # Stop searching once a match is found
            if matching_entry:
                # If found, add to the current hours
                hours_match = None
                for hours_entry in matching_entry['Hours']:
                    if hours_entry['Date'] == week_ending:
                        hours_match = hours_entry
                        break  # Stop searching once a match is found

                if hours_match:
                    hours_match['Hours'] += total_hours                      
                else:
                    matching_entry['Hours'].append({
                        'Date':week_ending,
                        'TaskID':"",
                        'Hours':total_hours,
                        'FileID':file_id
                    })
            else:
                errors.append(error)
                # Increase the ID counter after each new entry into the error logs
                error_entry_id_counter += 1
        else:
            # Put the hours into DB
            employee_id = emps_work_orders[0]["EmployeeID"]
            project_id = emps_work_orders[0]['ProjectID']
            customer_id = emps_work_orders[0]['CustomerID']
            work_order_id = emps_work_orders[0]['WorkOrderID']                
            database_service.insert_customer_reported_hours_lastRow(employee_id=employee_id, 
                                                            date=week_ending, 
                                                            customer_reported_hours=total_hours,
                                                            project_id=project_id,
                                                            customer_id=customer_id,
                                                            file_id=file_id,
                                                            work_order_id=work_order_id,
                                                            task_id=None,
                                                            location_id=None) 



    except Exception as e:
        airtanker_app.logger.debug(f'Error while parsing PDF file for Valiant: {e}')
        error = {
            'ID':error_entry_id_counter,
            'FileName': file_name,
            "Message":f"Unable to parse this Valiant PDF: {e}. Unexpected columns / rows may have been used in the table.",
            'ReportedProjectNumber':'',
            'WorkOrderEntry':{
            },
            'Employee': '',
            'EmployeeID':'',
            'Hours': []
        }
        
        errors.append(error)
        error_entry_id_counter += 1
        database_service.delete_data_with_fileID(file_id)
    finally:
        database_service.disconnect()

    return errors, name_errors, error_entry_id_counter# match work order name to name is spreadsheet
