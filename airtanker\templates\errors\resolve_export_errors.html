{% extends 'base.html' %}

{% block content %}
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" href="{{ url_for('static', filename='assets/favicon-32x32.png') }}" type="image/png">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css"
          integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <link href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" rel="stylesheet">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" rel="stylesheet">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.7.1/css/buttons.dataTables.min.css">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/styles_customer.css') }}"  />

    <style>
        .flash-message {
            color: white;
            background-color: rgb(216, 49, 49);
            padding: 10px;
            margin: 0 auto;
            border-radius: 5px;
            position: fixed;
            top: 15px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            display: none; /* Initially not displayed */
            opacity: 0; /* Start fully transparent */
        }
        div#loading {
            width: 500px;
            height: 500px;
            display: none;
            background: url(/static/assets/loadingOdoo.gif) no-repeat center center;
            background-size: contain;
            cursor: wait;
            z-index: 1000;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            
            box-shadow: none; /* Ensure no shadow is applied */
            filter: none; /* Remove any filters that might create a shadow effect */
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }
        body {
        font-family: "Helvetica Neue", Helvetica, Arial;
        font-size: 14px;
        line-height: 20px;
        font-weight: 400;
        color: #3b3b3b;
        -webkit-font-smoothing: antialiased;
        background: #2b2b2b;
        }
        @media screen and (max-width: 580px) {
        body {
            font-size: 16px;
            line-height: 22px;
        }
        }

        .wrapper {
        margin: 0 auto;
        padding: 40px;
        max-width: 100%;
        }

        .table {
        margin: 0 0 0px 0;
        width: 100%;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        display: table;
        }
        @media screen and (max-width: 580px) {
        .table {
            display: block;
        }
        }

        .row {
        display: table-row;
        background: #f6f6f6;
        }
        .row:nth-of-type(odd) {
        background: #e9e9e9;
        }
        .row.header {
        font-weight: 900;
        color: #ffffff;
        background: #ea6153;
        }
        .row.green {
        background: #27ae60;
        }
        .row.blue {
        background: #2980b9;
        }
        @media screen and (max-width: 580px) {
        .row {
            padding: 14px 0 7px;
            display: block;
        }
        .row.header {
            padding: 0;
            height: 10px;
        }
        .row.header .cell {
            display: none;
        }
        .row .cell {
            margin-bottom: 10px;
        }
        .row .cell:before {
            margin-bottom: 3px;
            content: attr(data-title);
            min-width: 98px;
            font-size: 10px;
            line-height: 10px;
            font-weight: bold;
            text-transform: uppercase;
            color: #969696;
            display: block;
        }
        }

        .cell {
        padding: 6px 12px;
        display: table-cell;
        }
        @media screen and (max-width: 580px) {
        .cell {
            padding: 2px 16px;
            display: block;
        }
        }

        .grayed-out {
            background-color: #e7e7e7; /* Light gray */
            color: #969696; /* Darker text color */
        }
        .hidden {
            display: none;
        }

    </style>
    <title>Resolve Export Errors</title>
</head>


<br>
<body class="bg-gradient-white">
    <h2 style="display: none;" id="waiting_message">Thank you for being patient. This process is expected to take a few minutes.</h2>
    <div id="loading"></div>
    <!-- Place this within the body of your HTML template -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        {% from "includes/_formhelpers.html" import render_field %}
        <div class="wrapper fadeIn" id="content_1">

            <div style="text-align: center; font-size: large;">
                <h2 style="font-size: larger;">Errors Found While Exporting to Odoo<br><small>Please select a correct contact</small></h2>
            </div>
                
            <div class="wrapper">
                <div class="table" id="errorsTable">
                    <div class="row header">
                        <div class="cell">Contractor</div>
                        <div class="cell">WeekEnding</div>
                        <div class="cell">Message</div>
                        <div class="cell">Select Contact</div>
                        <div class="cell" style="text-align: center;">Skip Export?</div>
                    </div>
                    <!-- Dynamic error rows will be inserted here -->
                </div>
                <div id="formFooter" style="width: 100%;">
                    <a class="underlineHover fadeIn third" href="/">Cancel</a>
                </div>
                <input type="submit" value="Continue" class="btn underlineHover" onclick="submitUpdates();">
            </div>
        </div>
</body>
</html>


<script src="https://code.jquery.com/jquery-3.2.1.slim.min.js"
        integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN"
        crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
        integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q"
        crossorigin="anonymous"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"
        integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl"
        crossorigin="anonymous"></script>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        var flashMessages = document.querySelectorAll('.flash-message');
        flashMessages.forEach(function(flashMessage) {
            // Show the flash message immediately with a fade-in effect
            flashMessage.style.display = 'block';
            flashMessage.style.animation = 'fadeIn 1s forwards';
    
            // After the fade-in, plus the duration of visibility, start the fade-out
            setTimeout(function() {
                flashMessage.style.animation = 'fadeOut 2s forwards';
    
                // Wait for the fade-out animation to complete before setting display to none
                setTimeout(function() {
                    flashMessage.style.display = 'none';
                }, 2000); // Duration of the fadeOut animation
            }, 4000); // 1s for fadeIn to complete + 3s visible = 4s total before fadeOut begins
        });
    });

    
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
        }).replace(/\//g, '-');
    }


    function isEmpty(obj) {
        return Object.keys(obj).length === 0;
    }


    document.addEventListener('DOMContentLoaded', function() {
        const errorsContainer = document.getElementById('errorsTable');

        // Assuming the errors are stored as a JSON string in sessionStorage
        const errorsJSON = sessionStorage.getItem('export_errors');
        const errors = JSON.parse(errorsJSON) || [];

        if (errors.length > 0){
            console.log(errors[0]);
        }

        errors.forEach(function(error) {
            // Create a new row for the error
            const row = document.createElement('div');
            row.className = 'row fadeIn';
            row.setAttribute('data-id', error.ErrorID); // Set the ID as a data attribute

            // Create cell for Contractor
            const contractorCell = document.createElement('div');
            contractorCell.className = 'cell';
            contractorCell.textContent = error.ContractorName;
            row.appendChild(contractorCell);            

            // Create cell for FileName
            const weekeEndingCell = document.createElement('div');
            weekeEndingCell.className = 'cell';
            weekeEndingCell.textContent = error.WeekEnding;
            row.appendChild(weekeEndingCell);

            // Create cell for Message
            const messageCell = document.createElement('div');
            messageCell.className = 'cell';
            messageCell.textContent = error.Message;
            row.appendChild(messageCell);

            console.log(error.Contacts);

            // Create a dropdown cell
            if (!isEmpty(error.Contacts)) {
                const dropdownCell = document.createElement('div');
                dropdownCell.className = 'cell';
                const select = document.createElement('select');
                select.className = 'workOrderDropdown';
                // Corrected loop to iterate over array of objects
                error.Contacts.forEach(contact => {
                    const option = document.createElement('option');
                    option.value = JSON.stringify({ contactId: contact.id, parentId: contact.parent_id[0] });
                    option.textContent = contact.display_name;  // Set option text content to the contact name
                    select.appendChild(option);  // Append option to select element
                });

                dropdownCell.appendChild(select);   // Append select to the dropdown cell
                row.appendChild(dropdownCell);
            }
            
            // Create a cell for the Toggle Button
            const toggleCell = document.createElement('div');
            toggleCell.className = 'cell';
            toggleCell.style = "text-align: center;";

            const toggleButton = document.createElement('button');            
            toggleButton.addEventListener('click', function() {
                toggleButton.textContent = toggleButton.textContent === 'No' ? 'Yes' : 'No';

                row.classList.toggle('grayed-out'); // Toggle the grayed-out class for the row
                
                const dropdown = row.querySelector('.workOrderDropdown');
                if (dropdown) {
                    // Toggle the hidden class to show or hide the dropdown
                    dropdown.classList.toggle('hidden');
                }
            });

            toggleButton.textContent = 'No'; // Customize this text or use an icon            

            toggleCell.appendChild(toggleButton);
            row.appendChild(toggleCell);


            // Add the new row to the errorsContainer
            errorsContainer.appendChild(row);
        });
        // Optionally, if you want to clear the errors from sessionStorage after loading
        // sessionStorage.removeItem('errors');     
    });
    

    function submitUpdates() {
        document.getElementById('waiting_message').style.display = 'block';
        //document.body.style.backgroundColor = 'white';

        $("#loading").show();
        $("#content_1").hide();
        const selectedRows = Array.from(document.querySelectorAll('.row:not(.grayed-out):not(.header)')).filter(row => {
            // Check if the row contains a select element with the class 'workOrderDropdown'
            return row.querySelector('select.workOrderDropdown') !== null;
        });
        const dataToSend = [];

        const errorsJSON = sessionStorage.getItem('export_errors');
        const errors = JSON.parse(errorsJSON) || [];

        //Check that all work orders are checked.
        let allRowsValid = true; // Flag to track if all rows have a selected WorkOrder

        selectedRows.forEach(row => {
            const id = row.getAttribute('data-id'); // Retrieve the ID from the data attribute

            // Find the original data entry by ID
            const rowData = errors.find(data => data['ErrorID'] === Number(id));
            if (rowData) {
                const dropdown = row.querySelector('.workOrderDropdown');                
                const selectedData = JSON.parse(dropdown.value);
                const selectedContactID = selectedData.contactId;
                const selectedParentID = selectedData.parentId;

                const selectedContactName = dropdown.options[dropdown.selectedIndex].text;

                if (!selectedContactID || selectedContactID === "defaultOptionValue") { 
                    allRowsValid = false; 
                    dropdown.classList.add('needs-selection'); 
                } else {
                    console.log(selectedContactID);
                    dataToSend.push({
                        'ContactID': selectedContactID,
                        'ContactName': selectedContactName,
                        'ParentCode': selectedParentID,
                        'WeekEnding': rowData['WeekEnding'],
                        'WorkOrderID': rowData['WorkOrderID'],
                        'ApprovedData': rowData['ApprovedData'],
                        'ContractorID': rowData['ContractorID'],
                        'ContractorName': rowData['ContractorName'],
                        'TimeSheetID': rowData['TimeSheetID']
                    });
                }
            }            
        });

        if (!allRowsValid) {
            alert("Please select a Contact for all rows before proceeding or toggle the Skip Update button to 'Yes'.");
            // Stop the function from proceeding further
            return;
        }

        // Now, send dataToSend to your server as before
        fetch('/resolve_export_errors', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(dataToSend),
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok ' + response.statusText);
            }
            return response.json(); // Process and return the JSON body as a Promise
        })
        .then(data => {
            sessionStorage.removeItem('export_errors');
            window.location.href = '/exports';            
        })
        .catch(error => console.error('Error:', error));
    }



    </script>
{% endblock %}
