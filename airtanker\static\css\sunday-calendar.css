.sunday-calendar {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 24px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.nav-btn {
  width: 40px;
  height: 40px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.nav-btn:hover {
  background: #f3f4f6;
}

.month-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.day-headers {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  /* margin-bottom: 8px; */
}

.day-header {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  font-weight: 500;
  color: #6b7280;
}

.calendar-weeks {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.calendar-week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  padding: 8px 0;
  border-radius: 8px;
  transition: all 0.2s;
  justify-items: center;
  border: 2px solid transparent;
}

.calendar-week.week-hovered {
  background: #dbeafe;
  border-color: #93c5fd; 
}

.calendar-day {
  height: 45px;
  width: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  color: #111827;
}

.calendar-day.other-month {
  color: #9ca3af;
}

.calendar-day:hover {
  background: #f3f4f6;
}

.calendar-day.selected {
  background: #3b82f6;
  color: white !important;
}

.calendar-day.selected .sunday-dot {
  background: white;
}

.calendar-day.hovered:not(.selected) {
  background: #e5e7eb;
}

.calendar-day.sunday {
  font-weight: 600;
  color: #2563eb;
}

.calendar-day.sunday-highlighted:not(.selected) {
  background: #93c5fd;
  color: white;
  font-weight: bold;
}

.sunday-dot {
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #3b82f6;
  transition: all 0.2s;
}

.calendar-day.sunday-highlighted .sunday-dot {
  background: white;
}

.selected-date {
  margin-top: 15px;
  padding: 16px;
  background: #dbeafe;
  border-radius: 8px;
}

.selected-label {
  font-size: 0.9rem;
  color: #6b7280;
  margin: 0 0 4px 0;
  text-align: center;
}

.selected-value {
  text-align: center;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1d4ed8;
  margin: 0;
}

.instructions {
  margin-top: 16px;
  text-align: center;
}

.instructions p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .sunday-calendar {
    padding: 16px;
    margin: 0 10px;
  }
  
  .calendar-day {
    height: 48px;
    width: 48px;
    font-size: 16px;
  }
  
  .month-title {
    font-size: 20px;
  }
  
  .nav-btn {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
}

/* Integration with existing Bootstrap styles */
.sunday-calendar .btn {
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.sunday-calendar .btn-primary {
  background: #3b82f6;
  color: white;
}

.sunday-calendar .btn-primary:hover {
  background: #2563eb;
}

/* Compact version for smaller spaces */
.sunday-calendar.compact {
  max-width: 600px;
  padding: 16px;
}

.sunday-calendar.compact .calendar-day {
  height: 48px;
  width: 48px;
  font-size: 16px;
}

.sunday-calendar.compact .month-title {
  font-size: 20px;
}

.sunday-calendar.compact .selected-date {
  margin-top: 16px;
  padding: 12px;
}

/* Hide instructions for compact version */
.sunday-calendar.compact .instructions {
  display: none;
}

/* Selected Sunday Display (for modal trigger) */
.selected-date-display {
  transition: all 0.2s ease;
  user-select: none;
}

.selected-date-display:hover {
  background: #cce3ff !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.selected-date-display:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Modal specific styles */
.modal-lg .sunday-calendar {
  box-shadow: none;
  padding: 16px;
}

.modal-body .sunday-calendar .instructions {
  display: none;
}
