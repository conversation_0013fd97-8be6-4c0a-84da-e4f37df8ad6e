import json
from collections import defaultdict
from datetime import datetime

class Contractor:
    def __init__(self, name, week_ending):
        self.name = name
        self.week_ending = week_ending
        self.daily_jobs = defaultdict(list)

    def add_daily_hours(self, date, hours, job_num, job_desc, op_code):
        self.daily_jobs[date].append({
            "hours": hours,
            "job_number": job_num,
            "job_description": job_desc,
            "op_code": op_code
        })
    
    def to_dict(self):
        return {
            "name": self.name,
            "week_ending": self.week_ending,
            "daily_jobs": dict(self.daily_jobs)
        }

    def to_json(self):
        return json.dumps(self.to_dict(), indent=4)
    
    def get_weekly_hours(self):
        travel_hours = 0
        regular_hours = 0
        overtime_hours = 0
        doubletime_hours = 0
        for key in self.daily_jobs.keys():
            for dict in self.daily_jobs[key]:
                #date_obj = datetime.strptime(key, '%Y-%m-%d')
                if key.weekday() == 6:
                    doubletime_hours += float(dict["hours"])
                elif dict["job_description"] is not None and "travel" in dict["job_description"].lower():
                    travel_hours += float(dict["hours"])
                elif dict["op_code"] is not None and "travel" in dict["op_code"].lower():
                    travel_hours += float(dict["hours"])
                elif dict["job_number"] is not None and "travel" in dict["job_number"].lower():
                    travel_hours += float(dict["hours"])
                else:
                    regular_hours += float(dict["hours"])                    
        if regular_hours <= 40:
            regular_hours = regular_hours
        else:
            regular_hours = 40
            overtime_hours = regular_hours - 40

        return travel_hours, regular_hours, overtime_hours, doubletime_hours



# Example usage:
# contractor = Contractor("John Doe", "2024-02-11")
# contractor.add_daily_hours("2024-02-10", 8, "JN001", "Job Description 1", "OP01")
# contractor.add_daily_hours("2024-02-10", 4, "JN002", "Job Description 2", "OP02")

# json_output = contractor.to_json()
# print(json_output)

    # week ending
    # daily_hours
    # standard_hourly_rate

    # def properties:
        # hours:
            # regular
            # overtime
            # doubletime
    
