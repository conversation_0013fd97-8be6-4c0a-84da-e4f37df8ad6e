<!-- <!DOCTYPE html>
<html lang="en">     -->

{% extends 'base.html' %}

{% block content %}   
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AirTanker</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" rel="stylesheet">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.7.1/css/buttons.dataTables.min.css">

    <style>
        .file-upload-wrapper {
            border: 2px dashed #91b0b3;
            border-radius: 10px;
            padding: 10px;
            text-align: center;
            position: relative;
            cursor: pointer;
        }
        .buttons-excel {
            margin: 0 auto;
        }
        #detailsModal .modal-dialog {
            max-width: 90%;
        }

        /* Add a horizontal scrollbar to the modal body */
        #detailsModal .modal-body {
            overflow-x: auto;
        }
        .employeeDetailsTable_info {
            display: none;
        }
        .employeeDetailsTable_filter {
            display: none;
        }
        .employeeDetailsTable_length, .employeeDetailsTable_paginate {
            display: none;
        }
        .employeeWeeklyDetailsTable_info {
            display: none;
        }
        .employeeWeeklyDetailsTable_filter {
            display: none;
        }
        .employeeWeeklyDetailsTable_length, .employeeWeeklyDetailsTable_paginate {
            display: none;
        }
        .file-upload-wrapper:hover {
            background-color: #f3f4f6;
        }
        .file-upload-wrapper i {
            color: #5cb85c;
        }
        .list-group-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .list-group-item i.fa-times {
            color: red;
            cursor: pointer;
        }
        #browse-btn {
            color: blue; /* Set the text color */
            text-decoration: underline; /* Underline the text to mimic a hyperlink */
            cursor: pointer; /* Change the cursor to indicate it's clickable */
        }

    </style>
</head>
<body>
    <!-- The additional table popup for details - all entries for that day -->
    <div class="modal fade" id="detailsModal" tabindex="-1" role="dialog" aria-labelledby="detailsModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="detailsModalLabel">All Entries For Day</h5>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body">
              <!-- Content of the modal goes here -->
              <table id="additionalDetailsTable" class="display" style="width:100%">
                <thead>
                    <tr>                  
                        <th>Source</th>
                        <th>FirstName</th>
                        <th>LastName</th>
                        <th>Date</th>
                        <th>ReportedHours</th>
                        <th>ProjectNumber</th>
                        <th>TaskName</th>
                        <th>CustomerName</th>
                    </tr>
                </thead>
                <tbody>
                  <!-- Data for the additional details table goes here -->
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
      </div>

    <h3 class="text-center">Data Access</h3> <!-- Title for the left container -->

    <!-- Dropdowns to sort by week ending and year -->
    <div class="d-flex justify-content-center">
        <label for="yearDropdown">Select Year:</label>              
    </div>
    <div class="d-flex justify-content-center" style="margin-bottom: 20px;">
        <select id="yearDropdown" onchange="updateWeekEndingDates()">
            <option value="2024">2024</option>
            <option value="2023">2023</option>
            <option value="2022">2022</option>
        </select>  
    </div>
    <div class="d-flex justify-content-center">
    <label for="weekEndingDropdown">Select Week-Ending:</label>            
    </div>
    <div class="d-flex justify-content-center" style="margin-bottom: 20px;">
        <select id="weekEndingDropdown" onchange="handleSelection()">
        </select>
    </div>

    <!-- Get Data button and Compare button -->
    <div class="d-flex justify-content-center">
        <button onclick="fetchInitialData()" class="btn btn-primary" style=" margin: 20px;">Get Daily Data</button>
        <button onclick="fetchWeeklyData()" class="btn btn-primary" style=" margin: 20px;">Get Weekly Data</button>
    </div>


    <!-- The Weekly Table -->
    <div class="row justify-content-center">
        <div class="col-auto">
            <table id="employeeWeeklyDetailsTable" class="table table-hover" style="display:none; width:100%; margin-top: 20px;">
                <thead>
                    <tr>
                        <th>FirstName</th>
                        <th>LastName</th>
                        <th>WeekStarting</th>
                        <th>WeekEnding</th>
                        <th>Status,
                        <th>TotalEmployeeReportedHours</th>
                        <th>TotalCustomerReportedHours</th>
                        <th>Error</th>
                        <th style="min-width: 250px;">Notes</th>
                        <th>TaskNames</th>
                        <th>CustomerNames</th>
                    </tr>
                </thead>
                <tbody>

                </tbody>
                <tfoot>
                    <tr>
                        <th>FirstName</th>
                        <th>LastName</th>
                        <th>WeekStarting</th>
                        <th>WeekEnding</th>
                        <th>Status,
                        <th>TotalEmployeeReportedHours</th>
                        <th>TotalCustomerReportedHours</th>
                        <th>Error</th>
                        <th style="min-width: 250px;">Notes</th>
                        <th>TaskNames</th>
                        <th>CustomerNames</th>
                    </tr>
                </tfoot>
        </table>
        </div>            
    </div>


    <!-- The Daily Table -->
    <div class="row justify-content-center">
        <div class="col-auto">
            <table id="employeeDetailsTable" class="display table table-hover" style="display:none; width:100%; margin-top: 20px;">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>FirstName</th>
                        <th>LastName</th>
                        <th>ErrorIndicator</th>
                        <th>EmployeeReportedHours</th>
                        <th>CustomerReportedHours</th>
                        <th style="min-width: 250px;">Notes</th>
                        <th>WeekStarting</th>
                        <th>WeekEnding</th>                        
                        <th>EmployeeID</th>
                        <th>TimeSheetEntryID</th>
                        <th>TimesheetID</th>
                        <th>CustomerName</th>
                    </tr>
                </thead>
                <tbody>

                </tbody>
                <tfoot>
                    <tr>
                        <th>Date</th>
                        <th>FirstName</th>
                        <th>LastName</th>
                        <th>ErrorIndicator</th>
                        <th>EmployeeReportedHours</th>
                        <th>CustomerReportedHours</th>
                        <th style="min-width: 250px;">Notes</th>
                        <th>WeekStarting</th>
                        <th>WeekEnding</th>                        
                        <th>EmployeeID</th>
                        <th>TimeSheetEntryID</th>
                        <th>TimesheetID</th>
                        <th>CustomerName</th>
                    </tr>
                </tfoot>
           </table>
        </div>            
    </div>
</body>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.3/js/dataTables.bootstrap4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.3/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>     

    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/1.7.1/js/dataTables.buttons.min.js"></script>
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/1.7.1/js/buttons.html5.min.js"></script>
    <script type="text/javascript" charset="utf8" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/buttons/1.7.1/js/buttons.print.min.js"></script>
    <script type="text/javascript" charset="utf8" src="static/js/data.js"></script>

{% endblock %}

<!-- </html> -->