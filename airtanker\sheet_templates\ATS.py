import re
from nameparser import <PERSON><PERSON><PERSON>
from services.DateParser import get_date_exception, is_date_string
from services.DatabaseService import DatabaseService
import textwrap
from rapidfuzz import process, fuzz
from dotenv import load_dotenv
import os

load_dotenv()

def parse_ats_cambridge_long(sheet,
                            sheet_name,
                            file_name,
                            file_id,
                            error_entry_id_counter,
                            selected_week_ending,
                            database_service, # type: DatabaseService
                            all_active_work_order_entries):
    
    errors = []
    name_errors = []

    start_row, hours_idx = find_ats_long_start_row_and_totals_column(sheet=sheet) # insert method to check the starting row. Check if it's background color. Could be row 3 or 4 to start. 
    # use same method to find the grandtotal column

    name_idx = 0
    date_idx = 0

    employee_id = None
    
    # Extract just the names from all_employee_names for matching
    names_to_match_against = set([entry["FullName"] for entry in all_active_work_order_entries])

    # Parse the worksheet
    for row in sheet.iter_rows(min_row=start_row, values_only=True):  # Skip header row and blank part
        # Get the data        
        try:                                
            try:
                # get employee name (this is basically our key)
                val = row[name_idx]
                
                # check if its not a date, because we don't want to overwrite out existing name variable
                if val is None or not isinstance(val, str) or is_date_string(val):
                    raise Exception
                employee_name = HumanName(row[name_idx]) ## remove the () and everything in them. Hard code kind of, special circuumstance.
                if employee_name.full_name == "Grand Total": # skip from grand total at the end 
                    continue
                if employee_name.full_name == "Row Labels": # skip from grand total at the end 
                    continue
                if employee_name.last == "Swart":
                    pass

                # match work order name to name is spreadsheet
                best_match_name, score, idx = process.extractOne(employee_name.full_name, names_to_match_against)
                
                if score < 90:
                    name_matching_enabled = os.getenv('ENABLE_NAME_MATCHING', 'false')
                    if name_matching_enabled == 'true':
                        # Check the DB if name exists in view.
                        query = """
                            SELECT TOP (1000) [FullName]
                                ,[EmpID]
                                ,[TS_Name]
                                ,[NameCount]
                            FROM [dbo].[View_NameSelectionCounts]
                            WHERE TS_NAME = ?
                            ORDER by NameCount
                        """
                        results = database_service.execute_query(query, employee_name.full_name)
                        if results:
                            best_match_name = results[0]["FullName"]
                            score = 100
                    if score < 90:
                        no_name_found_error = True
                        # Put into errors
                        matches = process.extract(employee_name.full_name, names_to_match_against, limit=5)
                        for match in matches:
                            match_name, score, idx = match
                            # Get the hours and get the work orders of each
                            emps_work_orders = []
                            project_numbers = []
                            for wo_entry in all_active_work_order_entries:
                                if wo_entry["FullName"] == match_name:
                                    emps_work_orders.append(wo_entry)
                                    curr_project_number = wo_entry['ProjectNumber'].strip()
                                    project_numbers.append(curr_project_number)

                            work_order_entry = {}
                            employee_id = emps_work_orders[0]['EmployeeID']
                            for entry in emps_work_orders:
                                if entry['ProjectNumber'].strip() in work_order_entry:
                                    # append it
                                    work_order_entry[entry['ProjectNumber'].strip()].append({
                                        entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                                        })
                                else:
                                    work_order_entry[entry["ProjectNumber"].strip()] = [{
                                        entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                                    }]

                            matching_entry = None
                            for error_entry in name_errors:
                                if error_entry['OriginalName'] == employee_name.full_name:
                                    matching_entry = error_entry
                                    break  # Stop searching once a match is found
                            if matching_entry:
                                data = {
                                    'EmployeeName':match_name,
                                    'EmployeeID':employee_id,
                                    'WorkOrders':work_order_entry
                                }
                                matching_entry['EmployeeData'].append(data)
                            else:
                                name_error = {
                                    'ID':error_entry_id_counter,
                                    'OriginalName':employee_name.full_name,
                                    'FileName': file_name,
                                    'Message': f"Original Name: <strong>{employee_name.full_name}</strong>. No direct matches in the database. Please select correct employee.",
                                    'EmployeeData':[{
                                        'EmployeeName':match_name, 'EmployeeID':employee_id, 'WorkOrders':work_order_entry # It's the Project number and work order numbers
                                    }],
                                    'Hours':[]
                                }
                                name_errors.append(name_error)
                                error_entry_id_counter += 1
                        continue                                  

                emps_work_orders = []
                project_numbers = []
                for wo_entry in all_active_work_order_entries:
                    if wo_entry["FullName"] == best_match_name:
                        emps_work_orders.append(wo_entry)
                        curr_project_number = wo_entry['ProjectNumber'].strip()
                        project_numbers.append(curr_project_number)
                        
                if score > 89:
                    no_name_found_error = False
                    employee_id = emps_work_orders[0]["EmployeeID"]

            except:
                # if error, means that name wasn't in that cell, so it's the date so don't go to next row just handle the exception
                pass


            # get the date
            curr_date = get_date_exception(row[date_idx])

            # get the hours
            hours = row[hours_idx]

            if no_name_found_error:
                matching_entry = None
                for error_entry in name_errors:
                    if error_entry['OriginalName'] == employee_name.full_name:
                        matching_entry = error_entry
                        break  # Stop searching once a match is found
                if matching_entry:
                    data = {
                        'Date':curr_date,
                        'FileID':file_id,
                        'Hours':hours,
                        'TaskID':""
                    }
                    matching_entry["Hours"].append(data)

            elif len(emps_work_orders) > 1:
                # Check if there's a timesheet with that data already. Use that WorkOrder.
                prev_timesheet_found = False

                employee_id = emps_work_orders[0]["EmployeeID"]
                query = """
                    SELECT TOP (1) [WorkOrderID]
                    FROM [dbo].[EmployeeReportedHours]
                    WHERE [EmployeeID] = ? AND Date = ?
                """
                self_reported_hours = database_service.execute_query(query, (employee_id, curr_date))
                if self_reported_hours:
                    prev_timesheet_found = True
                    work_order_id_from_reported_hours = self_reported_hours[0]["WorkOrderID"]
                    result = next((entry for entry in all_active_work_order_entries if entry["WorkOrderID"] == work_order_id_from_reported_hours), None)

                    project_id = result['ProjectID']
                    customer_id = result['CustomerID']
                    work_order_id = result['WorkOrderID']
                    database_service.insert_customer_reported_hours_lastRow(employee_id=employee_id, 
                                                                    date=curr_date, 
                                                                    customer_reported_hours=hours,
                                                                    project_id=project_id,
                                                                    customer_id=customer_id,
                                                                    file_id=file_id,
                                                                    work_order_id=work_order_id,
                                                                    task_id=None,
                                                                    location_id=None) 
                    continue # to next row in sheet

                # If there's not a previous match of reported hours, check previous errors to see if there is a match,
                # and add the error to make user choose the correct work order.
                if not prev_timesheet_found:
                    # Put into errors
                    work_order_entry = {}
                    for entry in emps_work_orders:
                        if entry['ProjectNumber'].strip() in work_order_entry:
                            # append it
                            work_order_entry[entry['ProjectNumber'].strip()].append({
                                entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                                })
                        else:
                            work_order_entry[entry["ProjectNumber"].strip()] = [{
                                entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                            }]
                    error = {
                        'ID':error_entry_id_counter,
                        'FileName': file_name,
                        'Message': f"More than one work order assigned.",
                        'WorkOrderEntry':work_order_entry,
                        'Employee': employee_name.full_name,                    
                        'EmployeeID':employee_id,
                        'Hours': [
                            {'Date': curr_date, 'TaskID': "", 'Hours': hours, 'FileID':file_id,},
                            # More entries as needed
                        ]
                    }

                    # Iterate over the list of errors to find the match
                    matching_entry = None
                    for error_entry in errors:
                        if error_entry['EmployeeID'] == employee_id:
                            matching_entry = error_entry
                            break  # Stop searching once a match is found
                    if matching_entry:
                        # If found, add to the current hours
                        hours_match = None
                        for hours_entry in matching_entry['Hours']:
                            if hours_entry['Date'] == curr_date:
                                hours_match = hours_entry
                                break  # Stop searching once a match is found

                        if hours_match:
                            hours_match['Hours'] += hours                      
                        else:
                            matching_entry['Hours'].append({
                                'Date':curr_date,
                                'TaskID':"",
                                'Hours':hours,
                                'FileID':file_id
                            })
                    else:
                        errors.append(error)
                        # Increase the ID counter after each new entry into the error logs
                        error_entry_id_counter += 1
                    continue # to next row in sheet
            else:
                # Put the hours into DB
                employee_id = emps_work_orders[0]["EmployeeID"]
                project_id = emps_work_orders[0]['ProjectID']
                customer_id = emps_work_orders[0]['CustomerID']
                work_order_id = emps_work_orders[0]['WorkOrderID']                
                database_service.insert_customer_reported_hours_lastRow(employee_id=employee_id, 
                                                                date=curr_date, 
                                                                customer_reported_hours=hours,
                                                                project_id=project_id,
                                                                customer_id=customer_id,
                                                                file_id=file_id,
                                                                work_order_id=work_order_id,
                                                                task_id=None,
                                                                location_id=None)                     
        except Exception as e:
            #print(e)
            continue

    return errors, name_errors, error_entry_id_counter


def find_ats_long_start_row_and_totals_column(sheet):
    row_counter = 0
    found = False  # Flag to indicate whether the target cell has been found
    for row in sheet.iter_rows(min_row=1, values_only=True):
        row_counter += 1
        if found:  # Check the flag at the beginning of each iteration
            break
        col_counter = 0
        for value in row[:10]:  # This slices the first 10 values (columns) of the row
            if "grand" in str(value).lower():
                found = True  # Set the flag to True to indicate the target cell is found
                break  # Break out of the inner loop
            col_counter += 1
    if found:
        return row_counter, col_counter  # Return the found position
    else:
        return None  # Return None or some indication that the value wasn't found


def parse_ats_ohio(sheet,
                    sheet_name,
                    file_name,
                    file_id,
                    error_entry_id_counter,
                    database_service, # type: DatabaseService
                    all_active_work_order_entries):
    
    # use this to empty out error_lines if this stays 0
    error_count = 0  
    
    error_lines = []
    error_lines.append(file_name + " | " + sheet_name + ": \n")

    name_idx = 0
    hours_idx = 2
    date_idx = 12

    
    for row in sheet.iter_rows(min_row=2, values_only=True):  # Skip header row
        # Get the data
        try:                                
            try:
                # get employee name (this is basically our key)
                val = row[name_idx]

                # check if its not a date, because we don't want to overwrite out existing name variable
                if val is None or not isinstance(val, str) or is_date_string(val):
                    raise Exception
                employee_name = HumanName(re.sub(r'\([^)]*\)', '', val.replace("Employee:", ""))) ## remove the () and everything in them. Hard code kind of, special circuumstance.
                if employee_name.full_name == "Grand Total": # skip from grand total at the end 
                    break






                employee_id = database_service.find_employee(employee_name, "Contract")

                if employee_id is None:
                    error_count += 1
                    error_lines.append(textwrap.indent(employee_name.full_name + " not found in database. \n",'    '))

                    ## TODO Can't really add user here. Fix this develop algorithm.

            except:
                # if error, means that name wasn't in that cell, so it's the date
                pass

            if employee_id == None:
                continue
            # get the date
            curr_date = get_date_exception(row[date_idx])
            #curr_date = curr_date.strftime('%m/%d/%Y')

            # get the hours
            hours_remaining = row[hours_idx]
            customer_id = database_service.find_or_create_customer("ATS")

            # get the timesheet entry (day) - each day could contain multiple entries, get the entries based on the employee hours reported
            timesheet_id = database_service.find_or_create_timesheet(date=curr_date, employee_id=employee_id)
            project_id = database_service.find_or_create_project("Unknown")


            timeSheetEntryID = database_service.find_or_create_timesheetEntry(timesheet_id=timesheet_id, date=curr_date)
            
            database_service.insert_customer_reported_hours_lastRow(timesheet_entry_id=timeSheetEntryID,
                                                                    customer_reported_hours=hours_remaining,
                                                                    date=curr_date,
                                                                    project_id=project_id,
                                                                    customer_id=customer_id,
                                                                    file_id=file_id)
        except Exception as e:
            #print(e)
            continue

    if error_count > 0:
        return error_lines
    else:
        return None


def parse_ats_cambridge_short(sheet,
                            sheet_name,
                            file_name,
                            file_id,
                            error_entry_id_counter,
                            selected_week_ending,
                            database_service, # type: DatabaseService
                            all_active_work_order_entries):

    # use this to empty out error_lines if this stays 0
    error_count = 0  
    
    # create variable to keep track of errors
    errors = []
    name_errors = []

    name_idx = 0
    date_idx = 0
    hours_idx = 1
    employee_id = None
    
    # Extract just the names from all_employee_names for matching
    names_to_match_against = set([entry["FullName"] for entry in all_active_work_order_entries])

    # Parse the worksheet
    for row in sheet.iter_rows(min_row=3, values_only=True):  # Skip header row and blank part
        # Get the data        
        try:                                
            try:
                # get employee name (this is basically our key)
                val = row[name_idx]
                
                # check if its not a date, because we don't want to overwrite out existing name variable
                if val is None or not isinstance(val, str) or is_date_string(val):
                    raise Exception
                employee_name = HumanName(row[name_idx]) ## remove the () and everything in them. Hard code kind of, special circuumstance.
                if employee_name.full_name == "Grand Total": # skip from grand total at the end 
                    break
                if employee_name.full_name == "Row Labels": # skip from grand total at the end 
                    break
                # match work order name to name is spreadsheet
                best_match_name, score, idx  = process.extractOne(employee_name.full_name, names_to_match_against, score_cutoff=0)
                
                if score < 90:
                    name_matching_enabled = os.getenv('ENABLE_NAME_MATCHING', 'false')
                    if name_matching_enabled == 'true':
                        # Check the DB if name exists in view.
                        query = """
                            SELECT TOP (1000) [FullName]
                                ,[EmpID]
                                ,[TS_Name]
                                ,[NameCount]
                            FROM [dbo].[View_NameSelectionCounts]
                            WHERE TS_NAME = ?
                            ORDER by NameCount
                        """
                        results = database_service.execute_query(query, employee_name.full_name)
                        if results:
                            best_match_name = results[0]["FullName"]
                            score = 100
                    if score < 90:
                    #if score > 75:
                        error_not_found_low_score = False
                        no_name_found_error = True
                        # Put into errors
                        matches = process.extract(employee_name.full_name, names_to_match_against, limit=3)
                        for match in matches:
                            match_name, score, idx = match
                            # Get the hours and get the work orders of each
                            emps_work_orders = []
                            project_numbers = []
                            for wo_entry in all_active_work_order_entries:
                                if wo_entry["FullName"] == match_name:
                                    emps_work_orders.append(wo_entry)
                                    curr_project_number = wo_entry['ProjectNumber'].strip()
                                    project_numbers.append(curr_project_number)

                            work_order_entry = {}
                            employee_id = emps_work_orders[0]
                            for entry in emps_work_orders:
                                if entry['ProjectNumber'].strip() in work_order_entry:
                                    # append it
                                    work_order_entry[entry['ProjectNumber'].strip()].append({
                                        entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                                        })
                                else:
                                    work_order_entry[entry["ProjectNumber"].strip()] = [{
                                        entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                                    }]

                            matching_entry = None
                            for error_entry in name_errors:
                                if error_entry['OriginalName'] == employee_name.full_name:
                                    matching_entry = error_entry
                                    break  # Stop searching once a match is found
                            if matching_entry:
                                data = {
                                    'EmployeeName':match_name,
                                    'EmployeeID':employee_id,
                                    'WorkOrders':work_order_entry
                                }
                                matching_entry['EmployeeData'].append(data)
                            else:
                                name_error = {
                                    'ID':error_entry_id_counter,
                                    'OriginalName':employee_name.full_name,
                                    'FileName': file_name,
                                    'Message': f"Original Name: <strong>{employee_name.full_name}</strong>. No direct matches in the database. Please select correct employee.",
                                    'EmployeeData':[{
                                        'EmployeeName':match_name, 'EmployeeID':employee_id, 'WorkOrders':work_order_entry # It's the Project number and work order numbers
                                    }],
                                    'Hours':[]
                                }
                                name_errors.append(name_error)
                                error_entry_id_counter += 1
                    # else:
                    #     error_not_found_low_score = True
                    #     no_name_found_error = False
                        # Don't let them select anything                        
                        continue
                if score > 89:
                    no_name_found_error = False
                    error_not_found_low_score = False
                    employee_id = emps_work_orders[0]

                emps_work_orders = []
                project_numbers = []
                for wo_entry in all_active_work_order_entries:
                    if wo_entry["FullName"] == best_match_name:
                        emps_work_orders.append(wo_entry)
                        curr_project_number = wo_entry['ProjectNumber'].strip()
                        project_numbers.append(curr_project_number)

            except:
                # if error, means that name wasn't in that cell, so it's the date so don't go to next row just handle the exception
                pass


            # get the date
            curr_date = get_date_exception(row[date_idx])

            # get the hours
            hours = row[hours_idx]

            # see notes for reference
            if no_name_found_error:
                matching_entry = None
                for error_entry in name_errors:
                    if error_entry['OriginalName'] == employee_name.full_name:
                        matching_entry = error_entry
                        break  # Stop searching once a match is found
                if matching_entry:
                    data = {
                        'Date':curr_date,
                        'FileID':file_id,
                        'Hours':hours,
                        'TaskID':""
                    }
                    matching_entry["Hours"].append(data)
                continue
            if error_not_found_low_score:
                # Put into errors                                
                matching_entry = None
                for error_entry in errors:
                    if error_entry['Employee'] == employee_name.full_name:
                        matching_entry = error_entry
                        break  # Stop searching once a match is found
                if matching_entry:
                    data = {
                        'Date': curr_date, 
                        'TaskID': "", 
                        'Hours': '', 
                        'FileID':file_id
                    }

                    matching_entry['Hours'].append(data)
                else:
                    error = {
                        'ID':error_entry_id_counter,
                        'FileName': file_name,
                        'Message': f"Employee not found with active work order, and predictive score is too low to recommend name matches.",
                        'WorkOrderEntry':[],
                        'Employee': employee_name.full_name,
                        'Hours': [
                            {'Date': curr_date, 'TaskID': "", 'Hours': '', 'FileID':file_id,},
                            # More entries as needed
                        ]
                    }
                    errors.append(error)
                    # Increase the ID counter after each new entry into the error logs
                    error_entry_id_counter += 1
                continue

            elif len(emps_work_orders) > 1:
                # Check if there's a timesheet with that data already. Use that WorkOrder.
                prev_timesheet_found = False

                employee_id = emps_work_orders[0]["EmployeeID"]
                query = """
                    SELECT TOP (1) [WorkOrderID]
                    FROM [dbo].[EmployeeReportedHours]
                    WHERE [EmployeeID] = ? AND Date = ?
                """
                self_reported_hours = database_service.execute_query(query, (employee_id, curr_date))
                if self_reported_hours:
                    prev_timesheet_found = True
                    work_order_id_from_reported_hours = self_reported_hours[0]["WorkOrderID"]
                    result = next((entry for entry in all_active_work_order_entries if entry["WorkOrderID"] == work_order_id_from_reported_hours), None)

                    project_id = result['ProjectID']
                    customer_id = result['CustomerID']
                    work_order_id = result['WorkOrderID']                
                    database_service.insert_customer_reported_hours_lastRow(employee_id=employee_id, 
                                                                    date=curr_date, 
                                                                    customer_reported_hours=hours,
                                                                    project_id=project_id,
                                                                    customer_id=customer_id,
                                                                    file_id=file_id,
                                                                    work_order_id=work_order_id,
                                                                    task_id=None,
                                                                    location_id=None) 
                    continue # to next row in sheet

                # If there's not a previous match of reported hours, check previous errors to see if there is a match,
                # and add the error to make user choose the correct work order.
                if not prev_timesheet_found:
                    # Put into errors
                    work_order_entry = {}
                    for entry in emps_work_orders:
                        if entry['ProjectNumber'].strip() in work_order_entry:
                            # append it
                            work_order_entry[entry['ProjectNumber'].strip()].append({
                                entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                                })
                        else:
                            work_order_entry[entry["ProjectNumber"].strip()] = [{
                                entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                            }]
                    error = {
                        'ID':error_entry_id_counter,
                        'FileName': file_name,
                        'Message': f"<strong>{employee_name.full_name}</strong> has more than one work order assigned.",
                        'WorkOrderEntry':work_order_entry,
                        'Employee': emps_work_orders[0]["FullName"],                    
                        'EmployeeID':employee_id,
                        'Hours': [
                            {'Date': curr_date, 'TaskID': "", 'Hours': hours, 'FileID':file_id,},
                            # More entries as needed
                        ]
                    }

                    errors.append(error)
                    # Increase the ID counter after each new entry into the error logs
                    error_entry_id_counter += 1
                    continue # to next row in sheet
            else:
                # Put the hours into DB
                employee_id = emps_work_orders[0]["EmployeeID"]
                project_id = emps_work_orders[0]['ProjectID']
                customer_id = emps_work_orders[0]['CustomerID']
                work_order_id = emps_work_orders[0]['WorkOrderID']                
                database_service.insert_customer_reported_hours_lastRow(employee_id=employee_id, 
                                                                date=curr_date, 
                                                                customer_reported_hours=hours,
                                                                project_id=project_id,
                                                                customer_id=customer_id,
                                                                file_id=file_id,
                                                                work_order_id=work_order_id,
                                                                task_id=None,
                                                                location_id=None)                     
        except Exception as e:
            #print(e)
            continue

    return errors, name_errors, error_entry_id_counter
