class EmployeeHours:
    def __init__(self, name, weekly_hours, daily_hours):
        self.name = name
        self.weekly_hours = weekly_hours
        self.daily_hours = daily_hours

    def add_daily_hours(self, date, hours):
        self.daily_hours[date] = hours
    
    def add_weekly_hours(self, hours):
        self.weekly_hours = float(self.weekly_hours) + float(hours)

    def get_total_hours(self):
        return sum(self.daily_hours.values())        

    def __str__(self):
        return f"{self.name} worked {self.get_total_hours()} hours over the following days: {', '.join([f'{date}: {hours}h' for date, hours in self.daily_hours.items()])}"
