import xmlrpc.client
from datetime import datetime, timedelta
import pprint
import os
from dotenv import load_dotenv
from pprint import pprint
import ast
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.OdooService import OdooService

class OdooAPI:
    def __init__(self):
        load_dotenv()
        self.url = os.getenv('ODOO_URL')
        self.db = os.getenv('ODOO_DB')

        self.username = os.getenv("ODOO_USER")
        self.password = os.getenv("ODOO_PASS")

        common = xmlrpc.client.ServerProxy('{}/xmlrpc/2/common'.format(self.url))
        self.uid = common.authenticate(self.db, self.username, self.password, {})
        self.models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))


    def get_expense_details_by_id(self, expense_id):
        model_name = 'x_approved_expenses'  # Replace with the actual model name for expenses
        # Get all fields of the expense model
        expense_fields = self.models.execute_kw(self.db, self.uid, self.password, model_name, 'fields_get', [], {'attributes': ['string', 'help', 'type']})
        expense_field_list = list(expense_fields.keys())
        
        # Read the expense record by ID
        expenses = self.models.execute_kw(self.db, self.uid, self.password, model_name, 'read', [[expense_id], expense_field_list])
        
        if not expenses:
            print(f"No expense record found with ID {expense_id}")
            return None
        
        expense = expenses[0]
        pprint(expense)
        return expense
    

    def get_expenses_for_site_sheet(self, site_sheet_id):
        site_sheet = self.get_site_sheet_by_id(site_sheet_id)
        if not site_sheet:
            return
        
        expense_fields = ['x_arrival_expense', 'x_working_expense', 'x_departure_expense']
        for field in expense_fields:
            if field in site_sheet[0] and site_sheet[0][field]:
                expense_id = site_sheet[0][field][0]
                print(f"Details for {field} (ID: {expense_id}):")
                self.get_expense_details_by_id(expense_id)


    def get_site_sheet_fields(self):
        model_name = 'x_site_sheet'
        fields = self.models.execute_kw(self.db, self.uid, self.password, model_name, 'fields_get', [], {'attributes': ['string', 'help', 'type']})
        for field in fields:
            print(f"Field: {field}, Type: {fields[field]['type']}, String: {fields[field]['string']}, Help: {fields[field].get('help', 'No help text')}")                       
        return fields


    def get_site_sheet_by_id(self, record_id):
        model_name = 'x_site_sheet'       
        # First, get all fields
        field_list = self.get_site_sheet_fields()
        print(field_list.keys())            
        # Now, read the specific record by ID using all fields
        record = self.models.execute_kw(self.db, self.uid, self.password, model_name, 'read', [[record_id], field_list])
        
        # Print the detailed record information
        if record:
            print(record[0])
            return record
        else:
            print(f"No record found with ID {record_id}")
            return None


    def get_work_orders(self):
        '''Gets the work orders from Odoo to
        store into the database.'''

        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))

        # Search filtered active (In Progress) tasks
        stage_id_field = os.getenv("STAGE_ID_FIELD")
        taskIDs = models.execute_kw(self.db, self.uid, self.password, 'project.task', 'search', [[]]) # Stage ID 187 = 'WO - In Progress'

        # Pull detailed information with provided task IDs
       # Pull detailed information with provided task IDs
        fields_str = os.getenv("ODOO_FIELDS")
        fields = ast.literal_eval(fields_str)

        tasks = models.execute_kw(self.db, self.uid, self.password, 'project.task', 'read', [taskIDs], 
                         {'fields': fields })
    
        return tasks

    def get_all_tasks(self, project=None, task_id=None):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        task_ids = None
        if task_id:
            task_ids = [int(task_id)]
        else:
            if isinstance(project, int):
                task_ids = models.execute_kw(self.db, self.uid, self.password, 'project.task', 'search', [[('project_id', '=', project)]])
            else:
                task_ids = models.execute_kw(self.db, self.uid, self.password, 'project.task', 'search', [[('project_id', '=', project['id'])]])

        tasks = models.execute_kw(self.db, self.uid, self.password, 'project.task', 'read', [task_ids, ['id', 'name', 'project_id']])

        return tasks
    

    def get_customers(self):
        # Define the model name and the domain to filter customers
        model_name = 'res.partner'
        domain = [('customer_rank', '>', 0)]  # Filtering for customers
        fields = ['id', 'name', 'email', 'phone']  # Fields to fetch
        
        # Fetch customer IDs based on the domain
        customer_ids = self.models.execute_kw(self.db, self.uid, self.password, model_name, 'search', [domain])
        
        # Read customer details using the fetched IDs
        customers = self.models.execute_kw(self.db, self.uid, self.password, model_name, 'read', [customer_ids, fields])
        
        return customers
    

    def update_task_with_site_sheet_id(self, task_id, site_sheet_id):
        update_successful = self.models.execute_kw(self.db, self.uid, self.password, 'project.task', 'write', [[task_id], {'x_studio_site_sheet': site_sheet_id}])
        return update_successful


    def update_all_tasks_with_site_sheet_id(self, site_sheet_id, project=None, task_id=None):
        site_sheet_record = self.get_site_sheet_by_id(1)
        if not site_sheet_record:
            print(f"Site sheet with ID {site_sheet_id} not found.")
            return
        
        tasks = self.get_work_orders()
        if not tasks:
            print(f"No tasks found for the given project/task.")
            return
        
        for task in tasks:
            task_id = task['id']
            if self.update_task_with_site_sheet_id(task_id, site_sheet_id):
                print(f"Successfully updated task {task_id} with site sheet ID {site_sheet_id}.")
            else:
                print(f"Failed to update task {task_id} with site sheet ID {site_sheet_id}.")
    

    def get_all_subcontractor_contacts(self):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        # Define the search domain to find contacts whose parent has the "Subcontractor" tag
        #domain = [['parent_id.category_id.name', '=', 'Subcontractor']]
        domain = []
        # Include 'parent_id' in the fields list to fetch the parent ID
        fields = ['id', 'display_name', 'parent_id', 'parent_name','company_name','company_id','x_studio_customer_id','x_studio_code']

        contacts = models.execute_kw(self.db, self.uid, self.password, 
                                    'res.partner', 'search_read', 
                                    [domain], {'fields': fields})

        return contacts
    

    def get_parent_code_field(self, parent_id):        
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        domain = [['id', '=', parent_id]]
        fields = ['id', 'x_studio_code']
        parents = models.execute_kw(self.db, self.uid, self.password,
                                    'res.partner', 'search_read',
                                    [domain], {'fields': fields})
        return parents[0]['x_studio_code'] if parents else None


    def get_all_fields(self, model):
        models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(self.url))
        fields_metadata = models.execute_kw(self.db, self.uid, self.password, 
                                            model, 'fields_get', [], {'attributes': ['string', 'type']})
        fields = list(fields_metadata.keys())
        return fields
# Example usage

odoo_service = OdooAPI()
try:
    # fields = odoo_service.get_all_fields('res.partner')
    # print('\n')
    # for field in fields:
    #     print(field)
    contacts = odoo_service.get_all_subcontractor_contacts()
    for contact in contacts:
        if contact['x_studio_code']:
            pass
        else:
            parent_code = odoo_service.get_parent_code_field(55)
            if parent_code:
                pass
            
except Exception as e:
    print(f'Error occurred: {str(e)}')

print('Done.')