from main import airtanker_app
from flask_wtf import FlaskForm
from wtforms import String<PERSON><PERSON>, PasswordField, validators


class LoginValidation(FlaskForm):
    user_name_pid = StringField('Email', [validators.InputRequired()],
                                render_kw={'autocomplete': 'email', 'autofocus': True})
    user_pid_Password = PasswordField('Password', [validators.InputRequired()],
                                      render_kw={'autocomplete': 'current-password'})

class LoginValidation_Odoo(FlaskForm):
    user_name_pid = StringField('', [validators.InputRequired()],
                                render_kw={'autofocus': True, 'placeholder': 'Enter Your Email Address'})

    user_pid_Password = PasswordField('', [validators.InputRequired()],
                                      render_kw={'autofocus': True, 'placeholder': 'Enter Your Password'})

class UploadValidation(FlaskForm):
    pass