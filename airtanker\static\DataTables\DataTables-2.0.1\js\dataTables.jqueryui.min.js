/*! DataTables jQuery UI integration
 * ©2011-2014 SpryMedia Ltd - datatables.net/license
 */
!function(n){var a,d;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return n(t,window,document)}):"object"==typeof exports?(a=require("jquery"),d=function(t,e){e.fn.dataTable||require("datatables.net")(t,e)},"undefined"==typeof window?module.exports=function(t,e){return t=t||window,e=e||a(t),d(t,e),n(e,0,t.document)}:(d(window,a),module.exports=n(a,window,window.document))):n(jQuery,window,document)}(function(u,t,e){"use strict";var n=u.fn.dataTable;return u.extend(!0,n.ext.classes,{container:"dt-container dt-jqueryui",paging:{active:"ui-state-disabled",button:"fg-button ui-button ui-state-default",container:"dt-paging fg-buttonset ui-buttonset fg-buttonset-multi ui-buttonset-multi",disabled:"ui-state-disabled"},thead:{cell:"ui-state-default"},tfoot:{cell:"ui-state-default"}}),n.ext.renderer.layout.jqueryui=function(t,e,n){var a=!1,d=u("<div/>",{class:"dt-layout-row ui-helper-clearfix"}).appendTo(e);u.each(n,function(t,e){t=u("<div/>",{id:e.id||null,class:"dt-layout-cell dt-"+t+" "+(e.className||"")}).append(e.contents).appendTo(d);u(e.contents).hasClass("dataTable")&&(a=!0,t.addClass("table"))}),a||d.addClass("fg-toolbar ui-toolbar ui-widget-header")},n});