from openpyxl import load_workbook
from nameparser import HumanName
import re
import csv
from io import StringIO
import nltk
from nltk.tag.stanford import StanfordNERTagger


#st = StanfordNERTagger ('stanford-ner/all.3class.distsim.crf.ser.gz', 'stanford-ner/stanford-ner.jar')
# text = """YOUR TEXT GOES HERE"""

# for sent in nltk.sent_tokenize(text):
#     tokens = nltk.tokenize.word_tokenize(sent)
#     tags = st.tag(tokens)
#     for tag in tags:
#         if tag[1]=='PERSON': print tag


# Define function to check if a cell value looks like a name
def looks_like_name(value):    
    try:
        name = HumanName(value)
        return bool(name.first and name.last)
    except Exception as e:
        print(f"Error parsing name: {e}")
        return False


# Function to identify the name column
def find_name_column_excel(sheet, exclude_header="Didn't Clock"):
    column_scores = {}  # Store scores for each column
    excluded_col_index = None  # Index of the column to be excluded
    
    # Get headers from the first row and find the index of the excluded column
    headers = [cell for cell in next(sheet.iter_rows(min_row=1, max_row=1, values_only=True))]
    
    if exclude_header in headers:
        excluded_col_index = headers.index(exclude_header)

    # Iterate over each column in the first few rows
    for col_idx, col in enumerate(sheet.iter_cols(min_row=0, max_row=50, values_only=True)):
        if "Didn't Clock" in str(cell for cell in col):
                excluded_col_index = col_idx
        if col_idx == excluded_col_index:  # Skip the excluded column
            continue
        score = 0
        for cell in col:
            if col_idx == 11:
                test = ""
            if cell is None or not isinstance(cell, str) or is_date_string(cell):
                score += False            
            elif "Didn't Clock" in str(cell):
                score = False
                break
            else:
                score += looks_like_name(cell)
        #score = sum(looks_like_name(cell) for cell in col)  # Score based on potential names
        column_scores[col_idx] = score

    # Identify the column index with the highest score
    best_guess_index = max(column_scores, key=column_scores.get) if column_scores else None
    return best_guess_index


def get_name_td(input_str):
    # Simplified pattern to match any character before " - " for the job identifier,
    # any characters after " - " for the name, and optionally "Overtime" or "Double Time" at the end.
    pattern = r'^(.*?) - (.*?)(?: (Overtime|Double Time))?$'

    match = re.search(pattern, input_str)
    if match:
        job_identifier = match.group(1).strip()  # Ensure we remove any leading/trailing spaces
        name = match.group(2).strip()  # Similarly, trim the name to remove any extra spaces
        status = match.group(3) if match.group(3) else "Standard"  # Default to "Standard" if no status is found

        return job_identifier, HumanName(name), status
    else:
        return None, None, None

def find_name_column_csv(csv_data, exclude_header="Didn't clock"):
    # Use StringIO to treat the CSV string as a file-like object
    csvfile = StringIO(csv_data)  # Convert the CSV data string into a file-like object
    reader = csv.reader(csvfile)
    headers = next(reader)  # Read the header row

    # Identify the index of the column to be excluded
    excluded_col_index = headers.index(exclude_header) if exclude_header in headers else None
    
    column_scores = {i: 0 for i, _ in enumerate(headers) if i != excluded_col_index}

    # Iterate over the first few rows (adjust the range as needed)
    for row in list(reader)[:50]:  # Assuming you want to check the first 20 rows
        for i, cell in enumerate(row):
            if "Didn't Clock" in str(cell):
                excluded_col_index = i
            if i == excluded_col_index:
                continue  # Skip the excluded column            
            # Increment score if the cell looks like a name
            if looks_like_name(cell):
                column_scores[i] += 1

    # Identify the column index with the highest score
    best_guess_index = max(column_scores, key=column_scores.get) if column_scores else None
    return best_guess_index, headers[best_guess_index] if best_guess_index is not None else "Not found"

def contains_emails(sheet,):    
    # Compile the email regex for efficiency
    email_pattern = r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
    email_regex = re.compile(email_pattern)

    # Iterate through the cells in the sheet
    for row in sheet.iter_rows(min_row=1, max_row=10, values_only=True):
        for cell in row:
            if cell and email_regex.search(str(cell)):
                return True  # Found an email, no need to check further

    return False  # No emails found in the sheet


# Find the Name column index
# name_column_index = find_name_column(sheet)
# print(f"The best guess for the Name column index is: {name_column_index}")
