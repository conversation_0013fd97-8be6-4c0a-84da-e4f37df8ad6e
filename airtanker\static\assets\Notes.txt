Use WO as the customer
- match with 

Get WO in class
Get double time / overtime / regular hours in class


Project td -> Timecard Worked Project ID Description
Odoo Customer 
TD Customer 

Database backend -> USMI-DB01

Print to PDF

Log / Summary of errors, and what was processed
 - analyze errors by customers
 - tracking

TD can mass approve

Verify against work order report
 - works , no work order
 - work order, didn't work





