from typing import Dict, List
from main import airtanker_app
from services.DatabaseService import DatabaseService
from services.GeminiService import GeminiService
from services.CustomerService import CustomerService
from models.NameMatch import Status, Origin, NameMatch
from google.genai import types
import base64
import json
import pandas as pd

class FileParserService:

    def __init__(self):
        pass

    def parse_customer_timesheets(self, *, files, template_ids, db_service: DatabaseService) -> pd.DataFrame:

        # db_service = DatabaseService()
        # db_service.connect() # We are already connected...
        
        all_dfs = []
        templates = db_service.get_templates()
        template_map = {int(template['id']): template for template in templates}

        # TODO: Check if we need to retrieve the customers before the call to gemini, so we can send it to gemini and have it match the customers for us.
        
        for idx, (file, template_id) in enumerate(zip(files, template_ids)):
            print(f"\n=== Processing File {idx + 1} ===")
            print(f"Filename: {file.filename}")
            print(f"Template ID: {template_id}")

            # Check if file has already been processed
            file_id, file_already_processed = db_service.insert_filename_get_id(filename=file.filename, sheet_name='', source_type='Customer Timesheets')
            if file_already_processed:
                print(f"File {file.filename} has already been processed. Skipping...")
                continue

            # Get template data
            template = template_map.get(int(template_id))
            if not template:
                print(f"ERROR: Template with ID {template_id} not found!")
                continue

            # Convert file to base64
            file.seek(0)  # Reset file pointer
            file_content = file.read()
            file_base64 = base64.b64encode(file_content).decode('utf-8')

            # Right before sending data to gemini, retrieve customers from active work orders and send them to gemini.

            # Prepare the AI prompt with template data
            ai_prompt = template['prompt']
            response_schema = template['response_schema']

            # Create the complete prompt with context
            complete_prompt = f"File Name: {file.filename} \nMime Type: {file.content_type}\n\n{ai_prompt}"

            # Generate 'parts'
            parts = [
                types.Part.from_bytes(
                    mime_type=file.content_type,
                    data=file_base64,
                ),
                types.Part.from_text(text=complete_prompt),
            ]

            # Make the AI call
            print(f"\n--- Making AI Call ---")
            try:
                gemini_service = GeminiService()

                # Generate AI response
                print("🤖 Sending prompt to Gemini...")
                print("⏳ This may take a few moments...")

                ai_response = gemini_service.generate_content(parts, response_schema)

                print(f"✅ AI Response received ({len(ai_response)} characters)")

                # Eventhough we are asking gemini to return json when sending back the reponse, it sends it in str chunks, so we need to reparse it.
                try:
                    parsed_response = json.loads(ai_response)
                    print(f"\n--- Parsed JSON Response to {type(parsed_response)} ---")
                    print(json.dumps(parsed_response, indent=2))

                    # Validate against expected schema
                    if 'timeentries' in parsed_response:
                        print("✅ Response appears to contain timesheet data")
                    else:
                        raise Exception("Response did not match expected timesheet format")
                    
                except Exception as e:
                    print(f"⚠️  Warning: Error parsing AI response: {e}")
                    raise Exception(f"Error parsing AI response: {e}")

            except Exception as ai_error:
                print(f"❌ Error calling Gemini AI: {ai_error}")
                airtanker_app.logger.exception("Gemini AI call failed")

            timeentries = parsed_response['timeentries']
            # Create a df from the timeentries
            df = pd.DataFrame(timeentries)
            # Parse the date
            df["Date"] = pd.to_datetime(df["Date"], format="%m-%d-%Y")
            # Add a new column with the file_id for all rows of this df
            df["FileID"] = file_id
            df["FileName"] = file.filename

            all_dfs.append(df) # Add the df to the list of all dfs
            print("=" * 50)

        # Now we have parsed all the files
        if not all_dfs:
            print("No new data to process. Returning empty DataFrame.")
            return pd.DataFrame()
        
        # db_service.disconnect() # We should disconnect in the calling function.
        
        combined_df = pd.concat(all_dfs, ignore_index=True) # glue all dfs together
        return combined_df
    
    def parse_customer_timesheets_dummy(self, *, files, template_ids, db_service: DatabaseService) -> pd.DataFrame:
        # 1) Your dummy list of dicts (from your first message)
        timeentries = [
            {
                "Date": "06-16-2025",
                "CustomerReportedHours": 7.91,
                "EmployeeName": "Harpreet Dhesi",
                "CustomerName": "ATS Corporation",
            },
            {
                "Date": "06-17-2025",
                "CustomerReportedHours": 8.80,
                "EmployeeName": "Harpreet Dhesi",
                "CustomerName": "ATS Corporation",
            },
            {
                "Date": "06-18-2025",
                "CustomerReportedHours": 8.27,
                "EmployeeName": "Harpreet Dhesi",
                "CustomerName": "ATS Corporation",
            },
            {
                "Date": "06-19-2025",
                "CustomerReportedHours": 8.02,
                "EmployeeName": "Harpreet Dhesi",
                "CustomerName": "ATS Corporation",
            },
            {
                "Date": "06-20-2025",
                "CustomerReportedHours": 7.48,
                "EmployeeName": "Harpreet Dhesi",
                "CustomerName": "ATS Corporation",
            },
            {
                "Date": "06-16-2025",
                "CustomerReportedHours": 10.0,
                "EmployeeName": "Cody Vine",
                "Location": "AtomTech - Clarkston",
                "CustomerID": "2134",
                "RateType": "TRT",
            },
            {
                "Date": "06-17-2025",
                "CustomerReportedHours": 10.0,
                "EmployeeName": "Cody Vine",
                "Location": "AtomTech - Clarkston",
                "CustomerID": "2134",
                "RateType": "OT",
            },
            {
                "Date": "06-18-2025",
                "CustomerReportedHours": 10.0,
                "EmployeeName": "Cody Vine",
                "Location": "AtomTech - Clarkston",
                "CustomerID": "2134",
                "RateType": "DBT",
            },
            {
                "Date": "06-19-2025",
                "CustomerReportedHours": 10.0,
                "EmployeeName": "Cody Vine",
                "Location": "AtomTech - Clarkston",
                "CustomerID": "2134",
                "RateType": "REG",
            },
            {
                "Date": "06-20-2025",
                "CustomerReportedHours": 10.0,
                "EmployeeName": "Cody Vine",
                "Location": "Roseville",
                "CustomerID": "2134",
                "RateType": "REG",
            },
            {
                "Date": "06-16-2025",
                "CustomerReportedHours": 11.0,
                "EmployeeName": "Luis Padilla",
                "Location": "South Carolina"
            },
            {
                "Date": "06-17-2025",
                "CustomerReportedHours": 11.0,
                "EmployeeName": "Luis Padilla",
                "Location": "South Carolina"
            },
            {
                "Date": "06-18-2025",
                "CustomerReportedHours": 11.0,
                "EmployeeName": "Luis Padilla",
                "Location": "South Carolina"
            },
            {
                "Date": "06-19-2025",
                "CustomerReportedHours": 11.0,
                "EmployeeName": "Luis Padilla",
                "Location": "South Carolina"
            },
            {
                "Date": "06-20-2025",
                "CustomerReportedHours": 11.0,
                "EmployeeName": "Luis Padilla",
                "Location": "South Carolina"
            },
            {
                "Date": "06-21-2025",
                "CustomerReportedHours": 10.0,
                "EmployeeName": "Luis Padilla",
                "Location": "South Carolina"
            },
        ]
        
        # 2) Wrap it in the same shape as the parsed AI response:
        parsed_response = {"timeentries": timeentries}
        
        # 3) Create a DataFrame just like your function does:
        df = pd.DataFrame(parsed_response["timeentries"])
        
        # 4) Parse the Date column into datetime:
        df["Date"] = pd.to_datetime(df["Date"], format="%m-%d-%Y")
        
        # (Optional) 5) If you wanted to add a FileID column:
        df["FileID"] = 1234
        df["FileName"] = "test_file.pdf"
        
        print(df)
        return df
    
    def parse_customer_names(self, *, df: pd.DataFrame, all_active_work_orders, db_service: DatabaseService) -> {pd.DataFrame}:
        # db_service = DatabaseService()
        # db_service.connect() # we are already connected...

        # If no column CustomerName, return df
        if 'CustomerName' not in df.columns:
            print("No CustomerName column found. Returning original DataFrame.")
            return df
        
        # build a mask of rows with a non-empty, non-null CustomerName
        name_mask = (
            df['CustomerName']
            .fillna('')               # turn NaN → ''
            .str.strip()              # remove leading/trailing whitespace
            .ne('')                   # keep only non-empty strings
        )

        missing_mask = ~name_mask
        rows_missing_name = df[missing_mask].copy()
        
        if not rows_missing_name.empty:
            # rows_missing_name['ErrorMessage'] = "Customer name is missing."
            print(f"Found {len(rows_missing_name)} rows with missing customer name. Skipping them.")

        df = df[name_mask].copy()

        # Filter for lines that do not have a CustomerID assigned
        if 'CustomerID' in df.columns:
            needs_id_mask = df['CustomerID'].isna() | (df['CustomerID'] == '')
            lines_without_customer_id = df[needs_id_mask]
        else:
            lines_without_customer_id = df

        # Nothing to do?
        if lines_without_customer_id.empty:
            print("No lines without customer ID. Returning original DataFrame as it is ready for next step.")
            return df        

        customer_names = lines_without_customer_id["CustomerName"].unique().tolist()

        # extract the distinct customers (name and id) from all_active_work_order_entries
        seen = set()
        active_customers = []
        for wo in all_active_work_orders:
            key = (wo['CustomerName'], wo['CustomerID'])
            if key not in seen:
                seen.add(key)
                active_customers.append(
                    {
                        'customer_id': wo['CustomerID'],
                        'customer_name': wo['CustomerName'],
                    }
                )

        # use the customer service to match the names
        customer_matches = CustomerService(db_service).match_customers(customer_names, active_customers)
        # customer_matches should return somthing like this:
        # [
        #   NameMatch(name='ACME Corporation', status=Status.MATCHED, origin=Origin.ACTIVE_WO, ids=[101]),
        #   NameMatch(name='Beta Industries, Inc.', status=Status.AMBIGUOUS, origin=Origin.ACTIVE_WO, ids=[102, 103]),
        #   NameMatch(name='Delta Co', status=Status.MATCHED, origin=Origin.DATABASE, ids=[104]),
        #   NameMatch(name='Epsilon Inc', status=Status.AMBIGUOUS, origin=Origin.DATABASE, ids=[105, 106]),
        #   NameMatch(name='Gamma & Sons LLC', status=Status.NOT_FOUND, origin=None, ids=None),
        # ]

        print(f"Customer Matches:\n{customer_matches}")

        df_result = df.copy()

        # Initialize new columns if they don't exist
        if 'CustomerID' not in df_result.columns:
            df_result['CustomerID'] = None
        if 'PossibleCustomerIDs' not in df_result.columns:
            df_result['PossibleCustomerIDs'] = None
        if 'ErrorMessage' not in df_result.columns:
            df_result['ErrorMessage'] = None

        # Create a mapping from customer names to match results for quick lookup
        customer_match_map = {match.name: match for match in customer_matches}

        # Get customer names from database for name replacement
        all_db_customers = db_service.get_customers()
        customer_id_to_name = {customer['CustomerID']: customer['CustomerName'] for customer in all_db_customers}

        # Handle results for each row that needs customer ID assignment
        for idx, row in df_result.iterrows():
            # Skip rows that already have CustomerID assigned
            if pd.notna(row['CustomerID']) and row['CustomerID'] != '':
                continue

            customer_name = row['CustomerName']
            match_result = customer_match_map.get(customer_name)

            if not match_result:
                # This shouldn't happen, but handle gracefully (Just in case bad template configured)
                df_result.at[idx, 'ErrorMessage'] = f"Customer {customer_name} was not processed during matching."
                airtanker_app.logger.error(f"Customer {customer_name} was not processed during matching. Error in FileParserService.parse_customer_names()")
                continue

            # Handle different match scenarios
            if match_result.status == Status.MATCHED:
                if match_result.origin == Origin.ACTIVE_WO:
                    # Perfect match from active work orders - assign CustomerID directly
                    df_result.at[idx, 'CustomerID'] = match_result.ids[0]
                    # Update CustomerName with the correct DB name
                    if match_result.ids[0] in customer_id_to_name:
                        df_result.at[idx, 'CustomerName'] = customer_id_to_name[match_result.ids[0]]

                elif match_result.origin == Origin.DATABASE:
                    # Match from database - add to PossibleCustomerIDs with warning
                    df_result.at[idx, 'PossibleCustomerIDs'] = str(match_result.ids[0])
                    df_result.at[idx, 'ErrorMessage'] = f"Customer {customer_name} not found on active work orders. Selected {customer_name} from database. Please make sure the customer has an active work order and reupload file. Or skip this record."
                    # Update CustomerName with the correct DB name
                    if match_result.ids[0] in customer_id_to_name:
                        df_result.at[idx, 'CustomerName'] = customer_id_to_name[match_result.ids[0]]

            elif match_result.status == Status.AMBIGUOUS:
                # Multiple matches - add all IDs to PossibleCustomerIDs
                ids_str = ','.join(map(str, match_result.ids))
                df_result.at[idx, 'PossibleCustomerIDs'] = ids_str

                if match_result.origin == Origin.ACTIVE_WO:
                    df_result.at[idx, 'ErrorMessage'] = f"Customer {customer_name} has multiple matches. Please select the correct one."
                elif match_result.origin == Origin.DATABASE:
                    df_result.at[idx, 'ErrorMessage'] = f"Customer {customer_name} has multiple matches. Select the correct one and make sure the customer has an active work order. Or skip this record."

            elif match_result.status == Status.NOT_FOUND:
                # No match found
                df_result.at[idx, 'ErrorMessage'] = f"Customer {customer_name} not found on internal records. Select one from this list, or verify work order and customer name and reupload file. Maybe a custom template for this file is needed."

        # Print summary of results
        matched_count = len(df_result[(df_result['CustomerID'].notna()) & (df_result['CustomerID'] != '')])
        error_count = len(df_result[df_result['ErrorMessage'].notna()])
        print(f"📈 Summary: {matched_count} rows with direct CustomerID assignment, {error_count} rows with errors/warnings")

        # db_service.disconnect() # we should disconnect in the calling function.

        df_result = pd.concat([df_result, rows_missing_name], ignore_index=True)

        return df_result


    def parse_employee_names(self, *, df: pd.DataFrame, all_active_work_orders, db_service: DatabaseService) -> pd.DataFrame:

        if 'EmployeeName' not in df.columns:
            print("No EmployeeName column found. Returning original DataFrame.")
            return df

        # df should not have EmployeeID assigned

        # Extract the distinct employee names from the df
        employee_names = df["EmployeeName"].unique().tolist()

        # extract the distinct employees (name and id) from all_active_work_order_entries
        seen = set()
        active_employees = []
        for wo in all_active_work_orders:
            key = (wo['FullName'], wo['EmployeeID'])
            if key not in seen:
                seen.add(key)
                active_employees.append(
                    {
                        'employee_id': wo['EmployeeID'],
                        'employee_name': wo['FullName'],
                    }
                )

        # use the employee service to match the names
        from services.EmployeeService import EmployeeService
        employee_matches = EmployeeService(db_service).match_employees(employee_names, active_employees)
        # employee_matches should return something like this:
        # [
        #   NameMatch(name='John Doe', status=Status.MATCHED, origin=Origin.ACTIVE_WO, ids=[101]),
        #   NameMatch(name='Jane Smith', status=Status.AMBIGUOUS, origin=Origin.ACTIVE_WO, ids=[102, 103]),
        #   NameMatch(name='Bob Johnson', status=Status.MATCHED, origin=Origin.DATABASE, ids=[104]),
        #   NameMatch(name='Alice Brown', status=Status.AMBIGUOUS, origin=Origin.DATABASE, ids=[105, 106]),
        #   NameMatch(name='Charlie Wilson', status=Status.NOT_FOUND, origin=None, ids=None),
        # ]

        print(f"Employee Matches:\n{employee_matches}")

        df_result = df.copy()

        # Create a mapping from employee name to match result for quick lookup
        employee_match_dict = {match.name: match for match in employee_matches}

        # Create a mapping from employee ID to employee name for database employees
        employee_id_to_name = {}
        try:
            all_db_employees = db_service.get_all_employees()
            for emp in all_db_employees:
                employee_id_to_name[emp['EmployeeID']] = emp['FullName']
        except Exception as e:
            print(f"Error getting database employees: {e}")

        # Helper function to append error messages
        def append_error_message(df, idx, new_error):
            """Append a new error message to existing error message, if any."""
            existing_error = df.at[idx, 'ErrorMessage']
            if pd.isna(existing_error) or existing_error is None or existing_error == '':
                df.at[idx, 'ErrorMessage'] = new_error
            else:
                df.at[idx, 'ErrorMessage'] = f"{existing_error}; {new_error}"

        # Add columns for results
        df_result['EmployeeID'] = None
        df_result['PossibleEmployeeIDs'] = None
        # df_result['ErrorMessage'] = None
        if 'ErrorMessage' not in df_result.columns:
            df_result['ErrorMessage'] = None

        # Process each row in the dataframe
        for idx, row in df_result.iterrows():
            employee_name = row['EmployeeName']
            match_result = employee_match_dict.get(employee_name)

            if not match_result:
                # This shouldn't happen, but handle it gracefully
                append_error_message(df_result, idx, f"No match result found for employee {employee_name}")
                continue

            if match_result.status == Status.MATCHED:
                # Single match found
                if match_result.origin == Origin.ACTIVE_WO:
                    # Match from active work orders - assign EmployeeID
                    df_result.at[idx, 'EmployeeID'] = match_result.ids[0]

                elif match_result.origin == Origin.DATABASE:
                    # Match from database - add to PossibleEmployeeIDs with warning
                    df_result.at[idx, 'PossibleEmployeeIDs'] = str(match_result.ids[0])
                    append_error_message(df_result, idx, f"Employee {employee_name} not found on active work orders. Selected {employee_name} from database. Please make sure the employee has an active work order and reupload file. Or skip this record.")
                    # Update EmployeeName with the correct DB name
                    if match_result.ids[0] in employee_id_to_name:
                        df_result.at[idx, 'EmployeeName'] = employee_id_to_name[match_result.ids[0]]

            elif match_result.status == Status.AMBIGUOUS:
                # Multiple matches found - add to PossibleEmployeeIDs
                df_result.at[idx, 'PossibleEmployeeIDs'] = ','.join(map(str, match_result.ids))
                origin_text = "active work orders" if match_result.origin == Origin.ACTIVE_WO else "database"
                append_error_message(df_result, idx, f"Multiple employees found for {employee_name} in {origin_text}. Please select the correct employee.")

            elif match_result.status == Status.NOT_FOUND:
                # No matches found
                append_error_message(df_result, idx, f"Employee {employee_name} not found in active work orders or database. Please check the name and that the employee has an active work order, or add the employee to the system.")

        return df_result

    def parse_rate_types(self, *, df: pd.DataFrame, db_service: DatabaseService) -> pd.DataFrame:
        """
        Parse RateType column and add RateTypeID column to the DataFrame.

        If RateType column exists and has values, matches them against database rate types
        and adds a RateTypeID column. Rows without RateType values are skipped (not removed).

        Args:
            df: DataFrame that may contain a 'RateType' column
            db_service: DatabaseService instance for database operations

        Returns:
            DataFrame with RateTypeID column added where RateType matches were found
        """
        # Check if RateType column exists
        if 'RateType' not in df.columns:
            print("No RateType column found. Returning original DataFrame.")
            return df

        # Create a copy to avoid modifying the original DataFrame
        df_result = df.copy()

        # Initialize RateTypeID column with None
        df_result['RateTypeID'] = None

        # Get rate types from database and create a mapping dictionary
        try:
            rate_types = db_service.get_rate_types()
            rate_type_map = {}
            for rate_type in rate_types:
                # Convert to lowercase for case-insensitive matching
                rate_type_map[rate_type['RateType'].lower()] = rate_type['ID']

            print(f"Available rate types in database: {list(rate_type_map.keys())}")

        except Exception as e:
            print(f"Error retrieving rate types from database: {e}")
            return df_result

        # Process each row that has a RateType value
        processed_count = 0
        matched_count = 0

        for idx, row in df_result.iterrows():
            rate_type_value = row.get('RateType')

            # Skip rows without RateType value (None, empty string, or NaN)
            if pd.isna(rate_type_value) or rate_type_value == '' or rate_type_value is None:
                continue

            processed_count += 1

            # Convert to string and lowercase for matching
            rate_type_str = str(rate_type_value).strip().lower()

            # Look up the rate type ID
            rate_type_id = rate_type_map.get(rate_type_str)

            if rate_type_id is not None:
                df_result.at[idx, 'RateTypeID'] = rate_type_id
                matched_count += 1
                print(f"Matched RateType '{rate_type_value}' to ID {rate_type_id}")
            else:
                print(f"Warning: RateType '{rate_type_value}' not found in database")

        print(f"Rate type processing complete: {matched_count}/{processed_count} rate types matched")

        return df_result

    def parse_location(self, *, df: pd.DataFrame, db_service: DatabaseService) -> pd.DataFrame:
        # TODO: Parse location column and add LocationID column to the DataFrame.
        # For now receive df, add LocationID column if doesnt exists, fill with None if exists, and return df.
        if 'Location' not in df.columns:
            print("No Location column found. Returning original DataFrame.")
            return df

        df['LocationID'] = None

        print("parse_location not implemented yet")
        return df

    def parse_work_orders(self, *, df: pd.DataFrame, all_active_work_orders, db_service: DatabaseService) -> pd.DataFrame:
        """
        Parse work orders and fill ProjectID and WorkOrderID columns based on EmployeeID matching.

        Logic:
        1. If employee has exactly one active work order:
           - Fill ProjectID and WorkOrderID
           - If CustomerID is filled, verify it matches; add warning if not
        2. If employee has multiple active work orders:
           - Try to match by CustomerID first (only if CustomerID column is filled)
           - If still multiple matches, try to match by date
           - If still multiple matches, save them to MultipleWorkOrderIDs column for user selection
        3. If employee not found in active work orders, add error to ErrorMessage column

        Args:
            df: DataFrame with EmployeeID column
            all_active_work_orders: List of dictionaries from get_active_work_orders
            db_service: DatabaseService instance

        Returns:
            DataFrame with ProjectID, WorkOrderID, and MultipleWorkOrderIDs columns added
        """
        print("Starting work order parsing...")

        # Create a copy to avoid modifying the original DataFrame
        df_result = df.copy()

        # Initialize new columns
        if 'ProjectID' not in df_result.columns:
            df_result['ProjectID'] = None
        if 'ProjectName' not in df_result.columns:
            df_result['ProjectName'] = None
        if 'WorkOrderID' not in df_result.columns:
            df_result['WorkOrderID'] = None
        if 'WorkOrderNumber' not in df_result.columns:
            df_result['WorkOrderNumber'] = None
        if 'MultipleWorkOrderIDs' not in df_result.columns:
            df_result['MultipleWorkOrderIDs'] = None
        if 'ErrorMessage' not in df_result.columns:
            df_result['ErrorMessage'] = None

        # Helper function to append error messages
        def append_error_message(df, idx, new_error):
            """Append a new error message to existing error message, if any."""
            existing_error = df.at[idx, 'ErrorMessage']
            if pd.isna(existing_error) or existing_error is None or existing_error == '':
                df.at[idx, 'ErrorMessage'] = new_error
            else:
                df.at[idx, 'ErrorMessage'] = f"{existing_error}; {new_error}"

        # Create a mapping from EmployeeID to work orders for quick lookup
        employee_work_orders = {}
        for wo in all_active_work_orders:
            employee_id = wo['EmployeeID']
            if employee_id not in employee_work_orders:
                employee_work_orders[employee_id] = []
            employee_work_orders[employee_id].append(wo)

        processed_count = 0
        matched_count = 0
        multiple_matches_count = 0
        not_found_count = 0

        # Process each row in the dataframe
        for idx, row in df_result.iterrows():
            employee_id = row.get('EmployeeID')

            # Skip rows without EmployeeID
            if pd.isna(employee_id) or employee_id is None:
                continue

            processed_count += 1

            # Get work orders for this employee
            work_orders = employee_work_orders.get(employee_id, [])

            if not work_orders:
                # No active work orders found for this employee
                append_error_message(df_result, idx, f"No active work order found for this employee. Suggestions: fix name or check work order in Odoo.")
                not_found_count += 1
                continue

            if len(work_orders) == 1:
                # Single work order - assign directly, but verify customer ID if available
                wo = work_orders[0]

                # Check if CustomerID matches (if CustomerID column is filled)
                customer_id = row.get('CustomerID')
                if customer_id and not pd.isna(customer_id) and customer_id != '':
                    if int(wo['CustomerID']) != int(customer_id):
                        # Customer IDs don't match - add warning but still assign work order
                        customer_name = row.get('CustomerName', 'Unknown')
                        append_error_message(df_result, idx, f"Warning: Customer mismatch. Timesheet shows '{customer_name}:{customer_id}' but work order is for '{wo['CustomerName']}:{wo['CustomerID']}'.")
                    # Else, customer IDs match - just assign the CustomerName from the work order
                    df_result.at[idx, 'CustomerName'] = wo['CustomerName']

                df_result.at[idx, 'ProjectID'] = wo['ProjectID']
                df_result.at[idx, 'ProjectName'] = wo['ProjectNumber']
                df_result.at[idx, 'WorkOrderID'] = wo['WorkOrderID']
                df_result.at[idx, 'WorkOrderNumber'] = wo['WorkOrderNumber']
                matched_count += 1
                continue

            # Multiple work orders - need to narrow down
            filtered_work_orders = work_orders.copy()

            # First try: match by CustomerID if available and column is filled
            customer_id = row.get('CustomerID')
            if customer_id and not pd.isna(customer_id) and customer_id != '':
                customer_matches = [wo for wo in filtered_work_orders if wo['CustomerID'] == customer_id]
                if customer_matches:
                    filtered_work_orders = customer_matches

                    if len(filtered_work_orders) == 1:
                        # Single match after customer filtering
                        wo = filtered_work_orders[0]
                        df_result.at[idx, 'ProjectID'] = wo['ProjectID']
                        df_result.at[idx, 'ProjectName'] = wo['ProjectName']
                        df_result.at[idx, 'WorkOrderID'] = wo['WorkOrderID']
                        df_result.at[idx, 'WorkOrderNumber'] = wo['WorkOrderNumber']
                        matched_count += 1
                        continue

            # Second try: match by date if still multiple matches
            if len(filtered_work_orders) > 1:
                row_date = row.get('Date')
                if row_date and not pd.isna(row_date):
                    # Convert row date to datetime if it's not already
                    if isinstance(row_date, str):
                        try:
                            row_date = pd.to_datetime(row_date)
                        except:
                            pass  # If conversion fails, skip date matching

                    if isinstance(row_date, pd.Timestamp):
                        date_matches = []
                        for wo in filtered_work_orders:
                            start_date = wo.get('StartDate')
                            end_date = wo.get('AnticipatedEndDate')

                            # Convert dates if they're strings
                            if isinstance(start_date, str):
                                try:
                                    start_date = pd.to_datetime(start_date)
                                except:
                                    start_date = None
                            if isinstance(end_date, str):
                                try:
                                    end_date = pd.to_datetime(end_date)
                                except:
                                    end_date = None

                            # Check if row date falls within work order date range
                            if start_date and row_date >= start_date:
                                if end_date is None or row_date <= end_date:
                                    date_matches.append(wo)

                        if date_matches:
                            filtered_work_orders = date_matches

                            if len(filtered_work_orders) == 1:
                                # Single match after date filtering
                                wo = filtered_work_orders[0]
                                df_result.at[idx, 'ProjectID'] = wo['ProjectID']
                                df_result.at[idx, 'ProjectName'] = wo['ProjectName']
                                df_result.at[idx, 'WorkOrderID'] = wo['WorkOrderID']
                                df_result.at[idx, 'WorkOrderNumber'] = wo['WorkOrderNumber']
                                matched_count += 1
                                continue

            # Still multiple matches - save them for user selection
            work_order_ids = [str(wo['WorkOrderID']) for wo in filtered_work_orders]
            df_result.at[idx, 'MultipleWorkOrderIDs'] = ','.join(work_order_ids)
            multiple_matches_count += 1

        print(f"Work order processing complete:")
        print(f"  Processed: {processed_count} rows")
        print(f"  Matched: {matched_count} work orders")
        print(f"  Multiple matches: {multiple_matches_count} rows")
        print(f"  Not found: {not_found_count} rows")

        return df_result