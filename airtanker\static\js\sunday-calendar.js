/**
 * SundayCalendar - A reusable calendar component for selecting Sundays
 * Highlights entire weeks on hover, allows week selection by clicking any day,
 * and emphasizes Sunday as the selected day with visual indicators.
 */
class SundayCalendar {
  constructor(containerId, options = {}) {
    this.container = document.getElementById(containerId);
    this.currentDate = new Date();
    this.selectedDate = null;
    this.hoveredDate = null;
    this.onDateSelect = options.onDateSelect || null;
    this.hiddenInputId = options.hiddenInputId || null; // For form integration
    
    this.monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];
    
    this.dayNames = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
    
    this.init();
  }

  init() {
    this.container.innerHTML = this.getCalendarHTML();
    this.attachEventListeners();
    this.render();
    
    // Set initial date to most recent Sunday
    this.setMostRecentSunday();
  }

  getCalendarHTML() {
    return `
      <div class="sunday-calendar">
        <div class="calendar-header">
          <button type="button" class="nav-btn" id="prevMonth">‹</button>
          <h2 class="month-title" id="monthTitle" style="color: #5f5f5f"></h2>
          <button type="button" class="nav-btn" id="nextMonth">›</button>
        </div>
        
        <div class="day-headers">
          ${this.dayNames.map(day => `<div class="day-header">${day}</div>`).join('')}
        </div>
        
        <div class="calendar-weeks" id="calendarWeeks"></div>
        
        <div class="selected-date" id="selectedDate" style="display: none;">
          <p class="selected-label">Selected Sunday:</p>
          <p class="selected-value" id="selectedValue"></p>
        </div>
        
        <div class="instructions">
          <p>Click any date to select its Sunday. Hover over any day to highlight the week and emphasize Sunday.</p>
        </div>
      </div>
    `;
  }

  attachEventListeners() {
    // Use event delegation to handle dynamically created buttons
    this.container.addEventListener('click', (e) => {
      if (e.target.id === 'prevMonth') {
        e.preventDefault();
        e.stopPropagation();
        this.navigateMonth('prev');
      } else if (e.target.id === 'nextMonth') {
        e.preventDefault();
        e.stopPropagation();
        this.navigateMonth('next');
      }
    });
  }

  navigateMonth(direction) {
    const newDate = new Date(this.currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    this.currentDate = newDate;
    this.render();
  }

  render() {
    // Update month title
    document.getElementById('monthTitle').textContent = 
      `${this.monthNames[this.currentDate.getMonth()]} ${this.currentDate.getFullYear()}`;

    // Generate calendar days
    const weeks = this.generateWeeks();
    const weeksContainer = document.getElementById('calendarWeeks');
    weeksContainer.innerHTML = '';

    weeks.forEach((week, weekIndex) => {
      const weekElement = document.createElement('div');
      weekElement.className = 'calendar-week';
      weekElement.dataset.weekIndex = weekIndex;

      week.forEach((date, dayIndex) => {
        const dayElement = document.createElement('div');
        dayElement.className = 'calendar-day';
        dayElement.textContent = date.getDate();
        dayElement.dataset.date = date.toISOString();

        // Add classes based on date properties
        if (!this.isInCurrentMonth(date)) {
          dayElement.classList.add('other-month');
        }
        if (this.isSunday(date)) {
          dayElement.classList.add('sunday');
          // Add Sunday indicator dot
          const dot = document.createElement('div');
          dot.className = 'sunday-dot';
          dayElement.appendChild(dot);
        }

        // Event listeners
        dayElement.addEventListener('click', () => this.handleDateClick(date));
        dayElement.addEventListener('mouseenter', () => this.handleDateHover(date));
        dayElement.addEventListener('mouseleave', () => this.handleMouseLeave());

        weekElement.appendChild(dayElement);
      });

      // Week hover events
      weekElement.addEventListener('mouseenter', () => this.handleWeekHover(week));
      weekElement.addEventListener('mouseleave', () => this.handleWeekLeave());

      weeksContainer.appendChild(weekElement);
    });
  }

  generateWeeks() {
    const firstDayOfMonth = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
    const lastDayOfMonth = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);

    // Get the first Monday to show
    const startDate = new Date(firstDayOfMonth);
    const firstDayOfWeek = firstDayOfMonth.getDay();
    const daysToSubtract = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;
    startDate.setDate(startDate.getDate() - daysToSubtract);

    // Get the last day to show (ensure we end on Sunday)
    const endDate = new Date(lastDayOfMonth);
    const lastDayOfWeek = lastDayOfMonth.getDay();
    const daysToAdd = lastDayOfWeek === 0 ? 0 : 7 - lastDayOfWeek;
    endDate.setDate(endDate.getDate() + daysToAdd);

    // Generate all days
    const days = [];
    const current = new Date(startDate);
    while (current <= endDate) {
      days.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }

    // Group into weeks
    const weeks = [];
    for (let i = 0; i < days.length; i += 7) {
      weeks.push(days.slice(i, i + 7));
    }

    return weeks;
  }

  handleDateClick(date) {
    const sunday = this.getSundayOfWeek(date);
    this.selectedDate = sunday;
    
    // Update UI
    this.updateSelectedDateDisplay();
    this.updateDayStyles();
    
    // Update hidden input if specified
    if (this.hiddenInputId) {
      const hiddenInput = document.getElementById(this.hiddenInputId);
      if (hiddenInput) {
        hiddenInput.value = sunday.toISOString().split('T')[0];
      }
    }
    
    // Call callback if provided
    if (this.onDateSelect) {
      this.onDateSelect(sunday);
    }
  }

  handleDateHover(date) {
    this.hoveredDate = date;
    this.updateDayStyles();
  }

  handleMouseLeave() {
    this.hoveredDate = null;
    this.updateDayStyles();
  }

  handleWeekHover(week) {
    // Highlight the entire week
    const weekElements = document.querySelectorAll('.calendar-week');
    weekElements.forEach(weekEl => {
      const weekDays = Array.from(weekEl.children).map(day => new Date(day.dataset.date));
      const isThisWeek = weekDays.some(weekDay => 
        week.some(hoverDay => this.isSameDay(weekDay, hoverDay))
      );
      
      if (isThisWeek) {
        weekEl.classList.add('week-hovered');
      }
    });
  }

  handleWeekLeave() {
    document.querySelectorAll('.calendar-week').forEach(week => {
      week.classList.remove('week-hovered');
    });
  }

  updateDayStyles() {
    document.querySelectorAll('.calendar-day').forEach(dayEl => {
      const date = new Date(dayEl.dataset.date);
      
      // Reset classes
      dayEl.classList.remove('selected', 'hovered', 'week-highlighted', 'sunday-highlighted');
      
      // Selected date
      if (this.selectedDate && this.isSameDay(date, this.selectedDate)) {
        dayEl.classList.add('selected');
      }
      
      // Hovered date
      if (this.hoveredDate && this.isSameDay(date, this.hoveredDate)) {
        dayEl.classList.add('hovered');
      }
      
      // Week highlighting
      if (this.hoveredDate && this.isInSameWeek(date, this.hoveredDate)) {
        dayEl.classList.add('week-highlighted');
        
        // Special Sunday highlighting
        if (this.isSunday(date)) {
          dayEl.classList.add('sunday-highlighted');
        }
      }
    });
  }

  updateSelectedDateDisplay() {
    const selectedDateEl = document.getElementById('selectedDate');
    const selectedValueEl = document.getElementById('selectedValue');
    
    if (this.selectedDate) {
      selectedDateEl.style.display = 'block';
      selectedValueEl.textContent = this.selectedDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } else {
      selectedDateEl.style.display = 'none';
    }
  }

  // Utility methods
  isSunday(date) {
    return date.getDay() === 0;
  }

  isSameDay(date1, date2) {
    return date1.getDate() === date2.getDate() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getFullYear() === date2.getFullYear();
  }

  isInCurrentMonth(date) {
    return date.getMonth() === this.currentDate.getMonth();
  }

  getMondayOfWeek(date) {
    const monday = new Date(date);
    const dayOfWeek = monday.getDay();
    const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
    monday.setDate(monday.getDate() - daysToSubtract);
    return monday;
  }

  getSundayOfWeek(date) {
    const monday = this.getMondayOfWeek(date);
    const sunday = new Date(monday);
    sunday.setDate(sunday.getDate() + 6);
    return sunday;
  }

  isInSameWeek(date1, date2) {
    const monday1 = this.getMondayOfWeek(date1);
    const monday2 = this.getMondayOfWeek(date2);
    return this.isSameDay(monday1, monday2);
  }

  // Public methods
  getSelectedSunday() {
    return this.selectedDate;
  }

  setMostRecentSunday() {
    const today = new Date();
    const dayOfWeek = today.getDay(); // Sunday - 0, Monday - 1, ..., Saturday - 6
    const difference = dayOfWeek % 7; // Calculate difference to get back to the previous Sunday
    const mostRecentSunday = new Date(today.setDate(today.getDate() - difference));
    
    this.handleDateClick(mostRecentSunday);
  }

  // Set a specific date (useful for initialization)
  setSelectedDate(dateString) {
    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      this.handleDateClick(date);
    }
  }
}
