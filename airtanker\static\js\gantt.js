let today = new Date(),
    day = 1000 * 60 * 60 * 24,
    week = 7 * day, // Define a week
    each = Highcharts.each,
    reduce = Highcharts.reduce,
    btnShowDialog = document.getElementById('btnShowDialog'),
    btnRemoveTask = document.getElementById('btnRemoveSelected'),
    btnAddTask = document.getElementById('btnAddTask'),
    btnCancelAddTask = document.getElementById('btnCancelAddTask'),
    addTaskDialog = document.getElementById('addTaskDialog'),
    selectEmployee = document.getElementById('selectEmployee'),
    selectWorkOrder = document.getElementById('selectWorkOrder'),
    filterYear = document.getElementById('filterYear'),
    btnAddEmployee = document.getElementById('btnAddEmployee'),
    employees = ['<PERSON>', '<PERSON>', '<PERSON>'],
    workOrders = ['Work Order 1', 'Work Order 2', 'Work Order 3'], // Available work orders
    tasks = [
        {
            start: today.getTime() + 2 * week,
            end: today.getTime() + 5 * week,
            name: 'Work Order 1',
            id: 'prototype',
            y: 0,
            year: 2023
        },
        {
            start: today.getTime() + 6 * week,
            name: 'Work Order 2',
            milestone: true,
            dependency: 'prototype',
            id: 'proto_done',
            y: 0,
            year: 2023
        },
        {
            start: today.getTime() + 7 * week,
            end: today.getTime() + 11 * week,
            name: 'Work Order 3',
            dependency: 'proto_done',
            y: 0,
            year: 2023
        },
        {
            start: today.getTime() + 5 * week,
            end: today.getTime() + 8 * week,
            name: 'Work Order 1',
            y: 1,
            year: 2023
        },
        {
            start: today.getTime() + 9 * week,
            end: today.getTime() + 10 * week,
            name: 'Work Order 2',
            y: 1,
            year: 2024
        },
        {
            start: today.getTime() + 9 * week,
            end: today.getTime() + 11 * week,
            name: 'Work Order 3',
            id: 'testing',
            y: 2,
            year: 2024
        },
        {
            start: today.getTime() + 11.5 * week,
            end: today.getTime() + 12.5 * week,
            name: 'Work Order 1',
            dependency: 'testing',
            y: 2,
            year: 2024
        }
    ];

// Set to 00:00:00:000 today
today.setUTCHours(0);
today.setUTCMinutes(0);
today.setUTCSeconds(0);
today.setUTCMilliseconds(0);

// Update disabled status of the remove button, depending on whether or not we
// have any selected points.
function updateRemoveButtonStatus() {
    const chart = this.series.chart;
    // Run in a timeout to allow the select to update
    setTimeout(function () {
        btnRemoveTask.disabled = !chart.getSelectedPoints().length ||
            isAddingTask;
    }, 10);
}

// Filter tasks by year
function filterTasksByYear(year) {
    return tasks.filter(task => year ? new Date(task.start).getFullYear() == year : true);
}

// Create the chart
const chart = Highcharts.ganttChart('container', {

    chart: {
        spacingLeft: 1,
        scrollablePlotArea: {
            minWidth: 1000,
            scrollPositionX: 1
        }
    },

    title: {
        text: 'Interactive Gantt Chart'
    },

    subtitle: {
        text: 'Drag and drop points to edit'
    },

    plotOptions: {
        series: {
            animation: false, // Do not animate dependency connectors
            dragDrop: {
                draggableX: true,
                draggableY: true,
                dragMinY: 0,
                dragMaxY: employees.length - 1,
                dragPrecisionX: week / 3 // Snap to part of a week
            },
            dataLabels: {
                enabled: true,
                format: '{point.name}',
                style: {
                    cursor: 'default',
                    pointerEvents: 'none'
                }
            },
            allowPointSelect: true,
            point: {
                events: {
                    select: updateRemoveButtonStatus,
                    unselect: updateRemoveButtonStatus,
                    remove: updateRemoveButtonStatus
                }
            }
        }
    },

    yAxis: {
        type: 'category',
        categories: employees,
        accessibility: {
            description: 'Employees'
        },
        min: 0,
        max: employees.length - 1
    },

    xAxis: {
        min: today.getTime(),
        max: today.getTime() + (52 * week),
        scrollbar: {
            enabled: true
        }
    },

    tooltip: {
        xDateFormat: '%a %b %d, %H:%M'
    },

    series: [{
        name: 'Work Orders / Projects',
        data: filterTasksByYear(filterYear.value)
    }]
});

/* Add button handlers for add/remove tasks */

btnRemoveTask.onclick = function () {
    const points = chart.getSelectedPoints();
    each(points, function (point) {
        point.remove();
        tasks = tasks.filter(task => task.id !== point.id);
    });
};

btnShowDialog.onclick = function () {
    // Update employee list
    let empInnerHTML = '';
    each(employees, function (employee, index) {
        empInnerHTML += '<option value="' + index + '">' + employee + '</option>';
    });
    selectEmployee.innerHTML = empInnerHTML;

    // Update work order list
    let workOrderInnerHTML = '';
    each(workOrders, function (workOrder) {
        workOrderInnerHTML += '<option value="' + workOrder + '">' + workOrder + '</option>';
    });
    selectWorkOrder.innerHTML = workOrderInnerHTML;

    // Show dialog by removing "hidden" class
    addTaskDialog.className = 'overlay visible';
    isAddingTask = true;

    // Focus employee select field
    selectEmployee.focus();
};

btnAddTask.onclick = function () {
    // Get values from dialog
    const series = chart.series[0],
        employeeIndex = parseInt(selectEmployee.value, 10),
        workOrder = selectWorkOrder.value,
        y = employeeIndex;
    let undef,
        maxEnd = reduce(series.points, function (acc, point) {
            return point.y === y && point.end ? Math.max(acc, point.end) : acc;
        }, 0);

    const milestone = chkMilestone.checked || undef;

    // Empty category
    if (maxEnd === 0) {
        maxEnd = today.getTime();
    }

    const newTask = {
        start: maxEnd + (milestone ? week : 0),
        end: milestone ? undef : maxEnd + week,
        y: y,
        name: workOrder,
        milestone: milestone,
        year: new Date(maxEnd).getFullYear()
    };

    // Add the point
    series.addPoint(newTask);
    tasks.push(newTask);

    // Hide dialog
    addTaskDialog.className += ' hidden';
    isAddingTask = false;
};

btnCancelAddTask.onclick = function () {
    // Hide dialog
    addTaskDialog.className += ' hidden';
    isAddingTask = false;
};

// Handle year filter change
filterYear.onchange = function () {
    chart.series[0].setData(filterTasksByYear(filterYear.value));
};

btnAddEmployee.onclick = function () {
    const employeeName = prompt("Enter the new employee's name:");
    if (employeeName) {
        employees.push(employeeName);
        chart.yAxis[0].setCategories(employees);
        chart.yAxis[0].update({ max: employees.length - 1 });
    }
};
