# old external parse
# def parse_external(self, files):
#         '''Parses the external files and looks up the person via name
#         from the timesheet_records array. It adds the hours to the external
#         hours part of the class and triggers error if needed. With notes.'''
#         for file in files:
#             if file:
#                 if file.filename.endswith('.xlsx'):
#                     workbook = load_workbook(filename=file)  # Load the workbook

#                     # iterate through each sheet in the workbook
#                     for sheet_name in workbook.sheetnames:
#                         sheet = workbook[sheet_name]                   

#                          # check if it's a placeholder sheet, or empty sheet (first 10 rows, and 10 columns for empty)
#                         if contains_emails(sheet) or all(sheet.cell(row=row, column=col).value is None for row in range(1, 11) for col in range(1, 11)):
#                             continue # go to next sheet if so
                        
#                         # get the indexes of the different data we need
#                         date_idx = find_date_column_excel(sheet)
#                         name_idx = find_name_column_excel(sheet)                        
#                         job_idx = 2
#                         customer_idx = 3
#                         employee_name = ""

#                         # hour_idx  TODO find hours column from the sheet
#                         print(f"For {file.filename}: Sheet: {sheet.title}, Name: {name_idx}, Date: {date_idx}")
                        
#                         for row in sheet.iter_rows(min_row=2, values_only=True):  # Skip header row
#                             # Get the data
#                             try:                                

#                                 try:
#                                     val = row[name_idx]
#                                     # get employee name (this is basically our key)
#                                     if val is None or not isinstance(val, str) or is_date_string(val):
#                                         raise Exception
#                                     employee_name = HumanName(re.sub(r'\([^)]*\)', '', val.replace("Employee:", ""))) ## remove the () and everything in them. Hard code kind of, special circuumstance.
#                                 except:
#                                     # if error, means that name wasn't in that cell
#                                     pass

#                                 # get the date
#                                 curr_date = get_date_exception(row[date_idx])
#                                 #curr_date = datetime.strptime(curr_date, '%Y-%m-%d %H:%M:%S')
#                                 curr_date = curr_date.strftime('%m-%d-%Y')

#                                 for cell in row:
#                                     hours_suspected, hours = is_two_digit_or_float(cell)
#                                     if hours_suspected:
#                                         break
#                                 curr_record = TimesheetRecord(employee_name.full_name, curr_date, curr_date, date_range=curr_date + " - " + curr_date, external_regular_hours=hours)
#                                 self.timesheet_records.append(curr_record)
#                                 print(employee_name.full_name, curr_date, hours)

#                             except Exception as e:
#                                 # means date wasn't formatted correctly
#                                 #print(e)
#                                 continue

def to_float(element: any) -> float :
    if element is None:
        return 0
    try:
        return float(element)
    except ValueError:
         return 0

def to_str(element: any, placeholder = '') -> str :
    if element is None:
        return placeholder
    try:
        return str(element)
    except ValueError:
        return placeholder

# OLD TD parsing csv. Using Contractor sheets now.
# if file.filename.endswith('.csv'): # TD
#                     csv_data = file.read().decode('utf-8')  # Read file contents as UTF-8 string                    
#                     csv_reader = csv.reader(csv_data.splitlines())  # Create CSV reader
#                     header = next(csv_reader, None)
#                     curr_record = None
                        
#                     name_column_index, name_column_header = find_name_column_csv(csv_data)
#                     date_column_index, date_column_header = find_date_column_in_csv(csv_data)
#                     print(f"For {file.filename}: Name: {name_column_index} | {name_column_header}, Date: {date_column_index} | {date_column_header}")

#                     job_idx = 2 # this will be parsed actually with the name.
#                     date_idx = 3
#                     hour_idx = 11
#                     wo_idx = name_column_index

#                     curr_job = ""
#                     curr_date_range = ""
#                     curr_hours = ""
#                     curr_wo = ""                    
                    
#                     for current_row, next_row in lookahead(csv_reader):                          
                    
#                         if current_row[job_idx] != '':
#                             curr_job = current_row[job_idx]
#                         if current_row[date_idx] != '':
#                             curr_date_range = current_row[date_idx]

                    
#                             if curr_date_range.count('-') == 1: # Check to see if the period is a single day, or is a date range.
#                                 start_time = datetime.strptime(curr_date_range.split("-")[0].strip(), "%d %b %Y").strftime("%m/%d/%Y")
#                                 end_time = datetime.strptime(curr_date_range.split("-")[1].strip(), "%d %b %Y").strftime("%m/%d/%Y")
#                             else: # if single day
#                                 times = curr_date_range.replace('-', ' ')
#                                 start_time = datetime.strptime(times.strip(), "%d %b %Y").strftime("%m/%d/%Y")
#                                 end_time =  datetime.strptime(times.strip(), "%d %b %Y").strftime("%m/%d/%Y")
                        

#                         curr_wo, employee_name, hour_type = get_name_td(current_row[name_column_index])                        

#                         # check if cell contains a name
#                         if employee_name != None:

#                             curr_customer = get_customer_from_work_order(curr_wo)

#                             # if so, check the array for an existing record.
#                             curr_record = find_timerecord_by_daterange(self.timesheet_records, employee_name, curr_job, curr_date_range)
                            
                            
#                             curr_hours = current_row[hour_idx] # get the hours from the sheet.

#                             if curr_record is not None:
#                                 add_hours(curr_record, hour_type, curr_hours)                                
#                             else:
#                                 # Add new record into the array if non-existing                                
#                                 curr_record = Timesheet_Weekly_Record(employee_name,
#                                                              start_time, end_time, 
#                                                              work_order=curr_wo, 
#                                                              job_name=curr_job, 
#                                                              customer=curr_customer,
#                                                              date_range=curr_date_range)
#                                 add_hours(curr_record, hour_type, curr_hours)
#                                 self.timesheet_records.append(curr_record)

#                         else:
#                             continue




# Odoo csv old code:                        

                    #         curr_object = find_timerecord(self.timesheet_records, get_name_regular(row[name_column_index]), curr_job, curr_date_range)

                    #         # adjust the current object to add the hours if it exists
                    #         # if the object with name and customer and job are found, update the hours.
                    #         if curr_object is not None:
                    #             curr_object.internal_hours = round(float(curr_object.internal_hours) + float(row[5]), 2)

                    #             curr_object_start_date = curr_object.start_date
                    #             row_0_date = row[0]

                    #             # Perform comparisons with datetime objects
                    #             if (curr_object_start_date > row_0_date):
                    #                 curr_object.end_date = row_0_date
                    #             elif (curr_object_start_date < row_0_date):
                    #                 curr_object.end_date = curr_object.start_date
                    #                 curr_object.start_date = row_0_date
                    #         else:
                    #             self.timesheet_records.append()
                    # elif talent_desk_template:



# TD Excel old code:
                    # elif talent_desk_template:
                    #     rows = list(sheet.iter_rows(values_only=True))
                    #     for i in range(1, len(rows)):  # Skip header and start from the second row
                    #         current_row = rows[i]
                    #         next_row = rows[i + 1] if i + 1 < len(rows) else None

                    #         # Your existing logic for handling rows goes here, adjusted for Excel handling
                    #         if current_row[1].replace(" ","") != "":

                    #             # Example for handling dates
                    #             if current_row[3].count('-') == 1:  # Date range
                    #                 start_time = datetime.strptime(current_row[3].split("-")[0].strip(), "%d %b %Y").strftime("%m/%d/%Y")
                    #                 end_time = datetime.strptime(current_row[3].split("-")[1].strip(), "%d %b %Y").strftime("%m/%d/%Y")
                    #             else: # if single day
                    #                 times = current_row[3].replace('-', ' ')
                    #                 start_time = datetime.strptime(times.strip(), "%d %b %Y").strftime("%m/%d/%Y")
                    #                 end_time =  datetime.strptime(times.strip(), "%d %b %Y").strftime("%m/%d/%Y")
                    #             curr_record = TimesheetRecord(current_row[1], start_time, end_time, current_row[11], 0, current_row[2], "AtomTech")
                                
                    #         # if no name in the row, this indicates that it has the hour information.
                    #         # Extract the hours and add it to the current record object
                    #         else:
                    #             if (curr_record.internal_hours == ''):
                    #                 curr_record.internal_hours = current_row[11]
                    #             else:
                    #                 curr_record.internal_hours = round(float(curr_record.internal_hours) + float(current_row[11]), 2)
                            
                    #         # Check if the next row has a name, which will mean we can add the current entry to the array
                    #         # A new one will be created in the next iteration because of the data in the next row.
                    #         if (next_row is not None and next_row[1].replace(" ","") != ""): # Check to see if the next row is a new entry -> if the next row is not empty, append the record.
                    #             self.timesheet_records.append(curr_record)     

# Customer csv sheet old:


                # elif file.filename.endswith('.csv'):
                #     csv_data = file.read().decode('utf-8')  # Read file contents as UTF-8 string                    
                #     name_column_index, name_column_header = find_name_column_csv(csv_data)
                #     date_column_index, date_column_header = find_date_column_in_csv(csv_data)
                #     print(f"For {file.filename}: Name: {name_column_index} | {name_column_header}, Date: {date_column_index} | {date_column_header}")
                #     continue
                #     workbook = load_workbook(filename=file)  # Load the workbook
                #     for sheet_name in workbook.sheetnames:
                #         sheet = workbook[sheet_name]
                #         if contains_emails(sheet):
                #             continue
                #         date_column_index = find_date_column_excel(sheet, date_formats)
                #         name_column = find_name_column_excel(sheet)
                #         print(f"For {file.filename}: Sheet: {sheet.title}, Name: {name_column}, Date: {date_column_index}")
                #         continue





