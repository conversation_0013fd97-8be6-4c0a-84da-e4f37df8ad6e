import re
from nameparser import <PERSON><PERSON><PERSON>
from services.DatabaseService import DatabaseService
import textwrap
from datetime import datetime, timedelta
from services.DateParser import get_date_exception
from dotenv import load_dotenv
from services.ContractorService import get_name_error, get_standard_error, get_travel_error
from services.ContractorService import is_weekending_sunday
from fuzzywuzzy import process
from main import airtanker_app
import os 



import camelot
import pandas as pd
from IPython.display import display
import re
from datetime import datetime
from nameparser import Human<PERSON><PERSON>


def parse_hti_data_from_pdf(df,
                        error_entry_id_counter,
                        file_name,
                        all_active_work_order_entries,
                        file_id):
    
    employee_id = None
    
    # Extract just the names from all_employee_names for matching
    names_to_match_against = set([entry["FullName"] for entry in all_active_work_order_entries])

    # connect to the database
    database_service = DatabaseService()
    database_service.connect()
    
    errors = []
    name_errors = []

    try:
        # Regular expression to find the date in the filename
        match = re.search(r'WE (\d{8})', file_name)
        week_ending = datetime.strptime("01019999", '%m%d%Y')

        if match:
            # Extract the date part
            date_str = match.group(1)
            # Convert the date string to a datetime object
            week_ending = datetime.strptime(date_str, '%m%d%Y')
        else:
            error_message = f"Couldn't determine the week ending date based on the filename. Expecting 'WE MMddYYYY'"
            # Raise error that week ending couldnt be determined
            error = {
                'ID':error_entry_id_counter,
                'FileName': file_name,
                "Message":error_message,
                'ReportedProjectNumber':'',
                'WorkOrderEntry':{
                },
                'Employee': '',
                'EmployeeID':'',
                'Hours': []
            }
            
            errors.append(error)
            error_entry_id_counter += 1
            database_service.delete_data_with_fileID(file_id)
            raise Exception(error_message)
        
        # Remove empty rows
        pd.set_option('future.no_silent_downcasting', True)
        df.replace("", float("NaN"), inplace=True)
        df.dropna(how='all', inplace=True)

        for index, row in df.iterrows():
            first_column_value = row[0]
            if pd.notna(first_column_value):

                # Check if it's the last row in the table, exit if so
                if "grandtotal" in first_column_value.lower().replace(" ",""):
                    break

                # Check row is the employees total hours for the week
                elif "total" in first_column_value.lower().replace(" ",""):

                    name = first_column_value.replace("Total", "").strip()
                    total_hours = row[13]

                    employee_name = HumanName(name)

                    best_match_name, score = process.extractOne(employee_name.full_name, names_to_match_against)
                    if score < 90:
                        name_matching_enabled = os.getenv('ENABLE_NAME_MATCHING', 'false')
                        if name_matching_enabled == 'true':
                            # Check the DB if name exists in view.
                            query = """
                                SELECT TOP (1000) [FullName]
                                    ,[EmpID]
                                    ,[TS_Name]
                                    ,[NameCount]
                                FROM [dbo].[View_NameSelectionCounts]
                                WHERE TS_NAME = ?
                                ORDER by NameCount
                            """
                            results = database_service.execute_query(query, employee_name.full_name)
                            if results:
                                best_match_name = results[0]["FullName"]
                                score = 100
                        if score < 90:
                            no_name_found_error = True
                            # Put into errors
                            matches = process.extract(employee_name.full_name, names_to_match_against, limit=5)
                            for match in matches:
                                match_name, score = match
                                # Get the hours and get the work orders of each
                                emps_work_orders = []
                                project_numbers = []
                                for wo_entry in all_active_work_order_entries:
                                    if wo_entry["FullName"] == match_name:
                                        emps_work_orders.append(wo_entry)
                                        curr_project_number = wo_entry['ProjectNumber'].strip()
                                        project_numbers.append(curr_project_number)

                                work_order_entry = {}
                                employee_id = emps_work_orders[0]['EmployeeID']
                                for entry in emps_work_orders:
                                    if entry['ProjectNumber'].strip() in work_order_entry:
                                        # append it
                                        work_order_entry[entry['ProjectNumber'].strip()].append({
                                            entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                                            })
                                    else:
                                        work_order_entry[entry["ProjectNumber"].strip()] = [{
                                            entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                                        }]

                                matching_entry = None
                                for error_entry in name_errors:
                                    if error_entry['OriginalName'] == employee_name.full_name:
                                        matching_entry = error_entry
                                        break  # Stop searching once a match is found
                                if matching_entry:
                                    data = {
                                        'EmployeeName':match_name,
                                        'EmployeeID':employee_id,
                                        'WorkOrders':work_order_entry
                                    }
                                    matching_entry['EmployeeData'].append(data)
                                else:
                                    name_error = {
                                        'ID':error_entry_id_counter,
                                        'OriginalName':employee_name.full_name,
                                        'FileName': file_name,
                                        'Message': f"Original Name: <strong>{employee_name.full_name}</strong>. No direct matches in the database. Please select correct employee.",
                                        'EmployeeData':[{
                                            'EmployeeName':match_name, 'EmployeeID':employee_id, 'WorkOrders':work_order_entry # It's the Project number and work order numbers
                                        }],
                                        'Hours':[]
                                    }
                                    name_errors.append(name_error)
                                    error_entry_id_counter += 1
                    
                    emps_work_orders = []
                    project_numbers = []
                    for wo_entry in all_active_work_order_entries:
                        if wo_entry["FullName"] == best_match_name:
                            emps_work_orders.append(wo_entry)
                            curr_project_number = wo_entry['ProjectNumber'].strip()
                            project_numbers.append(curr_project_number)
                            
                    if score > 89:
                        no_name_found_error = False
                        employee_id = emps_work_orders[0]["EmployeeID"]

                    # Check if there was a name mismatch
                    if no_name_found_error:
                            matching_entry = None
                            for error_entry in name_errors:
                                if error_entry['OriginalName'] == employee_name.full_name:
                                    matching_entry = error_entry
                                    break  # Stop searching once a match is found
                            if matching_entry:
                                data = {
                                    'Date':week_ending,
                                    'FileID':file_id,
                                    'Hours':total_hours,
                                    'TaskID':""
                                }
                                matching_entry["Hours"].append(data)

                    elif len(emps_work_orders) > 1:
                        
                        # Put into errors
                        work_order_entry = {}
                        for entry in emps_work_orders:
                            if entry['ProjectNumber'].strip() in work_order_entry:
                                # append it
                                work_order_entry[entry['ProjectNumber'].strip()].append({
                                    entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                                    })
                            else:
                                work_order_entry[entry["ProjectNumber"].strip()] = [{
                                    entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                                }]
                        error = {
                            'ID':error_entry_id_counter,
                            'FileName': file_name,
                            'Message': f"More than one work order assigned.",
                            'WorkOrderEntry':work_order_entry,
                            'Employee': employee_name.full_name,                    
                            'EmployeeID':employee_id,
                            'Hours': [
                                {'Date': week_ending, 'TaskID': "", 'Hours': total_hours, 'FileID':file_id,},
                                # More entries as needed
                            ]
                        }

                        # Iterate over the list of errors to find the match
                        matching_entry = None
                        for error_entry in errors:
                            if error_entry['EmployeeID'] == employee_id:
                                matching_entry = error_entry
                                break  # Stop searching once a match is found
                        if matching_entry:
                            # If found, add to the current hours
                            hours_match = None
                            for hours_entry in matching_entry['Hours']:
                                if hours_entry['Date'] == week_ending:
                                    hours_match = hours_entry
                                    break  # Stop searching once a match is found

                            if hours_match:
                                hours_match['Hours'] += total_hours                      
                            else:
                                matching_entry['Hours'].append({
                                    'Date':week_ending,
                                    'TaskID':"",
                                    'Hours':total_hours,
                                    'FileID':file_id
                                })
                        else:
                            errors.append(error)
                            # Increase the ID counter after each new entry into the error logs
                            error_entry_id_counter += 1
                    else:
                        # Put the hours into DB
                        employee_id = emps_work_orders[0]["EmployeeID"]
                        project_id = emps_work_orders[0]['ProjectID']
                        customer_id = emps_work_orders[0]['CustomerID']
                        work_order_id = emps_work_orders[0]['WorkOrderID']                
                        database_service.insert_customer_reported_hours_lastRow(employee_id=employee_id, 
                                                                        date=week_ending, 
                                                                        customer_reported_hours=total_hours,
                                                                        project_id=project_id,
                                                                        customer_id=customer_id,
                                                                        file_id=file_id,
                                                                        work_order_id=work_order_id,
                                                                        task_id=None,
                                                                        location_id=None)
    except Exception as e:
        airtanker_app.logger.debug(f'Error while parsing PDF file for HTI: {e}')
    finally:
        database_service.disconnect()

    return errors, name_errors, error_entry_id_counter # match work order name to name is spreadsheet



def parse_hti_internal(sheet,
               sheet_name,
               file_name,
               file_id,
               error_entry_id_counter,
               selected_week_ending,
               database_service, # type: DatabaseService
               all_active_work_order_entries):
    
    load_dotenv()

    errors = []
    name_errors = []

    data_offset = 5

    #####
    travel_hours_col = "H"
    travel_hours_column = 8
    work_hours_column = 13
    work_hours_col = "M"

    start_row = 7
    step = 5
    
    max_row = 37
    row = start_row
    #####
    names_to_match_against = set([entry["FullName"] for entry in all_active_work_order_entries])

    # Get static row data
    contractor_name = HumanName(sheet.cell(row=1, column=2).value)
    week_ending = sheet.cell(row=3, column=2).value
    if week_ending and not is_weekending_sunday(week_ending):
            error = {
                    'ID': error_entry_id_counter,
                    'FileName': file_name,
                    "Message":f"WeekEnding : {week_ending} in the file is not a Sunday. Please correct this and reupload.",
                    'ReportedProjectNumber':'',
                    'WorkOrderEntry':{
                    },
                    'Employee': '',
                    'EmployeeID': contractor_name.full_name,
                    'Hours': []
            }
            errors.append(error)
            error_entry_id_counter += 1
            database_service.delete_data_with_fileID(fileID=file_id)
            airtanker_app.logger.debug(f"Error with {file_name}: file had 'Your Name' in the name field")
            return errors, name_errors, error_entry_id_counter
    
    no_name_found_error = False
    emps_work_orders = []
    project_numbers = []
    work_order_numbers = []

    ### --------------------------------------------------------- ###
    
                    # Begin Preliminary Checks #
        # Find matching employee based off name in timesheet #

    ### --------------------------------------------------------- ###

    # Check the name in the timesheet with the names in active work orders.
    best_match_name, score = process.extractOne(contractor_name.full_name, names_to_match_against)

    # If there's no direct match between the timesheet and work orders
    if score < 90:
        name_matching_enabled = os.getenv('ENABLE_NAME_MATCHING', 'false')

        # if the name matching is enabled - previous names
        if name_matching_enabled == 'true':

            # Check the DB if name exists in view.
            query = """
                SELECT TOP (1000) [FullName]
                    ,[EmpID]
                    ,[TS_Name]
                    ,[NameCount]
                FROM [dbo].[View_NameSelectionCounts]
                WHERE TS_NAME = ?
                ORDER by NameCount
            """
            results = database_service.execute_query(query, contractor_name.full_name)
            if results:
                best_match_name = results[0]["FullName"]
                score = 100

        # if no results for the name matching, or name matching is off
        if score < 90:
            # create a name error element in the name_errors array
            ## We'll add the hours to this element later. 
            ### That's why we set no_name_found_error to true
            no_name_found_error, name_error = get_name_error(all_active_work_order_entries,
                                                            contractor_name,
                                                            names_to_match_against,
                                                            name_errors,
                                                            error_entry_id_counter,
                                                            file_name)
            if name_error:
                name_errors.append(name_error)
                error_entry_id_counter += 1

    # If name matching is found, or there's a direct match, then get the associated work orders based on the found name.
    if score > 89:
        for wo_entry in all_active_work_order_entries:
            if wo_entry["FullName"] == best_match_name:
                emps_work_orders.append(wo_entry)
                
                curr_project_number = wo_entry['ProjectNumber'].strip()
                project_numbers.append(curr_project_number)

                curr_work_order_number = wo_entry["WorkOrderNumber"].strip()
                work_order_numbers.append(curr_work_order_number)

        curr_employee_id = emps_work_orders[0]['EmployeeID']

    job_num = str(sheet.cell(row=1, column=6).value)
    job_num = re.sub(r'\([^)]*\)', '', job_num)
    task_id_support = database_service.find_or_create_task("Support")
    task_id_travel = database_service.find_or_create_task("Travel")

    # Parse the worksheet.
    try:
        week_ending_subtract = 7
        project_number_mismatch = False
        #### Check correct project number, only if we found a single employee match. Multiple matches we skip this. 
        if not no_name_found_error:
            reported_project_number = job_num
            if reported_project_number:
                best_match_project_number, score = process.extractOne(str(reported_project_number), project_numbers) # Check which WO it is, if multiple.
                best_match_wo_number, wo_score = process.extractOne(str(reported_project_number), work_order_numbers) # Check which WO it is, if multiple.

                if score < 90 and wo_score < 90:
                    project_number_mismatch = True
            else:
                # There's no reported job number.
                project_number_mismatch = True
        while row <= max_row:
            
            daily_travel_hours = 0 # reset to zero each day
            travel_hours = sheet.cell(row=row, column=8).value

            daily_working_hours = 0
            work_hours = sheet.cell(row=row, column=13).value
            
            if travel_hours:
                daily_travel_hours += travel_hours
            if work_hours:
                daily_working_hours += work_hours
            
            try:
                week_ending_subtract -= 1
                curr_date = week_ending - timedelta(days=week_ending_subtract) # cell contains equation, doing it manually. 
                if daily_travel_hours > 0 or daily_working_hours > 0:
                    if project_number_mismatch:
                        # add travel horus
                        if  daily_travel_hours > 0:
                            error = get_standard_error(emps_work_orders,
                                            error_entry_id_counter,
                                            file_name,
                                            reported_project_number if isinstance(reported_project_number, str) else 0,
                                            contractor_name,
                                            curr_employee_id,
                                            curr_date,
                                            None,
                                            daily_travel_hours,
                                            file_id,
                                            errors,
                                            selected_week_ending,
                                            True)
                            if error:
                                errors.append(error)
                                error_entry_id_counter += 1
                        # add working hours
                        if  daily_working_hours > 0:
                            error = get_standard_error(emps_work_orders,
                                            error_entry_id_counter,
                                            file_name,
                                            reported_project_number if isinstance(reported_project_number, str) else 0,
                                            contractor_name,
                                            curr_employee_id,
                                            curr_date,
                                            task_id_support if isinstance(task_id_support, int) else None,
                                            daily_working_hours,
                                            file_id,
                                            errors,
                                            selected_week_ending,
                                            True)
                            if error:
                                errors.append(error)
                                error_entry_id_counter += 1

                    elif no_name_found_error:
                        matching_entry = None
                        for error_entry in name_errors:
                            if error_entry['OriginalName'] == contractor_name.full_name:
                                matching_entry = error_entry
                                break  # Stop searching once a match is found
                        if matching_entry:
                            if daily_travel_hours > 0:
                                data = {
                                    'Date':curr_date,
                                    'FileID':file_id,
                                    'Hours':daily_travel_hours,
                                    'TaskID':None,
                                    "UnknownTravel": True
                                }
                                matching_entry["Hours"].append(data)
                            
                            if daily_working_hours > 0:
                                data = {
                                    'Date':curr_date,
                                    'FileID':file_id,
                                    'Hours':daily_working_hours,
                                    'TaskID':task_id_support,
                                    "UnknownTravel": False
                                }
                                matching_entry["Hours"].append(data)
                            else:
                                database_service.delete_data_with_fileID(file_id)
                                raise Exception("There as a name mismatch but couldn't find the error in the name_errors array.")
                    else:

                        filtered_work_orders = [wo for wo in emps_work_orders if wo.get("ProjectNumber").strip() == best_match_project_number]
                        
                        if filtered_work_orders:
                            project_id = filtered_work_orders[0]["ProjectID"]
                            work_order_id = filtered_work_orders[0]["WorkOrderID"]
                            customer_id = filtered_work_orders[0]["CustomerID"]
                        else:
                            filtered_work_orders = [wo for wo in emps_work_orders if wo.get("WorkOrderNumber") == best_match_wo_number]
                            
                            project_id = filtered_work_orders[0]["ProjectID"]
                            work_order_id = filtered_work_orders[0]["WorkOrderID"]
                            customer_id = filtered_work_orders[0]["CustomerID"]

                        if daily_travel_hours > 0:
                            database_service.insert_hours_internal(employee_id=curr_employee_id,
                                                                date=curr_date,
                                                                employee_reported_hours=daily_travel_hours,
                                                                project_id=project_id,
                                                                customer_id=customer_id,
                                                                file_id=file_id,
                                                                work_order_id=work_order_id,
                                                                task_id = task_id_travel if isinstance(task_id_travel, int) else None,
                                                                location_id=None)
                            
                        if daily_working_hours > 0:
                            database_service.insert_hours_internal(employee_id=curr_employee_id,
                                                                date=curr_date,
                                                                employee_reported_hours=daily_working_hours,
                                                                project_id=project_id,
                                                                customer_id=customer_id,
                                                                file_id=file_id,
                                                                work_order_id=work_order_id,
                                                                task_id = task_id_support if isinstance(task_id_support, int) else None,
                                                                location_id=None)
                        
            except Exception as e:
                if "formulas" in e.args[0]:
                    airtanker_app.logger.debug(f"Error with {file_name}: {e}")
                    break
                continue

            row += step
        
    except Exception as e:
        print(f"Error in file at fileId {file_id} with error {e} for {contractor_name.full_name}")

    return errors, name_errors, error_entry_id_counter
