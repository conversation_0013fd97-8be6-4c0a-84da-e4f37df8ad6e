{% extends 'base.html' %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.datatables.net/fixedcolumns/5.0.3/css/fixedColumns.dataTables.css">
<link rel="stylesheet" href="https://cdn.datatables.net/select/2.1.0/css/select.dataTables.css">
<style>
    div#loading {
        width: 500px;
        height: 500px;
        display: none;
        background: url(/static/assets/loadingOdoo.gif) no-repeat center center;
        background-size: contain;
        cursor: wait;
        z-index: 1000;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        
        box-shadow: none; /* Ensure no shadow is applied */
        filter: none; /* Remove any filters that might create a shadow effect */
    }
</style>
{% endblock %} 

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdn.datatables.net/fixedcolumns/5.0.3/js/dataTables.fixedColumns.js"></script>
<script src="https://cdn.datatables.net/fixedcolumns/5.0.3/js/fixedColumns.dataTables.js"></script>
<script src="https://cdn.datatables.net/select/2.1.0/js/dataTables.select.js"></script>
<script src="https://cdn.datatables.net/select/2.1.0/js/select.dataTables.js"></script>
<script type="text/javascript" charset="utf8" src="static/js/progress.js"></script>
<script type="text/javascript">
    $( document ).ready(function() {
        // $( "#progressLabel" ).append("<p>Step 2: Export Timesheets to Odoo</p>");
        // $( "#progressBar" ).attr("aria-valuenow", "66")
        loading();

        fetch('/get_timesheets?type=external')
        .then(response => response.json())
        .then(data => {
            if (data.redirect_url) {
                alert("Error: No timesheets available for selected week ending");
                window.location.href = "/exports";
            } else {
                if (data.status == 'error' && data.errors) {
                    handleErrors(data.message, data.errors, data.projects);
                } else {
                    showTimesheets(data.data);
                    doneLoading();
                }
            }
        })
        .catch(error => {
            console.error('Error fetching data:', error);
            alert(`Error fetching data: ${error}`);
            window.location.href = "/exports";
        });
    });

    function loading() {
        $("#loading").show();
        $("#content").hide(); 
    }

    function doneLoading() {
        $("#loading").hide();
        $("#content").show(); 
    }

    async function handleErrors(message, errors, projects) {
        // Let's collect the available tasks to include by project, based on what's missing
        var missingTasks = errors.filter(function(x){ return x.TaskID == null; });
        var availableTasks = [];
        for (let i = 0; i < missingTasks.length; i++) {
            let error = missingTasks[i];
            if (error.ProjectID) {
                tasks = await fetchTasks(error.ProjectID);
                if (tasks) {
                    availableTasks.push(...tasks);
                }              
            }
        }

        var table = $( '#errorsTable' ).DataTable({
            data: errors,
            columns: [
                {
                    data: 'ID',
                    visible: false,
                    searchable: false
                },
                { data: "Date" },
                { data: "EmployeeName" },
                { 
                    data: "ProjectName",
                    render: function (data, type, row) {
                        return buildProjectCell(data, row.ProjectID, projects);                        
                    }
                },
                {
                    data: "TaskName",
                    render: function (data, type, row) {
                        return buildTaskCell(data, row.ProjectID, row.TaskID, availableTasks);
                    }
                },
                { data: "Hours" }
            ],
            paging: false,
            searching: false,
            ordering: false,
            scrollCollapse: true
        });

        $( '#errorsMessage' ).append('<span>' + message + '</span>');
        const errorsModal = new bootstrap.Modal('#errorsModal', {
            backdrop: 'static',
            keyboard: false
        });
        errorsModal.show();
    }

    async function fetchTasks(projectId) {
        try {
            let response = await fetch(`/get_tasks/${projectId}`);
            return await response.json();
        } catch (error) {
            console.error('Error fetching tasks:', error);
            return [];
        }
    }

    function buildProjectCell(reported, projectId, projects) {
        var selected = '';
        if (projectId != null) {
            var project = projects.filter(function(x){ return x.id == projectId; });
            if (project.length == 1) {
                selected += `<p>${project[0].name}</p>`;
            } else if (project.length <= 0) {
                selected += '<p class="text-danger">[Invalid Project ID]</p>';
            } else {
                selected += '<p class="text-danger">[Missing]</p>';
            }            
        } else {
            selected += `
                <select onchange="reloadRowTasks()">
                    ${projects.map(project => `<option value="${project.id}" ${project.id === projectId ? 'selected' : ''}>${project.name}</option>`).join('')}
                </select>
            `;
        }

        var reported = `
            <p class="fst-italic text-secondary my-2">
                <span class="text-start">Reported:</span><br />
                <span>${reported}</span>
            </p>`;
        
        return selected + reported;
    }

    function buildTaskCell(reported, projectId, taskId, availableTasks) {
        var selected = '';
        if (projectId != null) {
            if (taskId != null) {

            } else {
                let projectTasks = availableTasks.filter(function(x){ return x.project_id && x.project_id[0] == projectId; });
                selected = `
                    <select>
                        ${projectTasks.map(task => `<option value="${task.id}" ${task.id === taskId ? 'selected' : ''}>${task.name}</option>`).join('')}
                    </select>
                `;            
            }
        } else {
            selected = '<p class="text-danger">[Missing ProjectID]</p>';
        }
        
        var reported = `
            <p class="fst-italic text-secondary my-2">
                <span class="text-start">Reported:</span><br />
                <span>${reported}</span>
            </p>
        `;

        return selected + reported;
    }

    function showTimesheets(timesheets) {
        var table = $( '#timesheetTable' ).DataTable({
            data: timesheets,
            fixedColumns: {
                start: 2
            },
            columns: [
                {
                    //data: "IsChecked",
                    orderable: false,
                    render: DataTable.render.select(),
                    targets: 0
                },
                { data: "Date" },
                { data: "ContractorName" },
                { data: "ProjectName" },
                { data: "TaskName" },
                { data: "Description" },
                { data: "Hours" }
            ],
            paging: false,
            searching: false,
            scrollCollapse: true,
            select: {
                style: 'multi'
            }
        });
    }

    function submitForm() {
        var table = $( '#errorsTable' ).DataTable();

    }

    function exportTimesheets() {
        var table = $( '#timesheetTable' ).DataTable();
        let selectedData = table.rows({selected: true}).data().toArray();

        if (selectedData.length <= 0) {
            return;
        }
        loading();
        fetch('/post_timesheets', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(selectedData),
            credentials: 'same-origin'
        })
        .then(response => {
            if (!response.ok) {
                // If the server response is not ok (e.g., 500 Internal Server Error), throw an error
                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }
            doneLoading();
            return response.json();
        })
        .then(data => {
            if (data.task_id){
                startProgress(`/progress/${data.task_id}`);
            }
            else{
                console.log("No task_id");
            }
        })
        .catch(error => {
            // Handle any errors that occurred during the fetch or due to a server error
            console.error('Error:', error);
            doneLoading();
        })
    }
</script>
{% endblock %}

{% block content %}
<div class="progress-container" style="display: none;">
    <div id="progressLabel" class="fadeIn text-center mt-4"></div>
    <div id="progressBar" class="progress w-75 mx-auto" role="progressbar" aria-valuemin="0" aria-valuemax="100">            
        <div class="progress-bar bg-success"></div>
    </div>
</div>
<div id="loading"></div>
<div id="content">
    <div id="buttons" class="d-flex justify-content-center my-4">
        <button class="btn btn-primary" onclick="exportTimesheets()">Looks Good!</button>
    </div>
    <div id="timesheet-container" class="timesheet-container">
        <table id="timesheetTable" class="table table-sm align-middle" style="width: 100%">
            <thead class="text-center">
                <th></th>
                <th style="width: 10%;">Date</th>
                <th>Employee</th>
                <th>Project</th>
                <th>Task</th>
                <th>Description</th>
                <th style="width: 10%;">Hours Spent</th>
            </thead>
            <tbody class="text-center">
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block modal %}
<div id="errorsModal" class="modal fade" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 id="errorsHeader" class="modal-title">Resolve Errors</h5>
                <button type="button" class="close btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="errorsMessage" class="text-danger"></p>
                <table id="errorsTable" class="table">
                    <thead class="text-center">
                        <th></th>
                        <th>Date</th>
                        <th class="w-25">Employee Name</th>
                        <th class="w-25">Project</th>
                        <th class="w-25">Task</th>
                        <th>Hours</th>
                    </thead>
                </table>
            </div>
            <div class="modal-footer">
                <!-- <button class="btn btn-secondary">Cancel</button> -->
                <button class="btn btn-primary" onclick="submitForm()">Update</button>
            </div>
        </div>        
    </div>
</div>
{% endblock %}