class Timesheet_Weekly_Record:
    def __init__(self, 
                 employee_name, 
                 start_date, 
                 end_date, 
                 date_range, 
                 internal_daily_hours=0,
                 internal_weekly_hours=0,                 
                 internal_regular_hours=0,
                 internal_overtime_hours=0,
                 internal_doubletime_hours=0,
                 external_daily_hours=0,
                 external_weekly_hours=0,
                 external_regular_hours=0, 
                 external_overtime_hours=0,
                 external_doubletime_hours=0,
                 job_name="", 
                 customer="", 
                 error=0,
                 notes="",
                 work_order =""):
        self.employee_name = employee_name
        self.start_date = start_date
        self.end_date = end_date
        self.date_range = date_range
        self.job_name = job_name
        self.customer = customer
        self.error = error
        self.notes = notes
        self.work_order = work_order

        # Set regular, overtime, and doubletime hours
        self.internal_daily_hours = internal_daily_hours # dictionary, date as key
        self.internal_weekly_hours = internal_weekly_hours 

        self.internal_regular_hours = internal_regular_hours
        self.internal_overtime_hours = internal_overtime_hours
        self.internal_doubletime_hours = internal_doubletime_hours

        # external hours
        self.external_daily_hours = external_daily_hours # dictionary, date as key
        self.external_weekly_hours = external_weekly_hours 

        self.external_regular_hours = external_regular_hours
        self.external_overtime_hours = external_overtime_hours
        self.external_doubletime_hours = external_doubletime_hours


    def add_daily_hours(self, date, hours):
        self.daily_hours[date] = hours # add to dictionary
    
    def add_weekly_hours(self, hours):
        self.weekly_hours = float(self.weekly_hours) + float(hours)


    # Properties to calculate total hours
    @property
    def internal_total_hours(self):
        return (self.internal_regular_hours + self.internal_overtime_hours + self.internal_doubletime_hours)

    @property
    def external_total_hours(self):
        return (self.external_regular_hours + self.external_overtime_hours + self.external_doubletime_hours)


    def to_dict(self):
        return {
            'employee_name': self.employee_name.full_name,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'date_range': self.date_range,
            'internal_daily_hours': self.internal_daily_hours,
            'internal_weekly_hours': self.internal_weekly_hours,
            'internal_regular_hours': self.internal_regular_hours,
            'internal_overtime_hours': self.internal_overtime_hours,
            'internal_doubletime_hours': self.internal_doubletime_hours,
            'external_daily_hours': self.external_daily_hours,
            'external_weekly_hours': self.external_weekly_hours,
            'external_regular_hours': self.external_regular_hours,
            'external_overtime_hours': self.external_overtime_hours,
            'external_doubletime_hours': self.external_doubletime_hours,
            'job_name': self.job_name,
            'customer': self.customer,
            'error': self.error,
            'notes': self.notes,
            'work_order': self.work_order
        }

    def __repr__(self):
        return (f"Timesheet_Weekly_Record(employee_name={self.employee_name!r}, start_date={self.start_date!r}, "
                f"end_date={self.end_date!r}, date_range={self.date_range!r}, "
                f"internal_daily_hours={self.internal_daily_hours}, internal_weekly_hours={self.internal_weekly_hours}, "
                f"internal_regular_hours={self.internal_regular_hours}, internal_overtime_hours={self.internal_overtime_hours}, "
                f"internal_doubletime_hours={self.internal_doubletime_hours}, external_daily_hours={self.external_daily_hours}, "
                f"external_weekly_hours={self.external_weekly_hours}, external_regular_hours={self.external_regular_hours}, "
                f"external_overtime_hours={self.external_overtime_hours}, external_doubletime_hours={self.external_doubletime_hours}, "
                f"job_name={self.job_name!r}, customer={self.customer!r}, error={self.error}, notes={self.notes!r}, "
                f"work_order={self.work_order!r})")


class TimesheetRecord:
    def __init__(self, name, start_date, end_date, internal_hours, external_hours, job_name, customer, error = 0, notes = ""):
        self.name = name
        self.start_date = start_date
        self.end_date = end_date
        self.internal_hours = internal_hours
        self.external_hours = external_hours
        self.job_name = job_name
        self.customer = customer
        self.error = error
        self.notes = notes

    def to_dict(self):
        return {
            "name": self.name,
            "start_date": self.start_date,
            "end_date": self.end_date,
            "internal_hours": self.internal_hours,
            "external_hours": self.external_hours,
            "job_name": self.job_name,
            "customer": self.customer,
            "error":self.error,
            "notes":self.notes

        }

    def __repr__(self):
        return (f"TimesheetRecord(Name={self.name}, StartDate={self.start_date}, "
                f"EndDate={self.end_date}, Internal Hours={self.internal_hours}, " 
                f"External Hours={self.external_hours}, JobName={self.job_name}, "
                f"Customer={self.customer}), Error={self.error}, Notes={self.notes}")
