/**
 * SundayCalendar Integration Utilities
 * Helper functions to easily integrate the SundayCalendar component
 * with existing forms and replace current date picker implementations
 */

/**
 * Replace a standard date input with the SundayCalendar component
 * @param {string} inputId - ID of the input element to replace
 * @param {string} containerId - ID of the container where calendar will be rendered
 * @param {Object} options - Additional options for the calendar
 */
function replaceDateInputWithCalendar(inputId, containerId, options = {}) {
  const inputElement = document.getElementById(inputId);
  const container = document.getElementById(containerId);
  
  if (!inputElement || !container) {
    console.error('Input element or container not found');
    return null;
  }
  
  // Hide the original input
  inputElement.style.display = 'none';
  
  // Create calendar with integration options
  const calendarOptions = {
    hiddenInputId: inputId,
    onDateSelect: (selectedSunday) => {
      // Update the original input value
      inputElement.value = selectedSunday.toISOString().split('T')[0];
      
      // Trigger change event on the original input
      const changeEvent = new Event('change', { bubbles: true });
      inputElement.dispatchEvent(changeEvent);
      
      // Call custom callback if provided
      if (options.onDateSelect) {
        options.onDateSelect(selectedSunday);
      }
    },
    ...options
  };
  
  const calendar = new SundayCalendar(containerId, calendarOptions);
  
  // If the input already has a value, set it in the calendar
  if (inputElement.value) {
    calendar.setSelectedDate(inputElement.value);
  }
  
  return calendar;
}

/**
 * Create a modal version of the calendar
 * @param {string} modalId - ID of the modal element
 * @param {string} inputId - ID of the input element to update
 * @param {Object} options - Additional options
 */
function createModalCalendar(modalId, inputId, options = {}) {
  const modal = document.getElementById(modalId);
  const input = document.getElementById(inputId);
  
  if (!modal || !input) {
    console.error('Modal or input element not found');
    return null;
  }
  
  // Create calendar container inside modal
  const calendarContainer = document.createElement('div');
  calendarContainer.id = `${modalId}-calendar`;
  
  // Find modal body and append calendar
  const modalBody = modal.querySelector('.modal-body');
  if (modalBody) {
    modalBody.appendChild(calendarContainer);
  } else {
    modal.appendChild(calendarContainer);
  }
  
  const calendar = new SundayCalendar(calendarContainer.id, {
    onDateSelect: (selectedSunday) => {
      input.value = selectedSunday.toISOString().split('T')[0];
      
      // Trigger change event
      const changeEvent = new Event('change', { bubbles: true });
      input.dispatchEvent(changeEvent);
      
      // Close modal if using Bootstrap
      if (window.bootstrap && bootstrap.Modal) {
        const modalInstance = bootstrap.Modal.getInstance(modal);
        if (modalInstance) {
          modalInstance.hide();
        }
      }
      
      if (options.onDateSelect) {
        options.onDateSelect(selectedSunday);
      }
    },
    ...options
  });
  
  return calendar;
}

/**
 * Create a compact inline calendar for dashboard widgets
 * @param {string} containerId - ID of the container element
 * @param {Object} options - Additional options
 */
function createCompactCalendar(containerId, options = {}) {
  const container = document.getElementById(containerId);
  
  if (!container) {
    console.error('Container element not found');
    return null;
  }
  
  // Add compact class to container
  container.classList.add('compact');
  
  const calendar = new SundayCalendar(containerId, {
    ...options
  });
  
  return calendar;
}

/**
 * Initialize calendar for timesheet wizard pages
 * Replaces the existing sundayPicker input with the calendar component
 * @param {Object} options - Additional options
 */
function initTimesheetWizardCalendar(options = {}) {
  // Check if we're on a timesheet wizard page
  const sundayPicker = document.getElementById('sundayPicker');
  
  if (!sundayPicker) {
    console.log('Sunday picker not found, skipping calendar initialization');
    return null;
  }
  
  // Create container for calendar
  const calendarContainer = document.createElement('div');
  calendarContainer.id = 'sunday-calendar-container';
  calendarContainer.style.marginBottom = '20px';
  
  // Insert calendar container after the label but before the input
  const label = sundayPicker.previousElementSibling;
  if (label && label.tagName === 'LABEL') {
    label.parentNode.insertBefore(calendarContainer, label.nextSibling);
  } else {
    sundayPicker.parentNode.insertBefore(calendarContainer, sundayPicker);
  }
  
  return replaceDateInputWithCalendar('sundayPicker', 'sunday-calendar-container', options);
}

/**
 * Initialize calendar for dashboard/approval pages
 * Replaces the existing weekEndingPicker input with the calendar component
 * @param {Object} options - Additional options
 */
function initDashboardCalendar(options = {}) {
  const weekEndingPicker = document.getElementById('weekEndingPicker');
  
  if (!weekEndingPicker) {
    console.log('Week ending picker not found, skipping calendar initialization');
    return null;
  }
  
  // Create container for calendar
  const calendarContainer = document.createElement('div');
  calendarContainer.id = 'week-ending-calendar-container';
  calendarContainer.style.marginTop = '20px';
  
  // Insert calendar container after the date picker container
  const datePickerContainer = weekEndingPicker.closest('.date-picker-container');
  if (datePickerContainer) {
    datePickerContainer.appendChild(calendarContainer);
  } else {
    weekEndingPicker.parentNode.insertBefore(calendarContainer, weekEndingPicker.nextSibling);
  }
  
  return replaceDateInputWithCalendar('weekEndingPicker', 'week-ending-calendar-container', {
    onDateSelect: (selectedSunday) => {
      // Call the existing fetchData or fetchWeeklyData functions if they exist
      if (typeof fetchData === 'function') {
        fetchData();
      } else if (typeof fetchWeeklyData === 'function') {
        fetchWeeklyData();
      } else if (typeof fetchInitialData === 'function') {
        fetchInitialData();
      }
      
      if (options.onDateSelect) {
        options.onDateSelect(selectedSunday);
      }
    },
    ...options
  });
}

/**
 * Auto-initialize calendars based on page context
 * Call this function on DOMContentLoaded to automatically set up calendars
 */
function autoInitializeCalendars() {
  // Check for timesheet wizard pages
  if (document.getElementById('sundayPicker')) {
    initTimesheetWizardCalendar();
  }
  
  // Check for dashboard/approval pages
  if (document.getElementById('weekEndingPicker')) {
    initDashboardCalendar();
  }
}

/**
 * Utility function to get the most recent Sunday
 * @returns {Date} The most recent Sunday
 */
function getMostRecentSunday() {
  const today = new Date();
  const dayOfWeek = today.getDay();
  const difference = dayOfWeek % 7;
  const mostRecentSunday = new Date(today.setDate(today.getDate() - difference));
  return mostRecentSunday;
}

/**
 * Utility function to check if a date is Sunday
 * @param {Date|string} date - Date to check
 * @returns {boolean} True if the date is Sunday
 */
function isSunday(date) {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.getDay() === 0;
}

/**
 * Legacy compatibility function - maintains existing checkSunday functionality
 * @param {HTMLInputElement} input - The input element
 */
function checkSunday(input) {
  const selectedDate = new Date(input.value);
  if (selectedDate.getDay() !== 6) { // Note: Your existing code checks for Saturday (6), not Sunday (0)
    alert('Please select a Sunday.');
    // Set to most recent Sunday
    const mostRecentSunday = getMostRecentSunday();
    input.value = mostRecentSunday.toISOString().split('T')[0];
  }
}

/**
 * Legacy compatibility function - maintains existing setMostRecentSunday functionality
 */
function setMostRecentSunday() {
  const sundayPicker = document.getElementById('sundayPicker');
  const weekEndingPicker = document.getElementById('weekEndingPicker');
  
  const mostRecentSunday = getMostRecentSunday();
  const formattedDate = mostRecentSunday.toISOString().split('T')[0];
  
  if (sundayPicker) {
    sundayPicker.value = formattedDate;
  }
  
  if (weekEndingPicker) {
    weekEndingPicker.value = formattedDate;
  }
}

// Export functions for module usage if needed
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    SundayCalendar,
    replaceDateInputWithCalendar,
    createModalCalendar,
    createCompactCalendar,
    initTimesheetWizardCalendar,
    initDashboardCalendar,
    autoInitializeCalendars,
    getMostRecentSunday,
    isSunday,
    checkSunday,
    setMostRecentSunday
  };
}
