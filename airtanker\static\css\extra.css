/* layout */
.splash {
  background: url('/static/assets/spray.webp') no-repeat center/cover;
  height: 80vh;
}
.start-screen {
  height: 50vh;
}
.chart-container {
  position: relative;
  margin: auto;
}

/* fire/extinguished buttons */
.fire,
.extinguished {
  padding: 10px 20px;
  font-size: 16px;
  display: inline-block;
  position: relative;
  cursor: pointer;
  color: white;
}

.fire {
  background-color: darkred;
  border: 2px solid darkred;
  box-shadow: 0 0 8px yellow;
  text-shadow: 0 0 2px yellow, 0 0 5px orange;
  animation: burn 1s infinite;
}

.extinguished {
  background-color: grey;
  border: 2px solid grey;
  box-shadow: none;
  text-shadow: none;
}

@keyframes burn {
  0%, 100% { opacity: 1; }
  50%      { opacity: 0.8; }
}
