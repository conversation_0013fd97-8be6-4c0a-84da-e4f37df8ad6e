# Base Image
FROM python:3.10-bookworm AS base

# upgrade/update package sources
RUN pip install --upgrade pip
RUN apt-get update

# get curl for healthchecks
RUN apt-get -y install curl nano

# Move venv out of /home/<USER>/home/<USER>
ENV VIRTUAL_ENV=/opt/venv
ENV PATH="$VIRTUAL_ENV/bin:$PATH"

# LDAP system dependencies
RUN apt-get -y install libldap2-dev libsasl2-dev

# cv2 dependencies
RUN apt-get -y install ffmpeg libsm6 libxext6

# MSSQL system dependencies
RUN apt-get -y install apt-transport-https
RUN curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add -
RUN curl https://packages.microsoft.com/config/debian/10/prod.list > /etc/apt/sources.list.d/mssql-release.list
RUN apt-get update
RUN ACCEPT_EULA=Y apt-get install msodbcsql17 --assume-yes

# PDF conversion libraries
RUN DEBIAN_FRONTEND=noninteractive
RUN apt-get --no-install-recommends install libreoffice-core-nogui libreoffice-calc-nogui -y
RUN apt-get install -y default-jre libreoffice-java-common ghostscript python3-tk
#RUN apt-get install unoconv

# permissions and nonroot user for tightened security
RUN adduser --disabled-login airtanker
RUN mkdir /home/<USER>/ && chown -R airtanker:airtanker /home/<USER>
WORKDIR /home/<USER>

# Copy only requirements.txt and install deps
COPY requirements.txt .

RUN python -m venv $VIRTUAL_ENV && pip install --upgrade pip && pip install -r requirements.txt

# copy all the files to the container
COPY --chown=airtanker:airtanker . .

USER airtanker

EXPOSE 5000

### Development Image ###
FROM base AS dev
RUN mv /home/<USER>/.env.dev /home/<USER>/.env && pip install debugpy
# EXPOSE 5678
USER airtanker
# Keeps Python from generating .pyc files in the container
ENV PYTHONDONTWRITEBYTECODE=1
# Turns off buffering for easier container logging
ENV PYTHONUNBUFFERED=1

### Production Image ###
FROM base AS prod
USER root
RUN pip install gunicorn && mv /home/<USER>/.env.prod /home/<USER>/.env
USER airtanker