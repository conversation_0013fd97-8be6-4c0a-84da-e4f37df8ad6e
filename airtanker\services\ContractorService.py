from nameparser import <PERSON><PERSON><PERSON>
from datetime import datetime, timedelta
from services.DatabaseService import DatabaseService
from fuzzywuzzy import process
from main import airtanker_app
from dotenv import load_dotenv
import os 


def parse_contractors(sheet, file_id,
                      file_name,
                      error_entry_id_counter,
                      selected_week_ending,
                    database_service, # type: DatabaseService
                      all_active_work_order_entries):
    '''This parse each contractor file'''

    load_dotenv()

    errors = []
    name_errors = []

    contractor_name_col = 9
    contractor_name_row = 4

    date_row = 9

    week_ending_row = 4
    week_ending_col = 12

    job_num_col = 2
    job_desc_col = 3
    op_code_col = 4
        
    day_start_col = 5

    row_start = 10
    row_end = 17

    week_ending = sheet.cell(row=week_ending_row, column=week_ending_col).value
    week_ending_val = week_ending
    contractor_name = HumanName(sheet.cell(row=contractor_name_row, column=contractor_name_col).value)

    try:
        if isinstance(selected_week_ending, str):
            selected_week_ending = datetime.strptime(selected_week_ending, '%Y-%m-%d')
            selected_week_ending = datetime.date(selected_week_ending)
    except Exception as e:
        error = {
                'ID': error_entry_id_counter,
                'FileName': file_name,
                "Message":f"There was an issue parsing the requested week ending date. Must be in YYYY-MM-DD format!",
                'ReportedProjectNumber':'',
                'WorkOrderEntry':{
                },
                'Employee': '',
                'EmployeeID': contractor_name.full_name,
                'Hours': []
            }
        errors.append(error)
        error_entry_id_counter += 1
        database_service.delete_data_with_fileID(fileID=file_id)
        return errors, name_errors, error_entry_id_counter

    try:
        if datetime.date(week_ending_val) != selected_week_ending:
            error = {
                    'ID': error_entry_id_counter,
                    'FileName': file_name,
                    "Message":f"This file had a week ending <strong>{datetime.date(week_ending_val)}</strong> where selected week ending was {selected_week_ending}. Please select a different week ending and reupload or fix the week ending in the timesheet.",
                    'ReportedProjectNumber':'',
                    'WorkOrderEntry':{
                    },
                    'Employee': '',
                    'EmployeeID': contractor_name.full_name,
                    'Hours': []
            }
            errors.append(error)
            error_entry_id_counter += 1
            database_service.delete_data_with_fileID(fileID=file_id)
            return errors, name_errors, error_entry_id_counter
    except Exception as e:
        if week_ending_val != selected_week_ending:
            error = {
                    'ID': error_entry_id_counter,
                    'FileName': file_name,
                    "Message":f"This file had a week ending <strong>{week_ending_val}</strong> where selected week ending was {selected_week_ending}. Please select a different week ending and reupload or fix the week ending in the timesheet.",
                    'ReportedProjectNumber':'',
                    'WorkOrderEntry':{
                    },
                    'Employee': '',
                    'EmployeeID': contractor_name.full_name,
                    'Hours': []
            }
            errors.append(error)
            error_entry_id_counter += 1
            database_service.delete_data_with_fileID(fileID=file_id)
            return errors, name_errors, error_entry_id_counter

    
    # Extract just the names from all_employee_names for matching
    names_to_match_against = set([entry["FullName"] for entry in all_active_work_order_entries])

    # 1. Get the name of the contractor from the sheet

    # Check if they actually filled out the name field., If not throw error, and skip parsing this file.
    if contractor_name.full_name == "Your Name":
        error = {
                'ID': error_entry_id_counter,
                'FileName': file_name,
                "Message":f"These files had 'Your Name' in the name field. Please correct this and reupload.",
                'ReportedProjectNumber':'',
                'WorkOrderEntry':{
                },
                'Employee': '',
                'EmployeeID': contractor_name.full_name,
                'Hours': []
        }
        errors.append(error)
        error_entry_id_counter += 1
        database_service.delete_data_with_fileID(fileID=file_id)
        return errors, name_errors, error_entry_id_counter


    if week_ending and not is_weekending_sunday(week_ending):
            error = {
                    'ID': error_entry_id_counter,
                    'FileName': file_name,
                    "Message":f"WeekEnding : {week_ending} in the file is not a Sunday. Please correct this and reupload.",
                    'ReportedProjectNumber':'',
                    'WorkOrderEntry':{
                    },
                    'Employee': '',
                    'EmployeeID': contractor_name.full_name,
                    'Hours': []
            }
            errors.append(error)
            error_entry_id_counter += 1
            database_service.delete_data_with_fileID(fileID=file_id)
            return errors, name_errors, error_entry_id_counter
    
    no_name_found_error = False
    emps_work_orders = []
    project_numbers = []
    work_order_numbers = []

    ### --------------------------------------------------------- ###
    
                    # Begin Preliminary Checks #
        # Find matching employee based off name in timesheet #

    ### --------------------------------------------------------- ###

    # Check the name in the timesheet with the names in active work orders.
    best_match_name, score = process.extractOne(contractor_name.full_name, names_to_match_against)

    # If there's no direct match between the timesheet and work orders
    if score < 90:
        name_matching_enabled = os.getenv('ENABLE_NAME_MATCHING', 'false')

        # if the name matching is enabled - previous names
        if name_matching_enabled == 'true':

            # Check the DB if name exists in view.
            query = """
                SELECT TOP (1000) [FullName]
                    ,[EmpID]
                    ,[TS_Name]
                    ,[NameCount]
                FROM [dbo].[View_NameSelectionCounts]
                WHERE TS_NAME = ?
                ORDER by NameCount
            """
            results = database_service.execute_query(query, contractor_name.full_name)
            if results:
                best_match_name = results[0]["FullName"]
                score = 100

        # if no results for the name matching, or name matching is off
        if score < 90:
            # create a name error element in the name_errors array
            ## We'll add the hours to this element later. 
            ### That's why we set no_name_found_error to true
            no_name_found_error, name_error = get_name_error(all_active_work_order_entries,
                                                            contractor_name,
                                                            names_to_match_against,
                                                            name_errors,
                                                            error_entry_id_counter,
                                                            file_name)
            if name_error:
                name_errors.append(name_error)
                error_entry_id_counter += 1

    # If name matching is found, or there's a direct match, then get the associated work orders based on the found name.
    if score > 89:
        for wo_entry in all_active_work_order_entries:
            if wo_entry["FullName"] == best_match_name:
                emps_work_orders.append(wo_entry)
                
                curr_project_number = wo_entry['ProjectNumber'].strip()
                project_numbers.append(curr_project_number)

                curr_work_order_number = wo_entry["WorkOrderNumber"].strip()
                work_order_numbers.append(curr_work_order_number)

        curr_employee_id = emps_work_orders[0]['EmployeeID']

    ### --------------------------------------------------------- ###

                    # End Preliminary Check #

    ### --------------------------------------------------------- ###


    ### --------------------------------------------------------- ###

        ### Begin parsing the worksheet, and get the hours. ###
            
    ### --------------------------------------------------------- ###
    try:
        # For each row in the timesheet starting at row 10, until row 20
        for row in range(row_start, row_end):
            # every row resets to full week
            week_ending_subtract = 7
            
            task_id = None
            task_id_error = False
            unknown_travel = False
            project_number_mismatch = False

            # In each row, grab the JobDesc and the OpCode to check tasks / type of working performed.
            job_desc = sheet.cell(row=row, column=job_desc_col).value
            op_code = sheet.cell(row=row, column=op_code_col).value
            contains_travel = "travel" in (job_desc or "").lower() or "travel" in (op_code or "").lower()

            # If the row is a travel row, check if it has the arrive / depart information.
            if  contains_travel:                
                contains_arriv = ("arriv" in (job_desc or "").lower() or "arriv" in (op_code or "").lower())
                contains_depart = ("depart" in (job_desc or "").lower() or "depart" in (op_code or "").lower())

                if (not contains_arriv) and (not contains_depart): # if both false
                    # prompt user to select the start travel hours, later.
                    unknown_travel = True

            if not unknown_travel:
                if job_desc:
                    task_id = database_service.find_or_create_task(job_desc)
                elif op_code:
                    task_id = database_service.find_or_create_task(op_code)
                else:
                    try:
                        for col_idx in range(day_start_col, day_start_col + 7):
                            cell_value = sheet.cell(row=row, column=col_idx).value
                            if cell_value is not None and cell_value.replace(' ','') != '':  # Check if there's a non-empty value
                                hours = float(cell_value)
                                raise Exception("Value found without task.")
                        continue
                    # if value found then throw an error. If not value found, then skip the row and continue.
                    except:
                        error = {
                            'ID':error_entry_id_counter,
                            'FileName': file_name,
                            "Message":f"The sheet: '{sheet.title}' from the '{file_name}' file, doesn't contain a task for one of the rows. Please fix by adding Travel or other task to the op-code or job description column in the file and reupload.",
                            'ReportedProjectNumber':'',
                            'WorkOrderEntry':{
                            },
                            'Employee': contractor_name.full_name,
                            'EmployeeID':'',
                            'Hours': []
                        }                
                        for error_entry in name_errors:
                            if error_entry['OriginalName'] == contractor_name.full_name:
                                error_entry = None
                        for error_entry in name_errors:
                            if error_entry['Employee'] == contractor_name.full_name:
                                error_entry = None
                        errors.append(error)
                        error_entry_id_counter += 1
                        database_service.delete_data_with_fileID(fileID=file_id)
                        break
                    
            
            # We will hit this next portion whether there is travel information or not.
            ## If we have an unknown travel at this point, then we won't have a task id yet. 

            ## TODO idea - If not name match, then check the reported work order number on the sheet.
            ## TODO cont. - if the work order number is direct match, we can likely infer who it is. 
            ### If there's a name error, then the reported project number becomes obsolete, so we'll skip this.
            if not no_name_found_error:
                reported_project_number = sheet.cell(row=row, column=job_num_col).value
                if reported_project_number:
                    best_match_wo_number, wo_score = process.extractOne(str(reported_project_number), work_order_numbers) # Check which WO it is, if multiple.
                    best_match_project_number, score = process.extractOne(str(reported_project_number), project_numbers) # Check which WO it is, if multiple.

                    # The reported project number / work order number isn't a direct match.
                    ## Later we'll add all their active work orders to the errors array.
                    if score < 90 and wo_score < 90:
                        project_number_mismatch = True
                else:
                    # There's no reported job number.
                    project_number_mismatch = True


            if not project_number_mismatch and not no_name_found_error:
                # Now that we have a single identified contractor, and a single identified project / work order number,
                ## we can get the full data for it
                filtered_work_orders = [wo for wo in emps_work_orders if wo.get("ProjectNumber").strip() == best_match_project_number]
                            
                if filtered_work_orders:
                    project_id = filtered_work_orders[0]["ProjectID"]
                    work_order_id = filtered_work_orders[0]["WorkOrderID"]
                    customer_id = filtered_work_orders[0]["CustomerID"]
                else:
                    filtered_work_orders = [wo for wo in emps_work_orders if wo.get("WorkOrderNumber").strip() == best_match_wo_number]
                    
                    project_id = filtered_work_orders[0]["ProjectID"]
                    work_order_id = filtered_work_orders[0]["WorkOrderID"]
                    customer_id = filtered_work_orders[0]["CustomerID"]

            ## Even if we can't find the correct project / work order number,
            ### and even if we can't find the exact contractor,
            #### we can get the hours from the sheet, and let the user select
            ##### the correct name / wo number later in the frontend.
        
            ### Get the hours now. ###
            for col_idx in range(day_start_col, day_start_col + 7):
            # for each day of the week in the row
                try:
                    week_ending_subtract -= 1
                    curr_date = week_ending - timedelta(days=week_ending_subtract) # cell contains equation, doing it manually. 
                    hr = sheet.cell(row=row, column=col_idx).value
                    if hr:

                        # Check for formulas here. This was taken care of earlier but we'll leave it just in case.
                        # This will raise an exception and not process the hours.
                        if isinstance(hr, str) and "=" in hr:
                            # add error message
                            matching_entry = None
                            if not matching_entry:
                                for error_entry in name_errors:
                                    if error_entry['OriginalName'] == contractor_name.full_name:
                                        matching_entry = error_entry
                                        break  # Stop searching once a match is found
                            if matching_entry:
                                # remove from name error array
                                name_errors.remove(matching_entry)

                            matching_entry = None
                            for error_entry in errors:
                                if error_entry['EmployeeID'] == curr_employee_id or error_entry['EmployeeID'] == contractor_name.full_name:
                                    matching_entry = error_entry
                                    break  # Stop searching once a match is found
                            if matching_entry:
                                # remove from error array
                                errors.remove(matching_entry)

                            error = {
                                'ID': error_entry_id_counter,
                                'FileName': file_name,
                                "Message":f"This file had formulas in cell where actual hours were expected. Please correct this and reupload.",
                                'ReportedProjectNumber':'',
                                'WorkOrderEntry':{
                                },
                                'Employee': '',
                                'EmployeeID': contractor_name.full_name,
                                'Hours': []
                            }
                            errors.append(error)
                            error_entry_id_counter += 1
                            database_service.delete_data_with_fileID(fileID=file_id)
                            raise Exception(f"File {file_name} had formulas in cell where actual hours were expected. Please correct this and reupload.")
                        
                        # Turn hours into float, after formula check.
                        hours = float(hr)


                        # We'll have to check if there's travel here. Like add another key that contains unknown travel.


                        # If there is a work order / project mismatch, add the hours to the matching error dictionary. 
                        if project_number_mismatch:
                            error = get_standard_error(emps_work_orders,
                                            error_entry_id_counter,
                                            file_name,
                                            reported_project_number if isinstance(reported_project_number, str) else 0,
                                            contractor_name,
                                            curr_employee_id,
                                            curr_date,
                                            task_id if isinstance(task_id, int) else None,
                                            hours,
                                            file_id,
                                            errors,
                                            selected_week_ending,
                                            unknown_travel)
                            # if project mismatch, we still need to add the travel unknown info.
                            if error:
                                errors.append(error)
                                error_entry_id_counter += 1

                        # If there's no direct name matches, add the hours to the matching error dictionary. 
                        elif no_name_found_error:
                            matching_entry = None
                            for error_entry in name_errors:
                                if error_entry['OriginalName'] == contractor_name.full_name:
                                    matching_entry = error_entry
                                    break  # Stop searching once a match is found
                            if matching_entry:
                                data = {
                                    'Date':curr_date,
                                    'FileID':file_id,
                                    'Hours':hours,
                                    'TaskID':task_id, # task_id will be none if unknown_travel
                                    "UnknownTravel":unknown_travel # default false
                                }
                                matching_entry["Hours"].append(data)


                        ###### If everything is perfect then insert the hours into the database #####
                        elif unknown_travel:
                            # add or find an error, include unknown_travel
                            error = get_travel_error(emps_work_orders,
                                            error_entry_id_counter,
                                            file_name,
                                            reported_project_number if isinstance(reported_project_number, str) else 0,
                                            contractor_name,
                                            curr_employee_id,
                                            curr_date,
                                            task_id if isinstance(task_id, int) else None,
                                            hours,
                                            file_id,
                                            errors,
                                            unknown_travel)
                            if error:
                                errors.append(error)
                                error_entry_id_counter += 1

                        # TODO: Need to handle if unknown_travel is true.
                        else:

                            filtered_work_orders = [wo for wo in emps_work_orders if wo.get("ProjectNumber").strip() == best_match_project_number]
                            
                            if filtered_work_orders:
                                project_id = filtered_work_orders[0]["ProjectID"]
                                work_order_id = filtered_work_orders[0]["WorkOrderID"]
                                customer_id = filtered_work_orders[0]["CustomerID"]
                            else:
                                filtered_work_orders = [wo for wo in emps_work_orders if wo.get("WorkOrderNumber").strip() == best_match_wo_number]
                                
                                project_id = filtered_work_orders[0]["ProjectID"]
                                work_order_id = filtered_work_orders[0]["WorkOrderID"]
                                customer_id = filtered_work_orders[0]["CustomerID"]

                            
                            database_service.insert_hours_internal(employee_id=curr_employee_id,
                                                                    date=curr_date,
                                                                    employee_reported_hours=hours,
                                                                    project_id=project_id,
                                                                    customer_id=customer_id,
                                                                    file_id=file_id,
                                                                    work_order_id=work_order_id,
                                                                    task_id = task_id if isinstance(task_id, int) else None,
                                                                    location_id=None)
                        

                except Exception as e:
                    if "formulas" in e.args[0]:
                        airtanker_app.logger.debug(f"Error with {file_name}: {e}")
                        break
                    continue

    except Exception as e:
        print(f"Error in file at fileId {file_id} with error {e} for {contractor_name.full_name}")
        airtanker_app.logger.debug(f"Error in file at fileId {file_id} with error {e} for {contractor_name.full_name}")

    
    return errors, name_errors, error_entry_id_counter


def is_weekending_sunday(week_ending):
    try:
        # Check if week_ending is a string and try to convert it to datetime
        if isinstance(week_ending, str):
            week_ending = datetime.strptime(week_ending, "%Y-%m-%d")
        elif isinstance(week_ending, datetime):
            pass  # Already a datetime object
        elif not week_ending:
            return False  # None or empty string
        
        # Check if the date is Sunday
        if week_ending.weekday() == 6:  # 6 corresponds to Sunday
            return True
        else:
            return False
    except ValueError:
        return False  # Handle incorrect date formats gracefully


def get_name_error(all_active_work_order_entries,
                   employee_name,
                   names_to_match_against,
                   name_errors,
                   error_entry_id_counter,
                   file_name):
        
        # Put into errors
        name_error = None
        matches = process.extract(employee_name.full_name, names_to_match_against, limit=5)
        for match in matches:
            match_name, score = match
            # Get the hours and get the work orders of each
            emps_work_orders = []
            project_numbers = []
            for wo_entry in all_active_work_order_entries:
                if wo_entry["FullName"] == match_name:
                    emps_work_orders.append(wo_entry)
                    curr_project_number = wo_entry['ProjectNumber'].strip()
                    project_numbers.append(curr_project_number)

            work_order_entry = {}
            employee_id = emps_work_orders[0]
            for entry in emps_work_orders:
                if entry['ProjectNumber'].strip() in work_order_entry:
                    # append it
                    work_order_entry[entry['ProjectNumber'].strip()].append({
                        entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                        })
                else:
                    work_order_entry[entry["ProjectNumber"].strip()] = [{
                        entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                    }]

            if name_error:     
                data = {
                    'EmployeeName':match_name,
                    'EmployeeID':employee_id,
                    'WorkOrders':work_order_entry
                }
                name_error['EmployeeData'].append(data)
            else:
                name_error = {
                    'ID':error_entry_id_counter,
                    'OriginalName':employee_name.full_name,
                    'FileName': file_name,
                    'Message': f"Original Name: <strong>{employee_name.full_name}</strong>. No direct matches in the database. Please select correct employee.",
                    'EmployeeData':[{
                        'EmployeeName':match_name, 'EmployeeID':employee_id, 'WorkOrders':work_order_entry # It's the Project number and work order numbers
                    }],
                    'Hours':[]
                }
        return True, name_error # no_name_found_error, error_not_found_low_score, name_error / new entry         


def get_standard_error(emps_work_orders,
                       error_entry_id_counter,
                       file_name,
                       reported_project_number,
                       employee_name,
                       curr_employee_id,
                       curr_date,
                       task_id,
                       hours,
                       file_id,
                       errors,
                       selected_week_ending,
                       unknown_travel=False):
    
    work_order_entry = {}

    # Find the errored entry by the Project Number
    for entry in emps_work_orders:

        # check if the project number already exists in the work order array above
        if entry['ProjectNumber'].strip() in work_order_entry:
            # if it does exist, append the workOrderID to the project ID for the UI
            work_order_entry[entry['ProjectNumber'].strip()].append({
                entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                })
        else:
            work_order_entry[entry["ProjectNumber"].strip()] = [{
                entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
            }]

    message = f"<strong>{employee_name.full_name}</strong> Reported Project Number: '{reported_project_number}' isn't found in any assigned work orders for selected week ending {selected_week_ending}"
    if reported_project_number == 0:
        message = f"<strong>{employee_name.full_name}</strong> didn't report a Job Number. Select correct one or skip update."

    # Create the error in case we need to append the error
    error = {
        'ID':error_entry_id_counter,
        'FileName': file_name,
        'Message': message,
        'ReportedProjectNumber':reported_project_number,
        'WorkOrderEntry':work_order_entry,
        'Employee': employee_name.full_name,
        'EmployeeID':curr_employee_id,
        'Hours': [
            {
                'Date': curr_date,
                'TaskID': task_id,
                'Hours': hours,
                'FileID':file_id,
                "UnknownTravel":unknown_travel # default false
            },
        ]
    }

    # In the UI we'll check if the data contains travel unknow true. Make the user select
    # arrive / depart for the first unknown travel entry, then update the task ID for it in the database

    # Iterate over the list of errors to find the match
    matching_entry = None
    for error_entry in errors:
        if error_entry['EmployeeID'] == curr_employee_id and error_entry['ReportedProjectNumber'] == reported_project_number:
            matching_entry = error_entry
            break  # Stop searching once a match is found
    if matching_entry:
        # If found, add to the current hours
        hours_match = None
        if matching_entry["Message"] == None:
            matching_entry["Message"] = message
        for hours_entry in matching_entry['Hours']:
            if hours_entry['Date'] == curr_date and hours_entry['TaskID'] == task_id:
                hours_match = hours_entry
                break  # Stop searching once a match is found

        if hours_match:
            hours_match['Hours'] += hours                      
        else:
            matching_entry['Hours'].append({
                'Date':curr_date,
                'TaskID':task_id,
                'Hours':hours,
                'FileID':file_id,
                "UnknownTravel":unknown_travel # default false
            })
        return None
    else:
        return error
    

def get_travel_error(emps_work_orders,
                       error_entry_id_counter,
                       file_name,
                       reported_project_number,
                       employee_name,
                       curr_employee_id,
                       curr_date,
                       task_id,
                       hours,
                       file_id,
                       errors,
                       unknown_travel=False):
    
    work_order_entry = {}

    # Find the errored entry by the Project Number
    for entry in emps_work_orders:

        # check if the project number already exists in the work order array above
        if entry['ProjectNumber'].strip() in work_order_entry:
            # if it does exist, append the workOrderID to the project ID for the UI
            work_order_entry[entry['ProjectNumber'].strip()].append({
                entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                })
        else:
            work_order_entry[entry["ProjectNumber"].strip()] = [{
                entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
            }]

    # Create the error in case we need to append the error
    error = {
        'ID':error_entry_id_counter,
        'FileName': file_name,
        'Message': None,
        'ReportedProjectNumber':reported_project_number,
        'WorkOrderEntry':work_order_entry,
        'Employee': employee_name.full_name,
        'EmployeeID':curr_employee_id,
        'Hours': [
            {
                'Date': curr_date,
                'TaskID': task_id,
                'Hours': hours,
                'FileID':file_id,
                "UnknownTravel":unknown_travel # default false
            },
        ]
    }

    # In the UI we'll check if the data contains travel unknow true. Make the user select
    # arrive / depart for the first unknown travel entry, then update the task ID for it in the database

    # Iterate over the list of errors to find the match
    matching_entry = None
    for error_entry in errors:
        if error_entry['EmployeeID'] == curr_employee_id and error_entry['ReportedProjectNumber'] == reported_project_number:
            matching_entry = error_entry
            break  # Stop searching once a match is found
    if matching_entry:
        # If found, add to the current hours
        hours_match = None
        for hours_entry in matching_entry['Hours']:
            if hours_entry['Date'] == curr_date and hours_entry['TaskID'] == task_id:
                hours_match = hours_entry
                break  # Stop searching once a match is found

        if hours_match:
            hours_match['Hours'] += hours                      
        else:
            matching_entry['Hours'].append({
                'Date':curr_date,
                'TaskID':task_id,
                'Hours':hours,
                'FileID':file_id,
                "UnknownTravel":unknown_travel # default false
            })
        return None
    else:
        return error



def find_sheet(workbook, name):
    sheet_idx = -1
    for sheet_name in workbook.sheetnames:
        sheet_idx += 1
        try:            
            sheet_name_parsed = HumanName(sheet_name)
            if sheet_name_parsed == name:
                break
        except:
            continue

    return sheet_idx


def find_next_empty_row(sheet):
    start_row = 10
    col_check = 1
    empty_row_start = None
    for row in range(start_row, 300): # random 300, could update later
        val = sheet.cell(row=row, column=col_check).value
        if val is None or val == '':
            empty_row_start = row
            return empty_row_start
 