These two variables are used to transmit data between the backend and the client-side. 
They are stored in the session when sent to the frontend. 

They are used in the parsing of the Customer sheets, and the internal sheets.


The name_errors variable is an array of dictionaries. The dictionaries look like this:



The errors variable is an array of dictionaries. An entry look like this:
{
    "Employee": "",
    "EmployeeID": "",
    "FileName": "Atom Tech Ohio Support Hours (1).xlsx",
    "Hours": [],
    "ID": 1,
    "Message": "The sheet: 'Mar 4 - 10' from the 'Atom Tech Ohio Support Hours (1).xlsx' file, has already been processed. Delete the sheet at '/edit-files' to re-parse.",
    "ReportedProjectNumber": "",
    "WorkOrderEntry": {}
}