// Initial call to populate the week-ending dropdown
updateWeekEndingDates();
    
function fetchInitialData() {
    // Make a fetch request to your Flask endpoint for initial data
    var selectedWeekEnding = document.getElementById("weekEndingDropdown").value;
    $('#employeeWeeklyDetailsTable').DataTable().destroy();
    $('#employeeDetailsTable').DataTable().destroy();
    $('#employeeWeeklyDetailsTable').hide();


    fetch(`/api/employee_details_daily?weekEnding=${selectedWeekEnding}`)
    .then(response => response.json())
    .then(dataArray => {
        // Assuming dataTable is already initialized
        let table = $('#employeeDetailsTable').DataTable({
            dom: '<"d-flex justify-content-between"lB>frtip', // Adjust as needed for your layout
            buttons: [
                'excelHtml5'
            ],
            order: [
                [3, "desc"]  // "Error" column is at index 3 (zero-based), sorting in descending order
            ]
        });
        table.clear();

        dataArray.forEach(row => {
            // Add new row with color logic
            // Access the ErrorIndicator column value for the current row
            eHours = row.EmployeeReportedHours == null ? 0 : row.EmployeeReportedHours;
            cHours = row.CustomerReportedHours == null ? 0 : row.CustomerReportedHours;
            
            // Access the ErrorIndicator column value for the current row
            errorIndicatorValue = true;
            var notes = "";
            if (eHours == 0){
                errorIndicatorValue = false;
            }
            if (cHours == 0){
                errorIndicatorValue = false;
            }
            if (errorIndicatorValue == true){
                var difference = row.CustomerReportedHours - row.EmployeeReportedHours;
                if (difference >= -0.03 && difference <= 0.03) {
                    errorIndicatorValue = false;
                    notes += "Reported hours don't match but they meet the tolerance spec <= 0.03";
                }
            }
            let newRow = [
                row.Date,
                row.FirstName,
                row.LastName,
                errorIndicatorValue,
                eHours,
                cHours,
                notes,
                row.WeekStarting,
                row.WeekEnding,
                row.EmployeeID,
                row.TimeSheetEntryID,
                row.TimesheetID,
                row.CustomerName,
            ];

            // Append the new row to the DataTable
            let addedRow = table.row.add(newRow).node();

            // Check if the ErrorIndicator value is 1
            if (errorIndicatorValue == true) {
                // If true, make the row red
                $(addedRow).css({
                    'background-color':'red',
                    'color': 'white'
                });                    
            }
        });



        // Event listener for when a row is clicked
        $('#employeeDetailsTable tbody').on('click', 'tr', function() {
            let rowData = table.row(this).data();
            console.log(rowData);
            if (rowData) {
                let timeSheetEntryID = rowData[10]; // Assuming TimeSheetEntryID is at index 10

                // Fetch additional data based on the clicked row
                fetchAdditionalData(timeSheetEntryID)
                    .then(moreData => {
                        // Populate the additional details table
                        populateAdditionalTable(moreData);

                        // Open the modal
                        $('#detailsModal').modal('show');
                    })
                    .catch(error => {
                        console.error('Error fetching additional data:', error);
                    }
                );
            }
        });

        // Draw the DataTable to apply changes
        table.draw();
    

        // show the datatable
        document.getElementById('employeeDetailsTable').style.display = 'block';

        // show the filter stuff
        $('.employeeDetailsTable_filter').css('display', 'block');
        $('.employeeDetailsTable_length').css('display', 'block');
        $('.employeeDetailsTable_paginate').css('display', 'block');
        $('.employeeDetailsTable_info').css('display', 'block');

        }
        )
        .catch(error => console.error('Error:', error));
}

function fetchWeeklyData() {
    // Make a fetch request to your Flask endpoint for initial data
    var selectedWeekEnding = document.getElementById("weekEndingDropdown").value;
    $('#employeeWeeklyDetailsTable').DataTable().destroy();
    $('#employeeDetailsTable').DataTable().destroy();
    $('#employeeDetailsTable').hide();

    fetch(`/api/employee_details_weekly?weekEnding=${selectedWeekEnding}`)
    .then(response => response.json())
    .then(dataArray => {
        // Assuming dataTable is already initialized
        let table = $('#employeeWeeklyDetailsTable').DataTable({
            dom: '<"d-flex justify-content-between"lB>frtip', // Adjust as needed for your layout
            buttons: [
                'excelHtml5'
            ],
            order: [
                [7, "desc"]  // "Error" column is at index 3 (zero-based), sorting in descending order
            ]
        });
        table.clear();

        dataArray.forEach(row => {
            // Add new row with color logic
            eHours = row.TotalEmployeeReportedHours == null ? 0 : row.TotalEmployeeReportedHours;
            cHours = row.TotalCustomerReportedHours == null ? 0 : row.TotalCustomerReportedHours;
            
            // Access the ErrorIndicator column value for the current row
            errorIndicatorValue = true;
            var notes = "";
            if (eHours == 0){
                errorIndicatorValue = false;
            }
            if (cHours == 0){
                errorIndicatorValue = false;
            }
            if (errorIndicatorValue == true){
                var difference = row.TotalCustomerReportedHours - row.TotalEmployeeReportedHours;
                console.log(difference);
                if (difference > -0.031 && difference < 0.031) {
                    errorIndicatorValue = false;
                    if (difference != 0){
                        notes += "Reported hours don't match but they meet the tolerance spec <= 0.03. \n";
                    }
                }
            }
            if (row.TotalCustomerReportedHours >= 40 || row.EmployeeReportedHours >= 40){
                notes += "Logged 40+ hours this week."
            }
                       
            let newRow = [                            
                row.FirstName,
                row.LastName,                        
                row.WeekStarting,
                row.WeekEnding,
                row.Status,
                eHours,
                cHours,
                errorIndicatorValue,
                notes,
                row.TaskNames,
                row.CustomerNames,
            ];

            // Append the new row to the DataTable
            let addedRow = table.row.add(newRow).node();


            // Check if the ErrorIndicator value is 1
            if (errorIndicatorValue == true) {
                // If true, make the row red
                $(addedRow).css({
                    'background-color':'red',
                    'color': 'white'
                });
            }

        });


        // Draw the DataTable to apply changes
        table.draw();
    

        // show the datatable
        document.getElementById('employeeWeeklyDetailsTable').style.display = 'block';

        // show the filter stuff
        $('.employeeWeeklyDetailsTable_filter').css('display', 'block');
        $('.employeeWeeklyDetailsTable_length').css('display', 'block');
        $('.employeeWeeklyDetailsTable_paginate').css('display', 'block');
        $('.employeeWeeklyDetailsTable_info').css('display', 'block');

        }
        )
        .catch(error => console.error('Error:', error));
}

function fetchAdditionalData(timesheetEntryID) {
    return new Promise((resolve, reject) => {
        fetch(`/api/employee_details_allEntries?timesheetEntryID=${timesheetEntryID}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => resolve(data))
            .catch(error => reject(error));
    });
}

function populateAdditionalTable(data) {
    // Assuming additionalTable is already initialized
    let additionalTable = $('#additionalDetailsTable').DataTable();
    additionalTable.clear();

    data.forEach(row => {
        let newRow = [
            row.Source, 
            row.FirstName,  
            row.LastName,   
            row.Date,   
            row.ReportedHours,  
            row.ProjectNumber,  
            row.TaskName,   
            row.CustomerName,   

        // Add more fields as needed
        ];
        let addedRow = additionalTable.row.add(newRow).node();
        // Access the ErrorIndicator column value for the current row
        var errorIndicatorValue = row.ErrorIndicator;

        // Check if the ErrorIndicator value is 1
        if (errorIndicatorValue == true) {
            // If true, make the row red
            $(addedRow).css({
                    'background-color':'red',
                    'color': 'white'
                });                
            }
    });            
    // Draw the additional details table to apply changes
    additionalTable.draw();
}

// Populate dropdowns
function updateWeekEndingDates() {
    var selectedYear = document.getElementById("yearDropdown").value;
    var weekEndingDropdown = document.getElementById("weekEndingDropdown");
    
    // Clear existing options
    weekEndingDropdown.innerHTML = "";

    for (var weekNumber = 1; weekNumber <= 52; weekNumber++) {
        var sundayDate = new Date(selectedYear, 0, 1 + (weekNumber - 1) * 7);
        while (sundayDate.getDay() !== 0) {
        sundayDate.setDate(sundayDate.getDate() + 1);
        }

        var formattedDate = sundayDate.toISOString().split("T")[0];
        var option = document.createElement("option");
        option.value = formattedDate;
        option.text = "Week " + weekNumber + " (Ending " + formattedDate + ")";
        weekEndingDropdown.appendChild(option);
    }
}   

function handleSelection() {
    var selectedYear = document.getElementById("yearDropdown").value;
    var selectedWeekEnding = document.getElementById("weekEndingDropdown").value;

    // Do something with the selected values
    console.log("Selected Year:", selectedYear);
    console.log("Selected Week Ending:", selectedWeekEnding);
}